<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Birthday Invitation</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Crimson+Pro:wght@200;500&family=Cormorant+Garamond:wght@400;600&family=Euphoria+Script&family=Akronim&display=swap"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Crimson Pro", serif;
        background-color: #f5f5f5;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
      }

      .invitation-container {
        position: relative;
        width: 1240px;
        height: 1748px;
        background-image: url("https://customer-preprod.eventpark.africa/assets/email-templates/birthday-background.png");
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        max-width: 100%;
        max-height: 100vh;
        object-fit: contain;
      }

      .text-element {
        position: absolute;
        color: #ffe29b;
      }

      .invitation-text {
        font-family: "Crimson Pro", serif;
        font-weight: 500;
        font-size: 32px;
        line-height: 1.111328125em;
        letter-spacing: 5%;
        text-align: center;
        left: 251px;
        top: 156px;
        width: 739px;
        height: 36px;
        transform-origin: center center;
      }

      .age-number {
        font-family: "Akronim", cursive;
        font-weight: 400;
        font-size: 440px;
        line-height: 0.8181818181818182em;
        letter-spacing: 5%;
        text-align: left;
        left: 355px;
        top: 264px;
        width: 356px;
        height: 360px;
        transform-origin: left center;
      }

      .age-suffix {
        font-family: "Akronim", cursive;
        font-weight: 400;
        font-size: 160px;
        line-height: 1.1420000076293946em;
        letter-spacing: 5%;
        text-align: left;
        left: 723px;
        top: 244px;
        width: 118px;
        height: 183px;
        transform-origin: left center;
      }

      .birthday-text {
        font-family: "Euphoria Script", cursive;
        font-weight: 400;
        font-size: 160px;
        line-height: 0.75em;
        letter-spacing: 5%;
        text-align: left;
        left: 346px;
        top: 616px;
        width: 548px;
        height: 120px;
        transform-origin: left center;
      }

      .milestone-text {
        font-family: "Cormorant Garamond", serif;
        font-weight: 600;
        font-size: 36px;
        line-height: 1.211000018649631em;
        letter-spacing: 14.000000000000002%;
        text-align: center;
        left: 185px;
        top: 828px;
        width: 870px;
        height: 44px;
        transform-origin: center center;
      }

      .event-month {
        font-family: "Crimson Pro", serif;
        font-weight: 200;
        font-size: 54px;
        line-height: 1.111328125em;
        letter-spacing: 5%;
        text-align: center;
        left: 346px;
        top: 1008px;
        width: 98px;
        height: 60px;
      }

      .event-day {
        font-family: "Akronim", cursive;
        font-weight: 400;
        font-size: 160px;
        line-height: 1em;
        letter-spacing: 5%;
        text-align: left;
        left: 561px;
        top: 966px;
        width: 118px;
        height: 160px;
      }

      .event-time {
        font-family: "Crimson Pro", serif;
        font-weight: 200;
        font-size: 54px;
        line-height: 1.111328125em;
        letter-spacing: 5%;
        text-align: center;
        left: 787px;
        top: 1008px;
        width: 102px;
        height: 60px;
      }

      .venue-address {
        font-family: "Cormorant Garamond", serif;
        font-weight: 400;
        font-size: 36px;
        line-height: 1.211000018649631em;
        letter-spacing: 14.000000000000002%;
        text-transform: uppercase;
        text-align: center;
        left: 125px;
        top: 1201px;
        width: 991px;
        height: 44px;
      }

      .rsvp-text {
        font-family: "Crimson Pro", serif;
        font-weight: 500;
        font-size: 34px;
        line-height: 1.111328125em;
        letter-spacing: 5%;
        text-align: center;
        left: 250px;
        top: 1312px;
        width: 739px;
        height: 76px;
      }

      /* Dynamic scaling for age number */
      .age-number[data-length="1"] {
        transform: scale(1);
      }
      .age-number[data-length="2"] {
        transform: scale(0.9);
      }
      .age-number[data-length="3"] {
        transform: scale(0.8);
      }
      .age-number[data-length="4"] {
        transform: scale(0.7);
      }
      .age-number[data-length="5"] {
        transform: scale(0.6);
      }

      /* Dynamic scaling for birthday text */
      .birthday-text[data-length="8"] {
        transform: scale(0.95);
      }
      .birthday-text[data-length="9"] {
        transform: scale(0.9);
      }
      .birthday-text[data-length="10"] {
        transform: scale(0.85);
      }
      .birthday-text[data-length="11"] {
        transform: scale(0.8);
      }
      .birthday-text[data-length="12"] {
        transform: scale(0.75);
      }
      .birthday-text[data-length="13"] {
        transform: scale(0.7);
      }
      .birthday-text[data-length="14"] {
        transform: scale(0.65);
      }
      .birthday-text[data-length="15"] {
        transform: scale(0.6);
      }
      .birthday-text[data-length="16"] {
        transform: scale(0.55);
      }
      .birthday-text[data-length="17"] {
        transform: scale(0.5);
      }
      .birthday-text[data-length="18"],
      .birthday-text[data-length="19"],
      .birthday-text[data-length="20"] {
        transform: scale(0.45);
      }

      @media screen and (max-width: 1280px) {
        .invitation-container {
          transform: scale(0.8);
          transform-origin: center center;
        }
      }

      @media screen and (max-width: 1024px) {
        .invitation-container {
          transform: scale(0.6);
          transform-origin: center center;
        }
      }

      @media screen and (max-width: 768px) {
        .invitation-container {
          transform: scale(0.4);
          transform-origin: center center;
        }
      }
    </style>
  </head>
  <body>
    <div class="invitation-container">
      <div class="text-element invitation-text">
        Please join us in celebrating {{{celebrant_name}}}'s
      </div>

      <div class="text-element age-number">{{{age_number}}}</div>

      <div class="text-element age-suffix">{{{age_suffix}}}</div>

      <div class="text-element birthday-text">Birthday!</div>

      <div class="text-element milestone-text">
        IT'S A MILESTONE WORTH CELEBRATING!
      </div>

      <div class="text-element event-month">{{{event_month}}}</div>

      <div class="text-element event-day">{{{event_day}}}</div>

      <div class="text-element event-time">{{{event_time}}}</div>

      <div class="text-element venue-address">{{{venue_address}}}</div>

      <div class="text-element rsvp-text">{{{rsvp_text}}}</div>
    </div>

    <!-- <script>
      function calculateTextLengths() {
        const ageNumber = document.querySelector(".age-number");
        const birthdayText = document.querySelector(".birthday-text");

        if (ageNumber) {
          const textContent = ageNumber.textContent || ageNumber.innerText;
          const cleanText = textContent.replace(/\s+/g, " ").trim();
          const totalLength = cleanText.length;
          ageNumber.setAttribute("data-length", totalLength.toString());
          console.log(`Age number length: ${totalLength} characters`);
        }

        if (birthdayText) {
          const textContent =
            birthdayText.textContent || birthdayText.innerText;
          const cleanText = textContent.replace(/\s+/g, " ").trim();
          const totalLength = cleanText.length;
          birthdayText.setAttribute("data-length", totalLength.toString());
          console.log(`Birthday text length: ${totalLength} characters`);
        }
      }

      // Run the calculation when the page loads
      document.addEventListener("DOMContentLoaded", calculateTextLengths);

      // Also run it after a short delay to ensure all content is loaded
      setTimeout(calculateTextLengths, 100);
    </script> -->
  </body>
</html>
