<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Grand Opening Invitation</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Inter:wght@500;700&family=Kranky&display=swap"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Inter", sans-serif;
        background-color: #f5f5f5;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
      }

      .invitation-container {
        position: relative;
        width: 1240px;
        height: 1748px;
        background-image: url("https://customer-preprod.eventpark.africa/assets/email-templates/grand-opening-background.png");
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        max-width: 100%;
        max-height: 100vh;
        object-fit: contain;
      }

      .text-element {
        position: absolute;
      }

      .company-name {
        font-family: "Inter", sans-serif;
        font-weight: 500;
        font-size: 24px;
        line-height: 1.3333333333333333em;
        text-align: center;
        color: #40322c;
        left: 514px;
        top: 252px;
        width: 213px;
        height: 32px;
      }

      .grand-text {
        font-family: "Bebas Neue", sans-serif;
        font-weight: 400;
        font-size: 160px;
        line-height: 1em;
        letter-spacing: 4%;
        text-align: center;
        color: #8f5d46;
        left: 445px;
        top: 485px;
        width: 351px;
        height: 160px;
        transform-origin: center center;
      }

      .opening-text {
        font-family: "Kranky", cursive;
        font-weight: 400;
        font-size: 240px;
        line-height: 1em;
        letter-spacing: 4%;
        text-align: center;
        color: #8f5d46;
        left: 142px;
        top: 613px;
        width: 956px;
        height: 240px;
        transform-origin: center center;
      }

      .event-date {
        font-family: "Inter", sans-serif;
        font-weight: 700;
        font-size: 44px;
        line-height: 1em;
        text-align: center;
        color: #40322c;
        left: 442px;
        top: 1067px;
        width: 357px;
        height: 44px;
      }

      .venue-address {
        font-family: "Inter", sans-serif;
        font-weight: 500;
        font-size: 24px;
        line-height: 1.3333333333333333em;
        text-align: center;
        color: #40322c;
        left: 387px;
        top: 1157px;
        width: 467px;
        height: 64px;
      }

      .social-media {
        font-family: "Inter", sans-serif;
        font-weight: 500;
        font-size: 24px;
        line-height: 1.3333333333333333em;
        text-align: center;
        color: #40322c;
        left: 455px;
        top: 1390px;
        width: 331px;
        height: 32px;
      }

      /* Decorative rectangles */
      .decorative-rectangle-1 {
        position: absolute;
        left: 268px;
        top: 613px;
        width: 138px;
        height: 340px;
        background-color: #fffef5;
      }

      .decorative-rectangle-2 {
        position: absolute;
        left: 833px;
        top: 674px;
        width: 138px;
        height: 180px;
        background-color: #fffef5;
      }

      /* Dynamic scaling for grand text */
      .grand-text[data-length="4"] {
        transform: scale(0.95);
      }
      .grand-text[data-length="5"] {
        transform: scale(0.9);
      }
      .grand-text[data-length="6"] {
        transform: scale(0.85);
      }
      .grand-text[data-length="7"] {
        transform: scale(0.8);
      }
      .grand-text[data-length="8"] {
        transform: scale(0.75);
      }
      .grand-text[data-length="9"] {
        transform: scale(0.7);
      }
      .grand-text[data-length="10"] {
        transform: scale(0.65);
      }
      .grand-text[data-length="11"] {
        transform: scale(0.6);
      }
      .grand-text[data-length="12"] {
        transform: scale(0.55);
      }
      .grand-text[data-length="13"] {
        transform: scale(0.5);
      }
      .grand-text[data-length="14"],
      .grand-text[data-length="15"],
      .grand-text[data-length="16"] {
        transform: scale(0.45);
      }

      /* Dynamic scaling for opening text */
      .opening-text[data-length="6"] {
        transform: scale(0.95);
      }
      .opening-text[data-length="7"] {
        transform: scale(0.9);
      }
      .opening-text[data-length="8"] {
        transform: scale(0.85);
      }
      .opening-text[data-length="9"] {
        transform: scale(0.8);
      }
      .opening-text[data-length="10"] {
        transform: scale(0.75);
      }
      .opening-text[data-length="11"] {
        transform: scale(0.7);
      }
      .opening-text[data-length="12"] {
        transform: scale(0.65);
      }
      .opening-text[data-length="13"] {
        transform: scale(0.6);
      }
      .opening-text[data-length="14"] {
        transform: scale(0.55);
      }
      .opening-text[data-length="15"] {
        transform: scale(0.5);
      }
      .opening-text[data-length="16"],
      .opening-text[data-length="17"],
      .opening-text[data-length="18"] {
        transform: scale(0.45);
      }

      @media screen and (max-width: 1280px) {
        .invitation-container {
          transform: scale(0.8);
          transform-origin: center center;
        }
      }

      @media screen and (max-width: 1024px) {
        .invitation-container {
          transform: scale(0.6);
          transform-origin: center center;
        }
      }

      @media screen and (max-width: 768px) {
        .invitation-container {
          transform: scale(0.4);
          transform-origin: center center;
        }
      }
    </style>
  </head>
  <body>
    <div class="invitation-container">
      <!-- Decorative rectangles -->
      <div class="decorative-rectangle-1"></div>
      <div class="decorative-rectangle-2"></div>

      <div class="text-element company-name">{{{company_name}}}</div>

      <div class="text-element grand-text">GRAND</div>

      <div class="text-element opening-text">Opening</div>

      <div class="text-element event-date">{{{event_date}}}</div>

      <div class="text-element venue-address">{{{venue_address}}}</div>

      <div class="text-element social-media">{{{social_media}}}</div>
    </div>

    <script>
      //   function calculateTextLengths() {
      //     const grandText = document.querySelector(".grand-text");
      //     const openingText = document.querySelector(".opening-text");

      //     if (grandText) {
      //       const textContent = grandText.textContent || grandText.innerText;
      //       const cleanText = textContent.replace(/\s+/g, " ").trim();
      //       const totalLength = cleanText.length;
      //       grandText.setAttribute("data-length", totalLength.toString());
      //       console.log(`Grand text length: ${totalLength} characters`);
      //     }

      //     if (openingText) {
      //       const textContent = openingText.textContent || openingText.innerText;
      //       const cleanText = textContent.replace(/\s+/g, " ").trim();
      //       const totalLength = cleanText.length;
      //       openingText.setAttribute("data-length", totalLength.toString());
      //       console.log(`Opening text length: ${totalLength} characters`);
      //     }
      //   }

      //   // Run the calculation when the page loads
      //   document.addEventListener("DOMContentLoaded", calculateTextLengths);

      //   // Also run it after a short delay to ensure all content is loaded
      //   setTimeout(calculateTextLengths, 100);
      //
    </script>
  </body>
</html>
