/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { CloseCircle, ArrowDown2 } from "iconsax-react";
import { useMutation } from "@tanstack/react-query";
import { AddressAutocomplete } from "../inputs/address-autocomplete";
import { FormInput } from "../inputs/form-input/form-input";
import { GiftRegistryServices } from "../../lib/services/gift-registry";
import { events } from "../../lib/services/events";
import { useEventStore } from "../../lib/store/event";
import { useEventManagement } from "../../lib/hooks/useEventManagement";
import { toast } from "react-toastify";

interface EditDeliveryDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  isEditing?: boolean;
}

export const EditDeliveryDetailsModal: React.FC<
  EditDeliveryDetailsModalProps
> = ({ isOpen, onClose, isEditing = true }) => {
  const { selectedEvent } = useEventStore();
  const { updateEventOptimistically } = useEventManagement();

  const [address, setAddress] = useState("");
  const [eventLocationPlaceId, setEventLocationPlaceId] = useState<string>("");
  const [mobileNumber, setMobileNumber] = useState("");
  const [shareConsent, setShareConsent] = useState(false);
  const [phoneError, setPhoneError] = useState("");

  // Initialize form with existing data
  useEffect(() => {
    if (selectedEvent && isOpen) {
      setAddress(selectedEvent.delivery_address || "");
      // Extract phone number without country code if it exists
      const phone = selectedEvent.delivery_phone_number;
      if (phone && phone.startsWith("+234")) {
        setMobileNumber(phone.substring(4));
      } else if (phone) {
        setMobileNumber(phone);
      }
      // Reset placeId when modal opens - user will need to reselect address
      setEventLocationPlaceId("");
    }
  }, [selectedEvent, isOpen]);

  const validatePhone = (phone: string) => {
    return phone.length === 10;
  };

  const updateEventMutation = useMutation({
    mutationFn: (payload: { delivery_address_id: string }) => {
      if (!selectedEvent?.id) throw new Error("No event selected");
      return events.updateEventDetails(selectedEvent.id, payload);
    },
    onSuccess: async (response) => {
      const updatedEvent = response.data;
      updateEventOptimistically(updatedEvent);
      toast.success("Delivery details updated successfully");
      onClose();
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || error?.message);
    },
  });

  const createAddressMutation = useMutation({
    mutationFn: (payload: {
      location_place_id: string;
      delivery_phone_number: string;
      is_consented: boolean;
    }) => GiftRegistryServices.createAddress(payload),
    onSuccess: async (response) => {
      const addressId = response?.data?.id;
      if (addressId) {
        updateEventMutation.mutate({ delivery_address_id: addressId });
      } else {
        toast.error("Failed to get address ID from response");
      }
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || error?.message);
    },
  });

  const handleSaveChanges = () => {
    if (!mobileNumber) {
      setPhoneError("Mobile number is required");
      return;
    }

    if (!validatePhone(mobileNumber)) {
      setPhoneError("Please enter a valid 10-digit mobile number");
      return;
    }

    if (!address.trim()) {
      toast.error("Please enter a delivery address");
      return;
    }

    if (!eventLocationPlaceId.trim()) {
      toast.error("Please select a valid address from the dropdown");
      return;
    }

    const fullPhoneNumber = `+234${mobileNumber}`;

    createAddressMutation.mutate({
      location_place_id: eventLocationPlaceId,
      delivery_phone_number: fullPhoneNumber,
      is_consented: shareConsent,
    });
  };

  const handleClose = () => {
    setPhoneError("");
    onClose();
  };

  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  if (!isOpen) return null;

  const isLoading =
    createAddressMutation.isPending || updateEventMutation.isPending;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center font-rethink">
        <div
          className="absolute inset-0 bg-black/20 bg-opacity-50"
          onClick={handleClose}
        />
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.2 }}
          className="bg-white relative rounded-3xl w-full max-w-full sm:max-w-[658px] mx-4 px-4 sm:px-20 py-6 max-h-[90vh] overflow-y-auto"
        >
          {/* Header */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 transition-colors"
          >
            <CloseCircle size="33" color="#4D55F2" variant="Bulk" />
          </button>
          <div className="flex items-center justify-between p-6 pb-4">
            <h2 className="text-[20px] sm:text-[28px] font-semibold text-black">
              {isEditing ? "Edit Delivery Details" : "Add Delivery Details"}
            </h2>
          </div>

          {/* Content */}
          <div className="px-2 sm:px-6 pb-2 sm:pb-6">
            {/* Delivery Address */}
            <div className="mb-2 sm:mb-6">
              <label className="block mb-2 text-sm font-medium text-[#414651]">
                Delivery Address
              </label>
              <div className="relative">
                <AddressAutocomplete
                  value={address}
                  onChange={(value, placeId) => {
                    setAddress(value);
                    if (placeId) {
                      setEventLocationPlaceId(placeId);
                    }
                  }}
                  placeholder="6, Saki Close, osapa London, Lekki Lagos Nigeria"
                  className="pl-4 sm:pl-12 border-grey-200 rounded-[26px]"
                  disabled={isLoading}
                />
                {address && !eventLocationPlaceId && (
                  <p className="text-xs text-orange-600 mt-1 ml-1">
                    Please select an address from the dropdown to ensure
                    accurate delivery
                  </p>
                )}
              </div>
            </div>

            <FormInput
              label="Delivery Phone Number"
              placeholder="Enter 10-digit mobile number"
              type="tel"
              value={mobileNumber}
              onChange={(e) => {
                const value = e.target.value.replace(/\D/g, "").slice(0, 10);
                setMobileNumber(value);
                if (phoneError && validatePhone(value)) {
                  setPhoneError("");
                }
              }}
              error={phoneError}
              leftAddon={
                <div className="flex items-center ">
                  <span className="text-grey-500  font-semibold mr-1 italic text-base">
                    +234
                  </span>
                  <ArrowDown2 size={16} color="#717680" />
                </div>
              }
            />

            <div className="mb-8">
              <label className="flex items-start gap-3 cursor-pointer">
                <div className="relative mt-1">
                  <input
                    type="checkbox"
                    checked={shareConsent}
                    onChange={(e) => setShareConsent(e.target.checked)}
                    className="sr-only"
                    disabled={isLoading}
                  />
                  <div
                    className={`w-11 h-6 rounded-full transition-colors duration-200 ${
                      shareConsent ? "bg-primary" : "bg-gray-300"
                    } ${isLoading ? "opacity-50" : ""}`}
                  >
                    <div
                      className={`w-5.5 h-5.5 bg-white rounded-full shadow-md transform transition-transform duration-200 ${
                        shareConsent ? "translate-x-5" : "translate-x-0.5"
                      } mt-0.5`}
                    ></div>
                  </div>
                </div>
                <span className="text-sm text-[#414651] font-medium italic leading-relaxed">
                  I consent to sharing my address and phone number with friends
                  and family for gift delivery
                </span>
              </label>
            </div>

            <button
              onClick={handleSaveChanges}
              disabled={isLoading}
              className="w-full bg-primary text-white font-semibold h-[44px] rounded-full hover:bg-primary/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? "Saving Changes..." : "Save Changes"}
            </button>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};
