import { twMerge } from "tailwind-merge";
import { Icon } from "../icons/icon";

interface Props extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  isLoading?: boolean;
  variant: "primary" | "secondary" | "neutral";
}

export function Button({ isLoading, children, className, ...props }: Props) {
  return (
    <button
      {...props}
      className={twMerge(
        "flex justify-center items-center disabled:opacity-40 disabled:cursor-not-allowed cursor-pointer",
        props.variant === "primary" &&
          "w-full bg-primary py-3.5 px-7 rounded-full hover:bg-primary/90 text-white ",
        props.variant === "secondary" &&
          "w-full bg-white py-2.5 px-4 rounded-full hover:bg-white/70 text-white border border-grey-600",
        props.variant === "neutral" && "",
        className
      )}
    >
      {isLoading ? <Icon name='loading' />: children}
    </button>
  );
}
