import { Arrow<PERSON>ircleLeft2, Tag2, TickCircle } from "iconsax-react";
import { But<PERSON> } from "../../../../components/button/onboardingButton";
import stackMoney from "../../../../assets/images/stack-money2.svg";
import { StepProgress } from "../../../../components/step-progress/step-progress";
import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { GifterDetails } from "./gifter-details";
import { CashGiftAmount } from "./cash-gift-amount";
import { PaymentModal } from "./payment-modal";
import { SuccessPayment } from "./success";
import { SendCashGift } from "./SendCashGift";
import {
  GuestGiftsAPI,
  CashGiftWithMetrics,
} from "../../../../lib/apis/guestGiftsApi";
import { toast } from "react-toastify";

export const CashGiftReservation = () => {
  interface GuestData {
    email: string;
    first_name: string;
    last_name: string;
    phone_number: string;
  }
  const { eventId, cashId, crowdfunding } = useParams<{
    eventId: string;
    cashId: string;
    crowdfunding?: string;
  }>();
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([1]);
  const [showTooltip, setShowTooltip] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [cashGift, setCashGift] = useState<CashGiftWithMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [guestData, setGuestData] = useState<GuestData | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [reservationId, setReservationId] = useState<string | null>(null);
  const [eventDetails, setEventDetails] = useState<{
    gift_registry_title: string;
    host_first_name: string;
    host_last_name: string;
    id: string;
  } | null>(null);

  console.log(accessToken, "djkdjjd");

  const isCrowdfundingEnabled =
    crowdfunding === "crowdfunding" || cashGift?.is_crowd_gift;

  const steps = [
    { id: 1, name: "Gifter's details" },
    { id: 2, name: "Gift Reservation" },
  ];

  // Fetch cash gift data
  useEffect(() => {
    const fetchCashGift = async () => {
      if (!eventId || !cashId) return;

      setLoading(true);
      try {
        // Fetch both single cash gift with metrics and event details
        const [cashGiftResponse, eventResponse] = await Promise.all([
          GuestGiftsAPI.getCashGift(cashId),
          GuestGiftsAPI.getEventDetails(eventId),
        ]);

        setCashGift(cashGiftResponse);
        setEventDetails(eventResponse);
      } catch (err) {
        const errorMsg =
          typeof err === "object" && err && "message" in err
            ? (err as { message?: string }).message ?? "Failed to fetch data"
            : "Failed to fetch data";
        setError(errorMsg);
        toast.error(errorMsg);
      } finally {
        setLoading(false);
      }
    };

    fetchCashGift();
  }, [eventId, cashId]);

  const handleStepChange = (stepId: number) => {
    if (
      completedSteps.includes(stepId) ||
      stepId === activeStep ||
      stepId === activeStep - 1
    ) {
      setActiveStep(stepId);
    }
  };

  if (showSuccess) {
    return <SuccessPayment />;
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#4D55F2] mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading cash gift...</p>
        </div>
      </div>
    );
  }

  if (error || !cashGift) {
    return (
      <div className="min-h-screen bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-500 text-lg font-medium mb-4">
            {error || "Cash gift not found"}
          </p>
          <Button
            variant="primary"
            size="md"
            onClick={() => navigate(`/guest/events/${eventId}/gifts`)}
          >
            Back to Gifts
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] pb-36">
      <header className="w-full h-20 border-b border-[#f5f6fe]">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="flex items-center h-20">
            <div
              onClick={() => {
                window.location.href = `${window.location.origin}/login`;
              }}
              className="flex items-center cursor-pointer gap-1"
            >
              <img className="w-6 h-6" alt="Vector" src="/vector.svg" />
              <div className="font-bold text-xl text-center tracking-[-0.40px] leading-5 whitespace-nowrap">
                <span className="text-[#000073] tracking-[-0.08px]">
                  EventPark Africa
                </span>
                <span className="text-[#ff6630] tracking-[-0.08px]">.</span>
              </div>
            </div>
          </div>
        </div>
      </header>
      <div className="pt-14 mx-auto md:max-w-[750px] w-full px-4 md:px-0">
        <Button
          variant="primary"
          size="md"
          className="text-primary-650 bg-white hover:bg-gray-50"
          iconLeft={
            <ArrowCircleLeft2 size="20" color="#4D55F2" variant="Bulk" />
          }
          onClick={() => navigate(`/guest/events/${eventId}/gifts`)}
        >
          Back
        </Button>

        <div className="mt-5 flex flex-col md:flex-row bg-white rounded-2xl shadow-[0px_12px_120px_0px_rgba(95,95,95,0.06)]">
          {/* Left Section */}
          <div className="w-full md:w-[282px] border-b md:border-b-0 md:border-r border-[#F0F0F0]">
            <div className="mt-4 mx-[18px] p-4 bg-[#F5F6FE] rounded-[14px]">
              <div className="relative w-[213px] h-[213px] mx-auto">
                <img
                  src={stackMoney}
                  alt="cash-gift"
                  className="w-full h-full object-contain"
                />
              </div>
            </div>

            <h3 className="mx-[18px] mt-6 text-[22px] font-normal text-[#090909] leading-[1.302] tracking-[-0.02em]">
              {cashGift.description}
            </h3>

            <div className="mx-4 mt-4">
              <div className="inline-flex items-center gap-1 px-2.5 py-1.5 bg-[#F4F3FF] rounded-2xl">
                <Tag2 size={12} variant="Bulk" color="#5856D6" />
                <span className="text-sm font-medium text-[#5925DC] leading-[1.429] tracking-[-0.01em]">
                  ₦{parseFloat(cashGift.amount).toLocaleString()}
                </span>
              </div>
            </div>

            {/* Crowd Gifting Section */}
            {isCrowdfundingEnabled && (
              <div className="mx-4 mt-[68px] relative">
                <button
                  className="flex items-center gap-1.5 focus:outline-none"
                  onMouseEnter={() => setShowTooltip(true)}
                  onMouseLeave={() => setShowTooltip(false)}
                >
                  <TickCircle size="20" variant="Bulk" color="#3CC35C" />
                  <span className="text-sm font-medium text-[#007AFF]">
                    Crowd gifting enabled
                  </span>
                </button>

                {showTooltip && (
                  <div className="absolute left-0 top-[32px] w-[254px] bg-[#9499F7] text-white p-3 rounded-lg shadow-[0px_12px_120px_0px_rgba(95,95,95,0.06)]">
                    <p className="text-xs leading-[1.667] tracking-[-0.03em]">
                      Contribute to this cause alongside others to make their
                      dream come true.
                    </p>
                  </div>
                )}

                {(() => {
                  const totalAmount = parseFloat(cashGift.amount);
                  const totalContributions = parseFloat(
                    cashGift.total_contributions
                  );
                  const progressPercentage =
                    totalAmount > 0
                      ? Math.min((totalContributions / totalAmount) * 100, 100)
                      : 0;

                  return (
                    <>
                      <div className="mt-4 w-[226px] h-2 bg-[#FEF5F1] rounded-lg overflow-hidden">
                        <div
                          className="h-full bg-[#4D55F2] rounded-lg transition-all duration-300"
                          style={{ width: `${progressPercentage}%` }}
                        />
                      </div>
                      <p className="mt-2 text-sm text-[#666666] leading-[1.302] tracking-[-0.02em]">
                        ₦
                        {parseFloat(
                          cashGift.total_contributions
                        ).toLocaleString()}{" "}
                        contributed by {cashGift.total_contributors}{" "}
                        {cashGift.total_contributors === 1
                          ? "person"
                          : "people"}
                      </p>
                    </>
                  );
                })()}
              </div>
            )}

            <div className="mt-[28px] px-5 py-[19px] border-t border-[#F5F5F5]">
              <p className="text-sm text-[#8E8E93] leading-[1.302] tracking-[-0.02em]">
                Total Amount
              </p>
              <p className="mt-1 text-base font-medium text-[#090909] leading-[1.302] tracking-[-0.04em]">
                ₦{parseFloat(cashGift?.amount).toLocaleString()}
              </p>
            </div>
          </div>

          {/* Right Section */}
          <div className="flex-1 p-4 md:p-6 md:pl-0">
            <div className="md:p-6">
              <h2 className="text-[18px] sm:text-[22px] font-medium text-[#090909] leading-[1.302] tracking-[-0.04em]">
                Send a Cash gift to{" "}
                {loading
                  ? "..."
                  : eventDetails?.host_first_name
                  ? `${eventDetails.host_first_name} ${eventDetails.host_last_name}`
                  : "the host"}
              </h2>
              <p className="text-base text-[#8E8E93] leading-[1.302] tracking-[-0.02em] mt-1">
                Give something they'll cherish.
              </p>
            </div>

            <StepProgress
              steps={steps}
              activeStep={activeStep}
              completedSteps={completedSteps}
              onStepChange={handleStepChange}
            />

            {activeStep === 1 && (
              <GifterDetails
                onContinue={(guestRegistrationData, token) => {
                  setGuestData(guestRegistrationData as GuestData);
                  setAccessToken(token || null);
                  setCompletedSteps((prev) => [...new Set([...prev, 1])]);
                  setActiveStep(2);
                }}
              />
            )}
            {activeStep === 2 &&
              (isCrowdfundingEnabled ? (
                <CashGiftAmount
                  onContinue={(resId) => {
                    setCompletedSteps((prev) => [...new Set([...prev, 2])]);
                    if (resId) {
                      setReservationId(resId);
                    }
                    setShowPaymentModal(true);
                  }}
                  cashGift={cashGift}
                  accessToken={accessToken}
                />
              ) : (
                <SendCashGift
                  onContinue={(resId) => {
                    setCompletedSteps((prev) => [...new Set([...prev, 2])]);
                    if (resId) {
                      setReservationId(resId);
                    }
                    setShowPaymentModal(true);
                  }}
                  cashGift={cashGift}
                  guestData={guestData as GuestData}
                  accessToken={accessToken}
                  eventDetails={eventDetails}
                />
              ))}
          </div>
        </div>
      </div>

      {showPaymentModal && (
        <PaymentModal
          onPayNow={() => setShowSuccess(true)}
          onPayLater={() => navigate(`/guest/events/${eventId}/gifts`)}
          onClose={() => setShowPaymentModal(false)}
          reservationId={reservationId || undefined}
        />
      )}
    </div>
  );
};
