<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Anniversary Invitation</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=MonteCarlo&family=Miama&family=Anonymous+Pro:wght@400&family=Charter:wght@400&display=swap"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Charter", serif;
        background-color: #f5f5f5;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
      }

      .invitation-container {
        position: relative;
        width: 1240px;
        height: 1748px;
        background-image: url("https://customer-preprod.eventpark.africa/assets/email-templates/anniversary-background.png");
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        max-width: 100%;
        max-height: 100vh;
        object-fit: contain;
      }

      .text-element {
        position: absolute;
      }

      .cheers-text {
        font-family: "MonteCarlo", cursive;
        font-weight: 400;
        font-size: 220px;
        line-height: 1.049999930641868em;
        letter-spacing: 4%;
        text-align: center;
        color: #000000;
        left: 48.25px;
        top: 431.35723528265953px;
        width: 1022.3303271532059px;
        height: 686.7833245098591px;
        transform-origin: center center;
      }

      .celebration-text {
        font-family: "Miama", cursive;
        font-weight: 400;
        font-size: 56px;
        line-height: 1.0499999182564872em;
        letter-spacing: 3%;
        text-align: left;
        color: #000000;
        left: 212px;
        top: 1236px;
        width: 817px;
        height: 59px;
        transform-origin: left center;
      }

      .couple-names {
        font-family: "Miama", cursive;
        font-weight: 400;
        font-size: 60px;
        line-height: 1.0499999364217123em;
        letter-spacing: 2.9999999999999996%;
        text-align: left;
        color: #b4823b;
        left: 271px;
        top: 1459px;
        width: 699px;
        height: 63px;
        transform-origin: left center;
      }

      .anniversary-text {
        font-family: "Anonymous Pro", monospace;
        font-weight: 400;
        font-size: 66px;
        line-height: 1.049999930641868em;
        letter-spacing: 20%;
        text-transform: uppercase;
        text-align: left;
        color: #000000;
        left: 356px;
        top: 1331px;
        width: 529px;
        height: 69px;
        transform-origin: left center;
      }

      .event-date {
        font-family: "Charter", serif;
        font-weight: 400;
        font-size: 42px;
        line-height: 1.399999981834775em;
        letter-spacing: 3%;
        text-align: left;
        color: #000059;
        left: 94px;
        top: 94px;
        width: 168px;
        height: 118px;
      }

      .venue-address {
        font-family: "Charter", serif;
        font-weight: 400;
        font-size: 38px;
        line-height: 1.22021484375em;
        text-align: center;
        color: #000000;
        left: 278px;
        top: 1575px;
        width: 685px;
        height: 46px;
      }

      .rsvp-info {
        font-family: "Charter", serif;
        font-weight: 400;
        font-size: 38px;
        line-height: 1.22021484375em;
        text-align: center;
        color: #000059;
        left: 446px;
        top: 1633px;
        width: 348px;
        height: 46px;
      }

      /* Dynamic scaling for cheers text */
      .cheers-text[data-length="8"] {
        transform: scale(0.95);
      }
      .cheers-text[data-length="9"] {
        transform: scale(0.9);
      }
      .cheers-text[data-length="10"] {
        transform: scale(0.85);
      }
      .cheers-text[data-length="11"] {
        transform: scale(0.8);
      }
      .cheers-text[data-length="12"] {
        transform: scale(0.75);
      }
      .cheers-text[data-length="13"] {
        transform: scale(0.7);
      }
      .cheers-text[data-length="14"] {
        transform: scale(0.65);
      }
      .cheers-text[data-length="15"] {
        transform: scale(0.6);
      }
      .cheers-text[data-length="16"] {
        transform: scale(0.55);
      }
      .cheers-text[data-length="17"] {
        transform: scale(0.5);
      }
      .cheers-text[data-length="18"],
      .cheers-text[data-length="19"],
      .cheers-text[data-length="20"] {
        transform: scale(0.45);
      }

      /* Dynamic scaling for couple names */
      .couple-names[data-length="8"] {
        transform: scale(0.95);
      }
      .couple-names[data-length="9"] {
        transform: scale(0.9);
      }
      .couple-names[data-length="10"] {
        transform: scale(0.85);
      }
      .couple-names[data-length="11"] {
        transform: scale(0.8);
      }
      .couple-names[data-length="12"] {
        transform: scale(0.75);
      }
      .couple-names[data-length="13"] {
        transform: scale(0.7);
      }
      .couple-names[data-length="14"] {
        transform: scale(0.65);
      }
      .couple-names[data-length="15"] {
        transform: scale(0.6);
      }
      .couple-names[data-length="16"] {
        transform: scale(0.55);
      }
      .couple-names[data-length="17"] {
        transform: scale(0.5);
      }
      .couple-names[data-length="18"],
      .couple-names[data-length="19"],
      .couple-names[data-length="20"] {
        transform: scale(0.45);
      }

      @media screen and (max-width: 1280px) {
        .invitation-container {
          transform: scale(0.8);
          transform-origin: center center;
        }
      }

      @media screen and (max-width: 1024px) {
        .invitation-container {
          transform: scale(0.6);
          transform-origin: center center;
        }
      }

      @media screen and (max-width: 768px) {
        .invitation-container {
          transform: scale(0.4);
          transform-origin: center center;
        }
      }
    </style>
  </head>
  <body>
    <div class="invitation-container">
      <div class="text-element cheers-text">
        Cheers<br />to {{{years}}} years!
      </div>

      <div class="text-element event-date">{{{event_date}}}</div>

      <div class="text-element celebration-text">{{{celebration_text}}}</div>

      <div class="text-element anniversary-text">anniversary</div>

      <div class="text-element couple-names">{{{couple_names}}} {{/if}}</div>

      <div class="text-element venue-address">{{{venue_address}}}</div>

      <div class="text-element rsvp-info">{{{rsvp_info}}}</div>
    </div>

    <script>
      function calculateTextLengths() {
        const cheersText = document.querySelector(".cheers-text");
        const coupleNames = document.querySelector(".couple-names");

        if (cheersText) {
          const textContent = cheersText.textContent || cheersText.innerText;
          const cleanText = textContent.replace(/\s+/g, " ").trim();
          const totalLength = cleanText.length;
          cheersText.setAttribute("data-length", totalLength.toString());
          console.log(`Cheers text length: ${totalLength} characters`);
        }

        if (coupleNames) {
          const textContent = coupleNames.textContent || coupleNames.innerText;
          const cleanText = textContent.replace(/\s+/g, " ").trim();
          const totalLength = cleanText.length;
          coupleNames.setAttribute("data-length", totalLength.toString());
          console.log(`Couple names length: ${totalLength} characters`);
        }
      }

      // Run the calculation when the page loads
      document.addEventListener("DOMContentLoaded", calculateTextLengths);

      // Also run it after a short delay to ensure all content is loaded
      setTimeout(calculateTextLengths, 100);
    </script>
  </body>
</html>
