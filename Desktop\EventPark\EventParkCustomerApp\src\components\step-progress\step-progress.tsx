import { ArrowRight2 } from "iconsax-react";
import React from "react";

export interface Step {
  id: number;
  name: string;
}

interface StepProgressProps {
  steps: Step[];
  activeStep: number;
  completedSteps: number[];
  onStepChange?: (stepId: number) => void;
  className?: string; // Add this prop to allow custom styling
}

export const StepProgress: React.FC<StepProgressProps> = ({
  steps,
  activeStep,
  completedSteps,
  onStepChange,
  className = "", // Default to empty string
}) => {
  const isStepActive = (stepId: number) => activeStep === stepId;
  const isStepCompleted = (stepId: number) => completedSteps.includes(stepId);
  const isStepClickable = (stepId: number) =>
    isStepCompleted(stepId) ||
    stepId === activeStep ||
    stepId === activeStep - 1;

  const handleStepClick = (stepId: number) => {
    if (isStepClickable(stepId) && onStepChange) {
      onStepChange(stepId);
    }
  };

  return (
    <div
      className={`flex items-center justify-start pl-2 md:pl-6  flex-wrap border-y border-grey-150 gap-2 mt-4 w-full mx-auto ${className}`}
    >
      <div className="max-w-[560px] w-full mx-auto overflow-y-auto scrollbar-hide flex items-center  gap-2">
        {steps.map((step, index) => (
          <React.Fragment key={step.id}>
            <div
              onClick={() => handleStepClick(step.id)}
              className={`flex items-center py-2 cursor-pointer text-sm sm:text-base ${
                isStepActive(step.id)
                  ? "text-primary-50 font-bold italic"
                  : isStepCompleted(step.id)
                  ? "text-primary-50"
                  : "text-grey-250"
              } ${
                isStepClickable(step.id)
                  ? "cursor-pointer"
                  : "cursor-not-allowed"
              }`}
            >
              {isStepActive(step.id) && (
                <span className="bg-cus-orange-100 h-1.5 w-1.5 rounded-full mr-2"></span>
              )}
              <h3 className="whitespace-nowrap text-sm sm:text-base">
                {step.name}
              </h3>
            </div>
            {index < steps.length - 1 && (
              <ArrowRight2 size="16" color="#292D32" />
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};
