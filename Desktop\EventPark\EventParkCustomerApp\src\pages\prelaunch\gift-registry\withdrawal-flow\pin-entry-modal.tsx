import React, { useState, useEffect, useCallback } from "react";
// import { CloseCircle } from "iconsax-react";
import { ClipLoader } from "react-spinners";
import stackMoney from "../../../../assets/images/stack-money2.svg";
import { WalletAPI, PayoutAccount } from "../../../../lib/apis/walletapi";
import { toast } from "react-toastify";

interface PinEntryModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  amount: number;
  walletId: string;
  selectedAccount: PayoutAccount;
}

export const PinEntryModal: React.FC<PinEntryModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  amount,
  walletId,
  selectedAccount,
}) => {
  const [pin, setPin] = useState(["", "", "", ""]);
  const [isChecking, setIsChecking] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  const handlePinChange = (index: number, value: string) => {
    if (value.length > 1) return;

    const newPin = [...pin];
    newPin[index] = value;
    setPin(newPin);

    // Auto focus next input
    if (value && index < 3) {
      const nextInput = document.getElementById(`pin-modal-${index + 1}`);
      nextInput?.focus();
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    // Handle backspace to go to previous input
    if (e.key === "Backspace" && !pin[index] && index > 0) {
      const prevInput = document.getElementById(`pin-modal-${index - 1}`);
      prevInput?.focus();
    }
  };

  const isPinComplete = pin.join("").length === 4;

  const handleSuccessComplete = () => {
    setShowSuccess(false);
    onSuccess();
  };

  const resetModal = () => {
    setPin(["", "", "", ""]);
    setIsChecking(false);
    setShowSuccess(false);
  };

  // Reset modal when it opens
  useEffect(() => {
    if (isOpen) {
      resetModal();
    }
  }, [isOpen]);

  const handleWithdrawal = useCallback(async () => {
    setIsChecking(true);

    try {
      const withdrawalPayload = {
        amount: amount.toString(),
        payout_account_id: selectedAccount.id,
        transaction_pin: pin.join(""),
      };

      await WalletAPI.withdrawFromWallet(walletId, withdrawalPayload);

      setIsChecking(false);
      setShowSuccess(true);
      toast.success("Withdrawal successful!");
    } catch (error) {
      setIsChecking(false);
      console.error("Withdrawal failed:", error);

      // Handle different error types
      if (error && typeof error === "object" && "message" in error) {
        toast.error(error.message as string);
      } else {
        toast.error("Withdrawal failed. Please try again.");
      }

      // Reset PIN on error
      setPin(["", "", "", ""]);
    }
  }, [amount, selectedAccount.id, pin, walletId]);

  // Auto-trigger withdrawal when PIN is complete
  useEffect(() => {
    if (isPinComplete && !isChecking && !showSuccess) {
      handleWithdrawal();
    }
  }, [isPinComplete, isChecking, showSuccess, handleWithdrawal]);

  if (!isOpen) return null;

  // Show checking state
  if (isChecking) {
    return (
      <div className="fixed inset-0 bg-[#********]/40 flex items-center justify-center z-50 font-rethink">
        <div className="bg-white rounded-3xl p-8 w-full h-[456px] md:w-[522px] mx-4 relative">
          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-6 right-6 hover:text-grey-50 transition-colors cursor-pointer"
          >
            <svg
              width="40"
              height="40"
              viewBox="0 0 40 40"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                opacity="0.4"
                d="M20.0026 36.6673C29.2074 36.6673 36.6693 29.2054 36.6693 20.0007C36.6693 10.7959 29.2074 3.33398 20.0026 3.33398C10.7979 3.33398 3.33594 10.7959 3.33594 20.0007C3.33594 29.2054 10.7979 36.6673 20.0026 36.6673Z"
                fill="#4D55F2"
              />
              <path
                d="M21.7682 19.9996L25.6016 16.1663C26.0849 15.6829 26.0849 14.8829 25.6016 14.3996C25.1182 13.9163 24.3182 13.9163 23.8349 14.3996L20.0016 18.2329L16.1682 14.3996C15.6849 13.9163 14.8849 13.9163 14.4016 14.3996C13.9182 14.8829 13.9182 15.6829 14.4016 16.1663L18.2349 19.9996L14.4016 23.8329C13.9182 24.3163 13.9182 25.1163 14.4016 25.5996C14.6516 25.8496 14.9682 25.9663 15.2849 25.9663C15.6016 25.9663 15.9182 25.8496 16.1682 25.5996L20.0016 21.7663L23.8349 25.5996C24.0849 25.8496 24.4016 25.9663 24.7182 25.9663C25.0349 25.9663 25.3516 25.8496 25.6016 25.5996C26.0849 25.1163 26.0849 24.3163 25.6016 23.8329L21.7682 19.9996Z"
                fill="#4D55F2"
              />
            </svg>
          </button>

          {/* Checking Content */}
          <div className="text-center pt-4 mt-[120px]">
            <div className="flex justify-center mb-8">
              <ClipLoader size={120} className="stroke-[12]" color="#CACCFB" />
            </div>
            <h2 className="text-[28px] font-semibold text-center text-grey-50 mb-2">
              Checking your Pin
            </h2>
          </div>
        </div>
      </div>
    );
  }

  // Show success state
  if (showSuccess) {
    return (
      <div className="fixed inset-0 bg-[#********]/40 flex items-center justify-center z-50 font-rethink">
        <div className="bg-white rounded-3xl  w-full h-[520px] sm:h-[596px] md:w-[522px] mx-4 relative">
          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-6 right-6 hover:text-grey-50 transition-colors cursor-pointer"
          >
            <svg
              width="40"
              height="40"
              viewBox="0 0 40 40"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                opacity="0.4"
                d="M20.0026 36.6673C29.2074 36.6673 36.6693 29.2054 36.6693 20.0007C36.6693 10.7959 29.2074 3.33398 20.0026 3.33398C10.7979 3.33398 3.33594 10.7959 3.33594 20.0007C3.33594 29.2054 10.7979 36.6673 20.0026 36.6673Z"
                fill="#4D55F2"
              />
              <path
                d="M21.7682 19.9996L25.6016 16.1663C26.0849 15.6829 26.0849 14.8829 25.6016 14.3996C25.1182 13.9163 24.3182 13.9163 23.8349 14.3996L20.0016 18.2329L16.1682 14.3996C15.6849 13.9163 14.8849 13.9163 14.4016 14.3996C13.9182 14.8829 13.9182 15.6829 14.4016 16.1663L18.2349 19.9996L14.4016 23.8329C13.9182 24.3163 13.9182 25.1163 14.4016 25.5996C14.6516 25.8496 14.9682 25.9663 15.2849 25.9663C15.6016 25.9663 15.9182 25.8496 16.1682 25.5996L20.0016 21.7663L23.8349 25.5996C24.0849 25.8496 24.4016 25.9663 24.7182 25.9663C25.0349 25.9663 25.3516 25.8496 25.6016 25.5996C26.0849 25.1163 26.0849 24.3163 25.6016 23.8329L21.7682 19.9996Z"
                fill="#4D55F2"
              />
            </svg>
          </button>

          {/* Success Content */}
          <div className="text-center   ">
            {/* Money/Success Illustration - Enhanced */}
            <div className="flex justify-center rounded-t-3xl mb-6 p-8 pt-[60px] bg-[#F5F6FE] h-[280px] sm:h-[310px]">
              <div className="relative">
                {/* Enhanced money bills and coins illustration */}
                <img
                  src={stackMoney}
                  alt=""
                  className="w-[220px] sm:w-[283px] h-auto"
                />
              </div>
            </div>

            <h2 className="text-[24px] sm:text-[28px] leading-normal font-semibold text-grey-50 mb-2">
              Your Money has Successfully
            </h2>
            <p className="text-[20px] sm:text-[24px] leading-normal mt-[-3px] text-[#808080] mb-2">
              been sent
            </p>
            <p className="text-[14px] sm:text-[16px] text-[#808080] mb-6 max-w-[450px] mx-auto">
              Please note that it might take a few minutes for the funds to
              appear in your bank account.
            </p>

            <div className="flex justify-center mt-5 pt-5 border-t border-t-gray-200 items-center">
              <button
                onClick={handleSuccessComplete}
                className="w-[200px] flex items-center justify-center px-6 rounded-full font-bold text-base bg-primary h-[40px] text-white hover:bg-primary/90 transition-all"
              >
                Back to Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show PIN entry state
  return (
    <div className="fixed inset-0 bg-[#********]/40 flex items-center justify-center z-50 font-rethink">
      <div className="bg-white rounded-3xl p-8 w-full h-[456px] md:w-[522px] mx-4 relative">
        {/* Close Button */}
        <button onClick={onClose} className=" cursor-pointer">
          <svg
            width="40"
            height="40"
            viewBox="0 0 40 40"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              opacity="0.4"
              d="M20.0026 36.6673C29.2074 36.6673 36.6693 29.2054 36.6693 20.0007C36.6693 10.7959 29.2074 3.33398 20.0026 3.33398C10.7979 3.33398 3.33594 10.7959 3.33594 20.0007C3.33594 29.2054 10.7979 36.6673 20.0026 36.6673Z"
              fill="#4D55F2"
            />
            <path
              d="M21.7682 19.9996L25.6016 16.1663C26.0849 15.6829 26.0849 14.8829 25.6016 14.3996C25.1182 13.9163 24.3182 13.9163 23.8349 14.3996L20.0016 18.2329L16.1682 14.3996C15.6849 13.9163 14.8849 13.9163 14.4016 14.3996C13.9182 14.8829 13.9182 15.6829 14.4016 16.1663L18.2349 19.9996L14.4016 23.8329C13.9182 24.3163 13.9182 25.1163 14.4016 25.5996C14.6516 25.8496 14.9682 25.9663 15.2849 25.9663C15.6016 25.9663 15.9182 25.8496 16.1682 25.5996L20.0016 21.7663L23.8349 25.5996C24.0849 25.8496 24.4016 25.9663 24.7182 25.9663C25.0349 25.9663 25.3516 25.8496 25.6016 25.5996C26.0849 25.1163 26.0849 24.3163 25.6016 23.8329L21.7682 19.9996Z"
              fill="#4D55F2"
            />
          </svg>
        </button>

        {/* Content */}
        <div className="text-center pt-4 mt-[80px] sm:mt-[120px]">
          {/* PIN Input */}
          <div className="flex justify-center gap-3 mb-8">
            {pin.map((digit, index) => (
              <input
                key={index}
                id={`pin-modal-${index}`}
                type="password"
                inputMode="numeric"
                value={digit}
                onChange={(e) => handlePinChange(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown(index, e)}
                className="w-[72px] h-[80px] text-center text-primary text-xl font-bold border-2 border-grey-150 rounded-lg focus:border-primary-50 focus:outline-none transition-colors"
                maxLength={1}
                autoFocus={index === 0}
                disabled={isChecking}
              />
            ))}
          </div>

          {/* Title */}
          <h2 className="text-[20px] sm:text-[28px] mt-14 font-semibold text-grey-50 mb-2">
            Enter your Transaction Pin
          </h2>

          {/* Subtitle */}
          <p className="text-[14px] sm:text-base text-grey-250 mb-8">
            Please enter your 4-digit transaction pin
          </p>
        </div>
      </div>
    </div>
  );
};
