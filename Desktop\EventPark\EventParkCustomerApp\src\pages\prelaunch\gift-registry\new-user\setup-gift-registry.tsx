import { CloseCircle } from 'iconsax-react';
import { Icon } from '../../../../components/icons/icon';
import { AccountSetup } from './account-setup';
import { WalletSetup } from './wallet-setup';
import { DeliveryAddress } from './delivery-address';
import { AllSet } from './all-set';
import { useState } from 'react';
// import { useNavigate } from 'react-router-dom';
import { useUserAuthStore } from '../../../../lib/store/auth';

interface RegistryData {
  bank?: string;
  accountNumber?: string;
  state?: string;
  city?: string;
  address?: string;
  shareAddress?: boolean;
}

export const SetupGiftRegistry = ({
  activeStep,
  completedSteps = [],
  toolCompletedSteps = [],
  onStepChange,
  onClose,
}: {
  activeStep: number;
  completedSteps: number[];
  toolCompletedSteps?: number[];
  onStepChange: (step: number) => void;
  onClose?: () => void;
}) => {
  const [registryData, setRegistryData] = useState<RegistryData>({});
  // const navigate = useNavigate();
  const { toolStatus } = useUserAuthStore();
  const steps = [
    { id: 1, name: 'Account Setup' },
    { id: 2, name: 'Wallet Setup' },
    { id: 3, name: 'Delivery Address' },
    { id: 4, name: 'All Set' },
  ];

  const handleNextStep = (data: Partial<RegistryData>) => {
    setRegistryData((prev) => ({ ...prev, ...data }));

    let nextStep = activeStep + 1;

   
    if (activeStep === 1 && toolStatus?.has_wallet) {
      nextStep = 3;
    }

    onStepChange(nextStep);
  };

  const handleStepClick = (step: number) => {
    onStepChange(step);
  };

  const handleNext = () => {
    if (onClose) {
      onClose();
    }
  };

  return (
    <div>
      <div className="w-full font-rethink">
        <div className="flex justify-between items-center px-4 md:px-12 h-[77px] border-b border-gray-200">
          <h1 className="text-2xl font-medium">Create Gift Registry</h1>
          <div className="text-gray-500">
            <button
              onClick={handleNext}
              className="w-10 h-10 cursor-pointer rounded-full bg-white flex justify-center items-center">
              <CloseCircle color="#FF6630" size="24" variant="Bulk" />
            </button>
          </div>
        </div>

        <div className="md:fixed xl:left-[calc(20%-40px)] left-3 top-[120px] z-50">
          <div className="">
            <div className="md:w-[170px] flex px-4 md:px-0 flex-row flex-wrap mt-8 md:mt-0 md:flex-col justify-center md:justify-start gap-3">
              {steps.map((step) => (
                <div
                  key={step.id}
                  className={`flex items-center md:py-2 ${
                    completedSteps.includes(step.id)
                      ? 'cursor-pointer'
                      : 'cursor-default opacity-60'
                  } text-sm ${
                    activeStep === step.id &&
                    !toolCompletedSteps.includes(step.id)
                      ? 'text-primary-50 font-bold italic'
                      : toolCompletedSteps.includes(step.id)
                      ? 'text-primary-50'
                      : activeStep === step.id
                      ? 'text-primary-50 font-bold italic'
                      : 'text-grey-250'
                  }`}
                  onClick={() =>
                    completedSteps.includes(step.id) && handleStepClick(step.id)
                  }>
                  {activeStep === step.id &&
                    !toolCompletedSteps.includes(step.id) && (
                      <span className="bg-cus-orange-100 h-1.5 w-1.5 rounded-full mr-2"></span>
                    )}
                  {toolCompletedSteps.includes(step.id) && (
                    <span className="mr-[5px]">
                      <Icon name="marked" />
                    </span>
                  )}
                  <span>{step.name}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {activeStep === 1 && (
        <AccountSetup onNextStep={handleNextStep} initialData={registryData} />
      )}

      {activeStep === 2 && (
        <WalletSetup onNextStep={() => handleNextStep({})} />
      )}

      {activeStep === 3 && (
        <DeliveryAddress
          onNextStep={handleNextStep}
          initialData={registryData}
        />
      )}

      {activeStep === 4 && (
        <AllSet
        // onComplete={handleComplete}
        />
      )}
    </div>
  );
};
