import { twMerge } from "tailwind-merge";

type Percent = number | `${number}%`;
export function Progress({
  percent,
  size = "base",
  trackClassName,
  showTracker = true,
}: {
  percent: Percent;
  size: "base" | "small";
  trackClassName?: string;
  showTracker?: boolean;
}) {
  const percentString =
    typeof percent === "string" ? percent : `${percent * 100}%`;
  return (
    <div
      className={twMerge(
        `relative ${
          size === "base" ? "h-[8px]" : "h-1"
        } w-full bg-primary-600 rounded`,
        trackClassName
      )}
    >
      <div
        className="h-full bg-primary rounded-l transition-all duration-300"
        style={{ width: percentString }}
      ></div>
      {showTracker && (
        <div
          style={{ left: percentString }}
          className={`absolute top-1/2 -translate-y-1/2 -translate-x-1/2 ${
            size === "base" ? "w-6 h-6" : "w-3 h-3"
          } flex items-center justify-center bg-primary-100 rounded-full`}
        >
          <div
            className={`${
              size === "base" ? "w-4 h-4" : "w-2 h-2"
            } aspect-square rounded-full bg-primary`}
          ></div>
        </div>
      )}
    </div>
  );
}
