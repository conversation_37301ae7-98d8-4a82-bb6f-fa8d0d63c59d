import { ArrowRight } from "iconsax-react";
import { Link } from "react-router-dom";
import emoji from "../../assets/images/emoji.png";

export const SuccessStep = () => {
  return (
    <div className=" md:w-[450px] bg-white rounded-[28px]">
      <div
        className="bg-[linear-gradient(180deg,_#FDEFE9_0%,_#FEF7F4_100%)] relative rounded-t-[20px] h-[262px] w-full overflow-hidden"
        style={{
          clipPath: 'polygon(0 0, 100% 0, 100% 100%, 70% 95%, 30% 95%, 0 100%)',
        }}>
        {' '}
        <div className="flex justify-center items-center text-[155px] h-full">
          <img src={emoji} alt="emoji" />
        </div>
      </div>{' '}
      <div className="relative z-10 flex flex-col items-center text-center py-12 px-4 w-full">
        <h2 className="text-4xl font-medium mb-2 text-dark-200">
          Your Password has <br />{' '}
          <span className="text-[18px] md:text-[26px] text-grey-250">
            been changed successfully
          </span>
        </h2>
        <p className="text-grey-250 text-base mb-8">
          You’re all set. Your account is now secured
          <br />
          with your new password.{' '}
        </p>
        <Link
          to="/login"
          className="bg-primary text-2xl text-white flex items-center py-3 px-6 font-semibold rounded-full gap-2 hover:bg-[#4A48E0] transition-colors">
          <span>Continue to Sign In</span>
          <div className="rounded-full bg-white/30 p-0.5">
            <ArrowRight size="16" color="#fff" />
          </div>
        </Link>
      </div>
    </div>
  );
};
