import { useEffect, useState, useRef, useCallback } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { motion } from "framer-motion";
import { toast } from "react-toastify";
import successGIF from "../assets/animations/gift.gif";
import { GuestGiftsAPI, SingleReservation } from "../lib/apis/guestGiftsApi";
import { guestTokenManager } from "../lib/utils/guestTokenManager";

export const GuestPaymentComplete = () => {
  const navigate = useNavigate();
  const { reservationId: urlReservationId } = useParams<{
    reservationId?: string;
  }>();
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [reservation, setReservation] = useState<SingleReservation | null>(
    null
  );
  const [error, setError] = useState<string | null>(null);
  const [paymentStatus, setPaymentStatus] = useState<
    "not_started" | "processing" | "completed" | "failed"
  >("not_started");
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Check for error parameter in URL - if present, payment failed regardless of API response
  const urlError = searchParams.get("error");
  const hasPaymentError = !!urlError;
  const formatAmount = (amount: string) => {
    if (!amount) return "₦0.00";
    return `₦${new Intl.NumberFormat("en-NG").format(parseInt(amount))}.00`;
  };

  // Function to fetch reservation and update payment status
  const fetchReservation = useCallback(
    async (reservationId: string, isInitialLoad = false) => {
      try {
        const accessToken = await guestTokenManager.getGuestAccessToken();
        if (!accessToken) {
          setError("No access token found");
          return null;
        }

        const reservationData = await GuestGiftsAPI.getReservation(
          reservationId
        );
        setReservation(reservationData);

        // Check for URL error parameter first - if present, payment failed regardless of API response
        if (hasPaymentError) {
          console.log(
            "🚨 Payment failed due to URL error parameter:",
            urlError
          );
          setPaymentStatus("failed");
          toast.error(
            `Payment failed: ${decodeURIComponent(
              urlError || "An error occurred"
            )}`
          );
        } else {
          // Update payment status based on API response
          const status = guestTokenManager.getPaymentStatus(reservationData);
          setPaymentStatus(status);
        }

        // Clear the reservation ID since we've processed it (only on initial load)
        if (isInitialLoad) {
          guestTokenManager.clearGuestReservationId();
        }

        return reservationData;
      } catch (reservationError) {
        console.error("Failed to fetch reservation:", reservationError);

        // Check if it's a 404 or similar error indicating invalid reservation ID
        if (
          typeof reservationError === "object" &&
          reservationError &&
          "code" in reservationError &&
          (reservationError as { code?: string }).code === "not_found"
        ) {
          setError(
            "Reservation not found. The payment link may be invalid or expired."
          );
        } else {
          setError("Failed to fetch payment details. Please try again.");
        }
        return null;
      }
    },
    [hasPaymentError, urlError]
  );

  // Start polling for payment status updates
  const startPolling = useCallback(
    (reservationId: string) => {
      // Clear any existing polling
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }

      pollingIntervalRef.current = setInterval(async () => {
        console.log("🔄 Polling payment status...");

        // If we have an error parameter, don't poll - payment already failed
        if (hasPaymentError) {
          console.log("🛑 Stopping polling due to URL error parameter");
          if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
          }
          return;
        }

        const reservationData = await fetchReservation(reservationId, false);

        if (reservationData) {
          const status = guestTokenManager.getPaymentStatus(reservationData);

          // Stop polling if payment is completed or failed
          if (status === "completed" || status === "failed") {
            console.log(
              `✅ Payment status changed to: ${status}. Stopping polling.`
            );
            if (pollingIntervalRef.current) {
              clearInterval(pollingIntervalRef.current);
              pollingIntervalRef.current = null;
            }

            // Show appropriate message
            if (status === "completed") {
              toast.success("Payment completed successfully!");
            } else if (status === "failed") {
              toast.error("Payment failed. Please try again.");
            }
          }
        }
      }, 10000); // Poll every 10 seconds
    },
    [hasPaymentError, fetchReservation]
  );

  // Stop polling when component unmounts or user leaves page
  const stopPolling = () => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
      console.log("🛑 Stopped payment status polling");
    }
  };

  useEffect(() => {
    const verifyPaymentAndFetchReservation = async () => {
      try {
        // Check for URL error parameter first - if present, payment failed immediately
        if (hasPaymentError) {
          console.log(
            "🚨 Payment failed due to URL error parameter:",
            urlError
          );
          setPaymentStatus("failed");
          setLoading(false);
          toast.error(
            `Payment failed: ${decodeURIComponent(
              urlError || "An error occurred"
            )}`
          );
          return;
        }

        const reservationId =
          urlReservationId || guestTokenManager.getGuestReservationId();

        if (!reservationId) {
          setError("No reservation ID found");
          setLoading(false);
          return;
        }

        const reservationData = await fetchReservation(reservationId, true);

        if (reservationData) {
          const status = guestTokenManager.getPaymentStatus(reservationData);

          // Start polling if payment is processing and no error parameter
          if (status === "processing" && !hasPaymentError) {
            console.log("💳 Payment is processing. Starting polling...");
            startPolling(reservationId);
          }
        }
      } catch (err) {
        const errorMsg =
          typeof err === "object" && err && "message" in err
            ? (err as { message?: string }).message ??
              "Failed to verify payment"
            : "Failed to verify payment";
        setError(errorMsg);
        toast.error(errorMsg);
      } finally {
        setLoading(false);
      }
    };

    verifyPaymentAndFetchReservation();

    // Cleanup polling on unmount
    return () => {
      stopPolling();
    };
  }, [
    urlReservationId,
    hasPaymentError,
    urlError,
    fetchReservation,
    startPolling,
  ]);

  const handleReturnToGifts = () => {
    const eventId = guestTokenManager.getGuestEventId();
    if (eventId) {
      navigate(`/guest/events/${eventId}/gifts`);
    } else {
      navigate("/");
    }
  };

  if (loading) {
    return (
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="min-h-screen px-4 pt-4 md:pt-0 bg-[linear-gradient(177.78deg,_#FFE5E5_24.89%,_#F5F6FE_98.13%)] flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-grey-250">Verifying payment...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="min-h-screen px-4 pt-4 md:pt-0 bg-[linear-gradient(177.78deg,_#FFE5E5_24.89%,_#F5F6FE_98.13%)] flex items-center justify-center">
          <div className="text-center">
            <div className="text-red-500 text-xl mb-4">
              Payment Verification Failed
            </div>
            <p className="text-grey-250 mb-6">{error}</p>
            <button
              onClick={handleReturnToGifts}
              className="bg-primary text-white px-6 py-3 rounded-full hover:bg-primary-dark transition-colors"
            >
              Return to Gifts
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Render different content based on payment status
  const renderPaymentStatusContent = () => {
    // Use the paymentStatus state directly instead of getting it from reservation
    // This allows us to show content even when reservation is null (e.g., URL error cases)
    const status = paymentStatus;

    switch (status) {
      case "completed":
        return (
          <>
            {/* Success Content */}
            <img
              src={successGIF}
              alt="Success animation"
              className="w-full transition-all h-[469px] opacity-40 absolute object-cover top-0 left-0 right-0"
            />
            <div className="relative z-10 flex flex-col items-center text-center pt-12 px-4 w-full max-w-md">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="mb-8"
              >
                <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg
                    className="w-12 h-12 text-green-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                  Payment Successful!
                </h1>
                <p className="text-gray-600 mb-6">
                  Your gift has been sent successfully
                </p>
              </motion.div>
            </div>
          </>
        );

      case "processing":
        return (
          <div className="relative z-10 flex flex-col items-center text-center pt-12 px-4 w-full max-w-md">
            <div className="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Payment Processing
            </h1>
            <p className="text-gray-600 mb-6">
              Please wait while we process your payment...
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <p className="text-blue-800 text-sm">
                🔄 We're checking your payment status every 10 seconds. You'll
                be notified when it's complete.
              </p>
            </div>
          </div>
        );

      case "failed":
        return (
          <div className="relative z-10 flex flex-col items-center text-center pt-12 px-4 w-full max-w-md">
            <div className="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg
                className="w-12 h-12 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Payment Failed
            </h1>
            <p className="text-gray-600 mb-6">
              {hasPaymentError && urlError
                ? `Error: ${decodeURIComponent(urlError)}`
                : "Unfortunately, your payment could not be processed"}
            </p>
          </div>
        );

      case "not_started":
        return (
          <div className="relative z-10 flex flex-col items-center text-center pt-12 px-4 w-full max-w-md">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg
                className="w-12 h-12 text-gray-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Payment Pending
            </h1>
            <p className="text-gray-600 mb-6">
              Payment has not been initiated yet
            </p>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="min-h-screen flex-col px-4 pt-4 md:pt-0 bg-[linear-gradient(177.78deg,_#FFE5E5_24.89%,_#F5F6FE_98.13%)] flex items-center justify-center relative">
        {renderPaymentStatusContent()}

        {/* Reservation Details and Actions */}
        {reservation && (
          <div className="relative z-10  text-center">
            <motion.div
              transition={{ duration: 0.5, delay: 0.4 }}
              className="mb-6"
            >
              <p className="text-grey-250 text-base">
                {paymentStatus === "completed" &&
                  "Your gift reservation has been confirmed."}
                {paymentStatus === "processing" &&
                  "We'll notify you once the payment is complete."}
                {paymentStatus === "failed" &&
                  "Please try making the payment again."}
                {paymentStatus === "not_started" &&
                  "You can complete the payment when ready."}
                {reservation && (
                  <span className="block mt-2 font-medium">
                    Amount: {formatAmount(reservation.amount)}
                  </span>
                )}
              </p>
            </motion.div>
          </div>
        )}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <button
            onClick={handleReturnToGifts}
            className="bg-primary text-white text-lg flex items-center py-3 px-8 font-semibold rounded-full gap-2 hover:bg-primary-dark transition-colors mx-auto"
          >
            <span>Return to Gifts</span>
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>
        </motion.div>
      </div>
    </div>
  );
};
