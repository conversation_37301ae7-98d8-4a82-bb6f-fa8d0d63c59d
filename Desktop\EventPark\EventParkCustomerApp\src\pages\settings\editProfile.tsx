/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useRef, useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { AuthServices } from '../../lib/services/auth';
import { toast } from 'react-toastify';
import { useUserAuthStore } from '../../lib/store/auth';
import { User } from 'iconsax-react';
import { Info } from 'lucide-react';

interface EditProfileProps {
  initialFirstName: string;
  initialLastName: string;
  onSave: (firstName: string, lastName: string) => void;
  onClose: () => void;
}

export const EditProfile = ({
  initialFirstName,
  initialLastName,
  // onSave,
  onClose,
}: EditProfileProps) => {
  const [firstName, setFirstName] = useState(initialFirstName);
  const [lastName, setLastName] = useState(initialLastName);
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const userData = useUserAuthStore((state) => state.userData);
  const MAX_IMAGE_SIZE =
    Number(import.meta.env.VITE_MAX_IMAGE_SIZE || 10) * 1024 * 1024; // Convert MB to bytes

  const modalRef = useRef<HTMLDivElement>(null);

  const uploadMutation = useMutation({
    mutationFn: (file: File) => AuthServices.uploadFiles(file),
    onSuccess: async (response) => {
      const profilePictureUrl = response.data[0].public_url;
      try {
        const timestamp = new Date().getTime();
        const userResponse = await AuthServices.getUser();
        const latestUserData = userResponse.data;
        if (
          latestUserData.profile_picture &&
          !latestUserData.profile_picture.includes('?')
        ) {
          latestUserData.profile_picture = `${latestUserData.profile_picture}?t=${timestamp}`;
        }

        const currentRefreshToken = useUserAuthStore.getState().refreshToken;
        useUserAuthStore
          .getState()
          .setAuthData(
            useUserAuthStore.getState().userAppToken || '',
            latestUserData,
            currentRefreshToken || undefined
          );

        setProfileImage(`${profilePictureUrl}?t=${timestamp}`);

        toast.success('Profile image uploaded successfully');
        onClose();
      } catch (error: any) {
        console.error('Failed to fetch updated user data:', error);
        toast.error('Profile image uploaded but failed to refresh user data');
      }
    },
    onError: (error: any) => {
      console.error('Upload error:', error);
      toast.error(
        error?.response?.data?.message || 'Failed to upload profile image'
      );
    },
  });

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > MAX_IMAGE_SIZE) {
        toast.error(
          `Image size must be less than ${
            import.meta.env.VITE_MAX_IMAGE_SIZE || 10
          }MB`
        );
        return;
      }

      setImageFile(file);
      const previewUrl = URL.createObjectURL(file);
      setProfileImage(previewUrl);
    }
  };

  useEffect(() => {
    return () => {
      if (profileImage) {
        URL.revokeObjectURL(profileImage);
      }
    };
  }, [profileImage]);
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);
  const handleSave = () => {
    if (imageFile) {
      uploadMutation.mutate(imageFile);
    }
  };

  return (
    <>
      <div
        ref={modalRef}
        className="relative mb-5 bg-white rounded-2xl shadow-[0px_12px_120px_0px_#5F5F5F0F] w-full p-4 z-50">
        <div className="mb-5">
          <div className="w-16 h-16 bg-gradient-to-br from-[#FEF7F4] from-26.3% to-[#F5F6FE] to-75.01% rounded-full flex items-center justify-center text-dark-300 font-bold text-[28px] relative overflow-hidden cursor-pointer group">
            {profileImage ? (
              <img
                src={profileImage}
                alt="Profile"
                className="w-full h-full object-cover"
              />
            ) : userData?.profile_picture ? (
              <img
                src={userData.profile_picture}
                alt="Profile"
                className="w-full h-full object-cover"
              />
            ) : (
              // <>
              //   {firstName.charAt(0).toUpperCase()}
              //   {lastName.charAt(0).toUpperCase()}
              // </>
              <User color="#4D55F2" size={32} />
            )}
            <div className="absolute inset-0 bg-black/30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
              <label
                htmlFor="profile-upload"
                className="text-white text-xs cursor-pointer">
                Change
              </label>
              <input
                id="profile-upload"
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleImageUpload}
              />
            </div>
          </div>
        </div>

        <div className="mb-3">
          <label className="text-grey-500 text-sm font-medium mb-1.5 block">
            First name
          </label>
          <div className="relative">
            <input
              type="text"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              disabled
              className="w-full text-gray-700 outline-none py-2.5 px-3.5 border border-gray-300 rounded-full bg-gray-50 cursor-not-allowed"
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-4 h-4 text-gray-400">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z"
                />
              </svg>
            </div>
          </div>
        </div>

        <div className="mb-5">
          <label className="text-grey-500 text-sm font-medium mb-1.5 block">
            Last name
          </label>
          <div className="relative">
            <input
              type="text"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              disabled
              className="w-full text-gray-700 outline-none py-2.5 px-3.5 border border-gray-300 rounded-full bg-gray-50 cursor-not-allowed"
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-4 h-4 text-gray-400">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z"
                />
              </svg>
            </div>
          </div>
        </div>

        {/* Contact support message */}

        <div className="mb-5 p-2.5 bg-pink-50 border border-blue-100 rounded-xl">
          <p className="text-sm text-blue-700 flex items-center gap-2">
            <Info size={14} />
            To update your name, please contact{' '}
            <a
              href="mailto:<EMAIL>"
              className="text-primary-650 font-medium  transition-colors underline decoration-primary-650/30 hover:decoration-primary-700">
              Support
            </a>
            .
          </p>
        </div>

        <button
          onClick={handleSave}
          disabled={!imageFile || uploadMutation.isPending}
          className={`w-fit bg-primary text-white py-2 px-3.5 rounded-full text-sm font-semibold ${
            !imageFile || uploadMutation.isPending
              ? 'opacity-40 cursor-not-allowed '
              : 'cursor-pointer'
          }`}>
          {uploadMutation.isPending ? 'Uploading...' : 'Save Changes'}
        </button>
      </div>
    </>
  );
};
