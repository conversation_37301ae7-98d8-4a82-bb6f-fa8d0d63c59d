/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect } from 'react';
import { <PERSON>R<PERSON>, ArrowDown2, <PERSON><PERSON>eft2, ArrowRight2 } from 'iconsax-react';
import { UploadIVModal } from '../../../components/modals/upload-iv-modal';
import { InvitationCardDetailsModal } from '../../../components/modals';
import group from '../../../assets/images/group.png';
import { useQuery } from '@tanstack/react-query';
import {
  GuestList,
  GetTemplatesParams,
  TemplateCategory,
} from '../../../lib/services/guest-list';

interface LayerData {
  [key: string]: {
    name: string;
    type: 'text' | 'image' | 'solid' | 'bar_code' | 'qr_code';
    placeholder: string;
  };
}

interface TemplateItem {
  id?: string;
  name: string;
  preview_url?: string;
  image?: string;
  layer_data?: LayerData;
}

interface CreateIVProps {
  onNextStep?: () => void;
  onTemplateSelect: (index: number, template: TemplateItem) => void;
}

export const CreateIV = ({ onNextStep, onTemplateSelect }: CreateIVProps) => {
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [isInvitationDetailsModalOpen, setIsInvitationDetailsModalOpen] =
    useState(false);
  const [selectedTemplateForModal, setSelectedTemplateForModal] =
    useState<TemplateItem | null>(null);
  const [selectedTemplateIndex, setSelectedTemplateIndex] = useState<
    number | null
  >(null);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);

  const { data: categoriesData, isLoading: isCategoriesLoading } = useQuery({
    queryKey: ['templateCategories'],
    queryFn: () => GuestList.getTemplatesCategories(),
  });

  const categories: TemplateCategory[] = categoriesData?.data || [];

  const allCategories = [
    { id: null, name: 'All Categories' },
    ...(categories || []),
  ];

  const templateParams: GetTemplatesParams = {
    status: 'active',
    per_page: 9,
    page: currentPage,
    ...(selectedCategory && { category_id: selectedCategory }),
  };

  const {
    data: templatesData,
    isLoading: isTemplatesLoading,
    error,
  } = useQuery({
    queryKey: ['guestListTemplates', templateParams],
    queryFn: () => GuestList.getTemplates(templateParams),
  });

  const templates = templatesData?.data?.templates || [];
  const meta = templatesData?.data?.meta || {
    from: null,
    to: null,
    page: 1,
    per_page: 9,
    previous_page: false,
    next_page: false,
    page_count: 1,
    total: 0,
  };

  const isLoading = isTemplatesLoading || isCategoriesLoading;

  useEffect(() => {
    setCurrentPage(1);
  }, [selectedCategory]);

  const handleTemplateSelect = (index: number) => {
    const selectedTemplate = templates[index];
    setSelectedTemplateForModal(selectedTemplate);
    setSelectedTemplateIndex(index);
    setIsInvitationDetailsModalOpen(true);
  };

  const handleInvitationDetailsComplete = () => {
    setIsInvitationDetailsModalOpen(false);
    if (selectedTemplateIndex !== null && selectedTemplateForModal) {
      onTemplateSelect(selectedTemplateIndex, selectedTemplateForModal);
    }
  };

  const handleCategorySelect = (categoryId: string | null) => {
    setSelectedCategory(categoryId);
    setIsDropdownOpen(false);
  };

  const handleNextPage = () => {
    if (meta.next_page) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  const handlePrevPage = () => {
    if (meta.previous_page) {
      setCurrentPage((prev) => prev - 1);
    }
  };

  return (
    <>
      <div className="max-w-[550px] mx-auto pt-5 md:pt-12 px-4 md:px-0 ">
        <div className="flex justify-between items-end">
          <h2 className="md:text-[40px] text-xl font-medium leading-[114.99999999999999%]">
            Create an Invitation
            <br />
            Card for your Guests
          </h2>
          <button
            type="button"
            onClick={onNextStep}
            className="border border-cus-orange-150 cursor-pointer text-cus-orange-500 flex items-center gap-2 px-3 py-2 rounded-full text-sm font-semibold ">
            <span>Skip</span>
            <div className="bg-cus-orange-100/30 h-4 w-4 rounded-full flex justify-center items-center">
              <ArrowRight color="#FF6630" size="10" />
            </div>
          </button>
        </div>
        <div className="bg-white my-7 flex justify-between rounded-[20px] h-[97px] shadow-[0px_12px_120px_0px_#5F5F5F0F]">
          <img src={group} alt="img" className="rounded-bl-[20px]" />
          <div className="flex justify-between items-center w-full ml-2 mr-4.5">
            <div>
              <h3 className="md:text-lg text-sm font-medium">
                Upload your own IV design
              </h3>
              <p className="md:text-sm text-xs text-grey-950">
                Get your own IV design? Upload your template
              </p>
            </div>

            <button
              type="button"
              onClick={() => setIsUploadModalOpen(true)}
              className="text-primary-650 bg-primary-150 text-nowrap text-xs md:text-sm font-bold italic rounded-full cursor-pointer px-3.5 py-2 backdrop-blur-[24px] shadow-[0px_33.75px_33.75px_0px_#A6A6A60A,0px_67.49px_84.37px_0px_#A6A6A61A,0px_39.68px_198.42px_0px_#0000000F]">
              Upload IV
            </button>
          </div>
        </div>

        <div>
          <div className="flex justify-between items-center mb-6">
            <h3 className="italic tracking-[0.12em] text-xs p-2.5">
              OR SELECT FROM THE TEMPLATES BELOW
            </h3>
            <div className="relative ">
              <button
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                className="flex items-center gap-2 cursor-pointer bg-white border border-grey-200 rounded-full px-4 py-2 text-sm font-medium text-grey-600">
                {selectedCategory
                  ? categories.find((cat) => cat.id === selectedCategory)
                      ?.name || 'All Categories'
                  : 'All Categories'}
                <ArrowDown2 size={16} color="#666" />
              </button>

              {isDropdownOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg z-10 pb-1 border border-grey-100">
                  {allCategories.map((category) => (
                    <button
                      key={category.id || 'all'}
                      onClick={() => handleCategorySelect(category.id)}
                      className={`block cursor-pointer w-full text-left px-4 py-2 text-sm ${
                        selectedCategory === category.id
                          ? 'bg-primary-150 text-primary-650 font-medium'
                          : 'text-grey-950'
                      }`}>
                      {category.name}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center py-28">
              <p>Loading templates...</p>
            </div>
          ) : error ? (
            <div className="flex justify-center items-center py-28">
              <p>Error loading templates. Please try again.</p>
            </div>
          ) : templates.length === 0 ? (
            <div className="flex justify-center items-center py-28">
              <p>No templates found.</p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-5 pb-20">
                {templates.map((template: any, index: number) => (
                  <div
                    key={index}
                    className={`border-[6px] border-white overflow-hidden rounded-2xl max-w-[170px] w-full h-[202px] backdrop-blur-[24px] shadow-[0px_33.75px_33.75px_0px_#A6A6A60A,0px_67.49px_84.37px_0px_#A6A6A61A,0px_39.68px_198.42px_0px_#0000000F] cursor-pointer transition-all duration-300 `}
                    onClick={() => handleTemplateSelect(index)}>
                    <img
                      src={template.preview_url}
                      alt={template.name}
                      className="w-full h-full object-contain"
                    />
                  </div>
                ))}
              </div>
              {meta.page_count > 1 && (
                <div className="flex justify-center items-center gap-4 mt-8 mb-12">
                  <button
                    onClick={handlePrevPage}
                    disabled={!meta.previous_page}
                    className={` ${
                      !meta.previous_page
                        ? 'cursor-not-allowed'
                        : 'cursor-pointer'
                    }`}>
                    <ArrowLeft2 size={20} color="#000" />
                  </button>

                  <div className="text-sm font-medium">
                    Page {meta.page} of {meta.page_count}
                  </div>

                  <button
                    onClick={handleNextPage}
                    disabled={!meta.next_page}
                    className={` ${
                      !meta.next_page ? 'cursor-not-allowed' : 'cursor-pointer'
                    }`}>
                    <ArrowRight2 size={20} color="#000" />
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
      <UploadIVModal
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        onUploadComplete={onNextStep}
      />
      <InvitationCardDetailsModal
        isOpen={isInvitationDetailsModalOpen}
        onClose={() => setIsInvitationDetailsModalOpen(false)}
        selectedTemplate={selectedTemplateForModal}
        onContinue={handleInvitationDetailsComplete}
      />
    </>
  );
};
