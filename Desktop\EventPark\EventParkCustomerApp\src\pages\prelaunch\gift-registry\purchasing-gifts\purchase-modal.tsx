import stackMoney from "../../../../assets/images/stack-money2.svg";

export const PurchaseModal = ({
  onClose,
  onProceedToPayment,
}: {
  onClose: () => void;
  onProceedToPayment: () => void;
}) => {
  return (
    <div className="fixed inset-0 bg-[#00002666]/40 flex items-center justify-center z-50 font-rethink">
      <div className="bg-white rounded-3xl w-full md:w-[522px] mx-4 relative">
        <div className="flex justify-center rounded-t-3xl mb-6 p-8 pt-[60px] bg-[#F5F6FE] h-[280px]">
          <div className="relative">
            <img src={stackMoney} alt="" className="w-[220px] h-auto" />
          </div>
        </div>

        {/* Modal Content */}
        <div className="px-8 pb-8 text-center">
          <h2 className="text-[28px] font-semibold text-grey-50 mb-4">
            Continue to Purchase?{" "}
          </h2>

          <p className="text-sm sm:text-base text-grey-250 mb-8 leading-relaxed max-w-[400px] mx-auto">
            Please note that payment for a gift reservation has to be made
            within 7 days after which the reservation would be cancelled, would
            you like to make payment now?
          </p>

          <div className=" flex gap-2 items-center">
            <button
              //   onClick={() => setShowPaymentMethods(true)}
              onClick={onProceedToPayment}
              className="w-full h-[48px] bg-[#4D55F2] text-white rounded-full font-semibold text-base hover:bg-[#4D55F2]/90 transition-all mb-0"
            >
              Yes pay now
            </button>

            <button
              onClick={onClose}
              className="w-full h-[48px] bg-[#FFE5E5] text-[#ff4d4d] rounded-full font-semibold text-base hover:bg-[#FFE5E5]/80 transition-all"
            >
              No, pay later
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
