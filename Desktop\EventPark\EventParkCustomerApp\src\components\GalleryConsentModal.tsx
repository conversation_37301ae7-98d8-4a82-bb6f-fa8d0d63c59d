import React from 'react';

interface GalleryConsentModalProps {
  open: boolean;
  onClose: () => void;
  onAgree: () => void;
  onDisagree: () => void;
}

interface RejectInvitationModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  onCancel: () => void;
}

export const GalleryConsentModal: React.FC<GalleryConsentModalProps> = ({
  open,
  onClose,
  onAgree,
  onDisagree,
}) => {
  if (!open) return null;
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 backdrop-blur-[8px]">
      <div className="relative bg-white rounded-2xl shadow-2xl flex flex-row-reverse w-full max-w-[734px] min-h-[360px]">
        {/* Close Icon */}
        <button
          className="absolute top-4 right-4 z-10"
          onClick={onClose}
          aria-label="Close">
          <img src="/icons/close-circle.svg" alt="close" className="w-8 h-8" />
        </button>
        {/* Left: Content */}
        <div className="flex flex-col justify-center px-10 py-8 gap-6 flex-1 min-w-[320px]">
          <div>
            <h2
              className="text-[28px] font-semibold leading-none mb-4 tracking-tight"
              style={{ letterSpacing: '-0.03em' }}>
              An Invitation to More!{' '}
            </h2>
            <p className="text-[#666] text-base leading-relaxed mb-2">
              By accepting this invitation, you also agree to receiving
              carefully curated event ideas, exclusive tips, and special offers
              sent straight to your inbox.
              <br />
              (No spam, just the good stuff!)
            </p>
          </div>
          <div className="flex gap-3 mt-2">
            <button
              className="bg-[#343cd8] hover:bg-[#4d55f2] text-white font-semibold rounded-full px-8 py-3 text-base shadow transition"
              onClick={onAgree}>
              I agree
            </button>
            <button
              className="bg-[#FDF2ED] hover:bg-[#ffe5e5] text-[#000026] font-semibold rounded-full px-8 py-3 text-base border border-[#FDF2ED] shadow transition"
              onClick={onDisagree}>
              I Disagree
            </button>
          </div>
        </div>
        {/* Right: Illustration */}
        <div className="hidden md:flex items-center justify-center bg-[#F5F9FF] rounded-r-2xl min-w-[270px] max-w-[270px] h-full min-h-[360px]">
          <img
            src="/icons/envelope-illustration.png"
            alt="envelope"
            className="w-[200px] h-[200px] object-contain"
          />
        </div>
      </div>
    </div>
  );
};

export const RejectInvitationModal: React.FC<RejectInvitationModalProps> = ({
  open,
  // onClose,
  onConfirm,
  onCancel,
}) => {
  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 backdrop-blur-[8px]">
      <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-[500px] mx-2 md:mx-4">
        {/* Close Icon */}
        {/* <button
          className="absolute top-4 right-4 z-10"
          onClick={onClose}
          aria-label="Close">
          <img src="/icons/close-circle.svg" alt="close" className="w-8 h-8" />
        </button> */}

        {/* Content */}
        <div className="flex flex-col px-4 md:px-8 py-10 gap-6">
          <div>
            <h2 className="text-[32px] font-semibold leading-tight mb-6 tracking-tight text-[#000026]">
              You are about to decline this Invitation
            </h2>
            <p className="text-[#666] text-base leading-relaxed">
              If you proceed, you'll be removed from the guestlist and lose
              access to the event, this action is irreversible please confirm
              you want to continue
            </p>
          </div>

          <div className="flex gap-4 mt-6">
            <button
              className=" bg-white hover:bg-gray-50 text-[#4d55f2] font-semibold rounded-full px-5 py-2.5 text-sm border-2 border-[#4d55f2] transition-colors"
              onClick={onCancel}>
              Cancel
            </button>
            <button
              className="bg-[#CC0000] hover:bg-[#CC0000]/90 text-white font-semibold rounded-full px-4 py-2.5 text-sm transition-colors"
              onClick={onConfirm}>
              Decline Invitation
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
