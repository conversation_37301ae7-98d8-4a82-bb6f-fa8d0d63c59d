/**
 * Utility functions for cache busting
 */

/**
 * Adds a cache buster parameter to a URL
 * @param url - The original URL
 * @param forceRefresh - Whether to force a fresh cache buster (default: false)
 * @returns URL with cache buster parameter
 */
export const addCacheBuster = (
  url: string,
  forceRefresh: boolean = false
): string => {
  if (!url) return url;

  const separator = url.includes("?") ? "&" : "?";
  const timestamp = forceRefresh ? Date.now() : Math.floor(Date.now() / 1000); // Use seconds for less aggressive busting
  return `${url}${separator}t=${timestamp}`;
};

/**
 * Adds a cache buster to image URLs specifically
 * @param imageUrl - The image URL
 * @param forceRefresh - Whether to force a fresh cache buster
 * @returns Image URL with cache buster
 */
export const bustImageCache = (
  imageUrl: string,
  forceRefresh: boolean = false
): string => {
  return addCacheBuster(imageUrl, forceRefresh);
};

/**
 * Preloads an image with cache busting to ensure it's fresh
 * @param imageUrl - The image URL to preload
 * @param forceRefresh - Whether to force a fresh cache buster
 * @returns Promise that resolves when image is loaded
 */
export const preloadImage = (
  imageUrl: string,
  forceRefresh: boolean = false
): Promise<void> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = () => reject(new Error("Failed to preload image"));
    img.src = bustImageCache(imageUrl, forceRefresh);
  });
};
