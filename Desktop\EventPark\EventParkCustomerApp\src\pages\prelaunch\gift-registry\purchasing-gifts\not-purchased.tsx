import { useState } from "react";
import { Button } from "../../../../components/button/onboardingButton";
import { useNavigate, useParams } from "react-router-dom";
import { GuestGiftsAPI } from "../../../../lib/apis/guestGiftsApi";
import { guestTokenManager } from "../../../../lib/utils/guestTokenManager";
import { toast } from "react-toastify";

export const NotPurchased = () => {
  const { eventId, giftId } = useParams();
  console.log(giftId);
  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  const navigate = useNavigate();

  const handleYesButNotNow = () => {
    // Navigate back to viewing gifts as guest
    navigate(`/guest/events/${eventId}/gifts`);
  };

  const handleCancelReservation = async () => {
    setIsCancelling(true);
    try {
      const accessToken = await guestTokenManager.getGuestAccessToken();
      if (!accessToken) {
        toast.error("Access token not found");
        return;
      }

      // Get the reservation ID from localStorage
      const reservationId = guestTokenManager.getGuestReservationId();
      if (!reservationId) {
        toast.error("Reservation not found");
        return;
      }

      // Cancel the reservation
      await GuestGiftsAPI.cancelReservation(reservationId);

      toast.success("Reservation cancelled successfully!");

      // Navigate back to viewing gifts
      navigate(`/guest/events/${eventId}/gifts`);
    } catch (error) {
      const errorMsg =
        typeof error === "object" && error && "message" in error
          ? (error as { message?: string }).message ??
            "Failed to cancel reservation"
          : "Failed to cancel reservation";
      toast.error(errorMsg);
    } finally {
      setIsCancelling(false);
    }
  };

  const handleBackToAllGifts = () => {
    navigate(`/guest/events/${eventId}/gifts`);
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="min-h-screen px-4  bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] flex items-center justify-center">
        {!showCancelConfirmation && (
          <div className="bg-white rounded-2xl px-6 py-13 text-center w-full max-w-[450px] shadow-[0px_12px_120px_0px_#5F5F5F0F]">
            <div className=" max-w-[306px] mx-auto">
              <p className="text-dark-blue text-lg md:text-3xl font-medium">
                Do you still intend to <br /> purchase this gift?
              </p>
              <Button
                variant="primary"
                size="md"
                onClick={handleYesButNotNow}
                className={`text-white font-semibold w-full mb-8 mt-16 bg-primary-650 `}
              >
                Yes, but not right now{" "}
              </Button>
              <Button
                variant="primary"
                size="md"
                onClick={() => setShowCancelConfirmation(true)}
                className={` w-full mb-9 font-semibold bg-cus-pink-50 `}
              >
                No, Cancel my reservation
              </Button>

              <button
                onClick={handleBackToAllGifts}
                className="text-cus-orange-100 italic mx-auto w-full underline text-base font-bold"
              >
                Back to All Gifts{" "}
              </button>
            </div>
          </div>
        )}

        {showCancelConfirmation && (
          <div className="bg-white rounded-2xl px-6 py-13 text-center w-full max-w-[450px] shadow-[0px_12px_120px_0px_#5F5F5F0F]">
            <div className=" max-w-[378px] mx-auto">
              <p className="text-dark-blue text-lg md:text-3xl font-medium">
                Cancel your Reservation
              </p>
              <p className="text-grey-500 text-sm md:text-base mt-4">
                This means that the recipient will no longer see the gift as
                reserved and the item would be available again for purchase
              </p>
              <Button
                variant="primary"
                size="md"
                onClick={handleCancelReservation}
                disabled={isCancelling}
                className={`w-full max-w-[306px] mx-auto mt-14 bg-cus-pink-50 disabled:opacity-50 disabled:cursor-not-allowed`}
              >
                {isCancelling ? "Cancelling..." : "Cancel my reservation"}
              </Button>
              <button
                onClick={() => setShowCancelConfirmation(false)}
                className="mt-4 text-cus-orange-100 italic mx-auto w-full underline text-base font-bold"
              >
                Don't Cancel
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
