import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { motion } from "framer-motion";
// import { ArrowRight } from "iconsax-react";
import successGIF from "../../assets/animations/gift.gif";
import { events } from "../../lib/services/events";
import { useEventStore } from "../../lib/store/event";
import { toast } from "react-toastify";

interface EventSuccessProps {
  onClose?: () => void;
  eventName?: string;
  eventDate?: string;
  eventTime?: string;
  eventImage?: string;
}

interface EventData {
  id: string;
  title: string;
  date_from: string;
  date_to: string;
  banner_preview_url?: string;
  description?: string;
}

export const EventSuccess: React.FC<EventSuccessProps> = ({ onClose }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { setSelectedEvent, setCreatedEventData } = useEventStore();
  const [eventData, setEventData] = useState<EventData | null>(null);
  const [loading, setLoading] = useState(true);

  // Get event ID from location state
  const eventId = location.state?.eventId;
  const eventName = location.state?.eventName;

  // Fetch event data when component mounts
  useEffect(() => {
    const fetchEventData = async () => {
      if (!eventId) {
        toast.error("Event ID not found");
        setLoading(false);
        return;
      }

      try {
        const response = await events.getEventByID(eventId);
        if (response?.data) {
          setEventData(response.data);
          setCreatedEventData(response.data);
          setSelectedEvent(response.data);
        }
      } catch (error) {
        console.error("Failed to fetch event data:", error);
        toast.error("Failed to load event details");
      } finally {
        setLoading(false);
      }
    };

    fetchEventData();
  }, [eventId, setCreatedEventData, setSelectedEvent]);

  useEffect(() => {
    // Prevent background scrolling when modal is mounted
    document.body.style.position = "fixed";
    document.body.style.width = "100%";
    document.body.style.overflow = "hidden";

    // Cleanup when modal is unmounted
    return () => {
      document.body.style.position = "";
      document.body.style.width = "";
      document.body.style.overflow = "";
    };
  }, []);

  const handleGoToDashboard = () => {
    if (onClose) {
      onClose();
    }
    navigate("/");
  };

  // Show loading state while fetching event data
  if (loading) {
    return (
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="min-h-screen flex items-center justify-center relative">
          <div className="bg-white rounded-[20px] p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#4D55F2] mx-auto mb-4"></div>
            <p className="text-gray-600">Loading event details...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="min-h-screen flex items-center justify-center relative">
        {/* Background GIF */}
        <img
          src={successGIF}
          alt="success animation"
          className="w-full transition-all h-[469px] opacity-40 absolute object-cover top-0 left-0 right-0"
        />

        {/* Main Content */}
        <div className="relative w-full max-w-[450px] mx-auto">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="relative z-20 bg-white rounded-[20px] text-center shadow-[0px_12px_120px_0px_#5F5F5F0F] overflow-hidden"
          >
            {/* Top Section with Event Preview */}
            <div className="bg-[linear-gradient(180deg,_#FDEFE9_0%,_#FEF7F4_100%)] h-[262px] w-full relative ">
              {/* Event Preview Card */}
              <div className="absolute left-1/2 top-[123px] -translate-x-1/2 rotate-[-5deg]">
                <div className="relative">
                  <div className="bg-[#FEF7F4] border-6 border-white rounded-[17.52px] w-[171.77px] h-[176.21px] shadow-[0px_24.64px_24.64px_0px_#A6A6A60A,0px_49.28px_61.6px_0px_#A6A6A61A,0px_28.97px_144.87px_0px_#0000000F] backdrop-blur-[17.52px]">
                    <div className="overflow-hidden rounded-[17.52px] w-full h-full">
                      <img
                        src={
                          eventData?.banner_preview_url ||
                          "/src/assets/images/wed.png"
                        }
                        alt="Event preview"
                        className="w-full h-[107.59px] object-cover"
                      />
                      <div className="p-4 text-left">
                        <p className="font-bold text-base text-[#000000] truncate">
                          {eventData?.title || eventName || "Your Event"}
                        </p>
                        <div className="text-xs text-[#808080] mt-0.5">
                          {eventData?.date_from
                            ? new Date(eventData.date_from).toLocaleDateString(
                                "en-US",
                                {
                                  day: "numeric",
                                  month: "short",
                                  year: "numeric",
                                }
                              )
                            : "Event Date"}{" "}
                          •{" "}
                          {eventData?.date_from
                            ? new Date(eventData.date_from).toLocaleTimeString(
                                "en-US",
                                {
                                  hour: "numeric",
                                  minute: "2-digit",
                                  hour12: true,
                                }
                              )
                            : "Event Time"}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Content Section */}
            <div className="flex flex-col items-center text-center py-8 px-6 mt-6 w-full">
              <div className="max-w-[335px] w-full">
                <h2 className="text-[28px] font-medium mb-4 text-[#000026] font-['Rethink_Sans'] tracking-[-0.035em] leading-[1.25]">
                  Your Event has <br />
                  <span className="text-[#808080]">
                    been created Successfully!
                  </span>
                </h2>
                <p className="text-[16px] text-[#808080] font-normal mb-8 font-['Rethink_Sans'] tracking-[-0.03em] leading-[1.6]">
                  Your Event details has been saved
                  <br />
                  Please proceed to curate a guestlist and a create gift
                  registry on the Event Dashboard
                </p>
              </div>

              {/* Go to Dashboard Button */}
              <button
                onClick={handleGoToDashboard}
                className="bg-[#343CD8] cursor-pointer text-base  max-w-[226px] text-white py-3 px-6 font-semibold rounded-full hover:bg-[#343CD8]/90 transition-colors shadow-[0px_1px_2px_0px_rgba(10,13,18,0.05)] flex items-center justify-center gap-2"
              >
                <span>Go to Dashboard</span>
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    opacity="0.4"
                    d="M10.0013 18.3327C14.6037 18.3327 18.3346 14.6017 18.3346 9.99935C18.3346 5.39698 14.6037 1.66602 10.0013 1.66602C5.39893 1.66602 1.66797 5.39698 1.66797 9.99935C1.66797 14.6017 5.39893 18.3327 10.0013 18.3327Z"
                    fill="white"
                  />
                  <path
                    d="M13.357 9.5582L10.857 7.0582C10.6154 6.81654 10.2154 6.81654 9.9737 7.0582C9.73203 7.29987 9.73203 7.69987 9.9737 7.94154L11.407 9.37487H7.08203C6.74036 9.37487 6.45703 9.6582 6.45703 9.99987C6.45703 10.3415 6.74036 10.6249 7.08203 10.6249H11.407L9.9737 12.0582C9.73203 12.2999 9.73203 12.6999 9.9737 12.9415C10.0987 13.0665 10.257 13.1249 10.4154 13.1249C10.5737 13.1249 10.732 13.0665 10.857 12.9415L13.357 10.4415C13.5987 10.1999 13.5987 9.79987 13.357 9.5582Z"
                    fill="white"
                  />
                </svg>
              </button>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};
