// import React from 'react';
// import { useTokenRefresh } from '../../lib/hooks/useTokenRefresh';
// import { useUserAuthStore } from '../../lib/store/auth';
// import { EventParkAPI } from '../../lib/event-park-api';
// import { toast } from 'react-toastify';

// /**
//  * Example component demonstrating token refresh functionality
//  * This is for demonstration purposes only - remove in production
//  */
// export const TokenRefreshExample: React.FC = () => {
//   const { refreshAccessToken, isRefreshing, canRefresh, refreshError } =
//     useTokenRefresh();
//   const { userAppToken, refreshToken } = useUserAuthStore();

//   const handleManualRefresh = async () => {
//     if (!canRefresh) {
//       toast.error('No access token available for refresh');
//       return;
//     }

//     try {
//       const newToken = await refreshAccessToken();
//       toast.success('Token refreshed successfully!');
//       console.log('New access token:', newToken);
//     } catch (error) {
//       toast.error('Failed to refresh token');
//       console.error('Refresh error:', error);
//     }
//   };

//   const handleTestApiCall = async () => {
//     try {
//       // This will automatically refresh the token if needed
//       const response = await EventParkAPI().get('/v1/user');
//       toast.success('API call successful!');
//       console.log('User data:', response.data);
//     } catch (error) {
//       toast.error('API call failed');
//       console.error('API error:', error);
//     }
//   };

//   const handleTestExpiredToken = async () => {
//     try {
//       // Simulate an API call that might trigger token refresh
//       const response = await EventParkAPI().get('/v1/user/events');
//       toast.success('Events fetched successfully!');
//       console.log('Events:', response.data);
//     } catch (error) {
//       toast.error('Failed to fetch events');
//       console.error('Events error:', error);
//     }
//   };

//   return (
//     <div className="p-6 bg-white rounded-lg shadow-md max-w-md mx-auto">
//       <h3 className="text-lg font-semibold mb-4">Token Refresh Demo</h3>

//       <div className="space-y-4">
//         {/* Token Status */}
//         <div className="text-sm">
//           <p>
//             <strong>Access Token:</strong>{' '}
//             {userAppToken ? '✅ Present' : '❌ Missing'}
//           </p>
//           <p>
//             <strong>Refresh Token:</strong>{' '}
//             {refreshToken ? '✅ Present' : '❌ Missing'}
//           </p>
//           <p>
//             <strong>Can Refresh:</strong>{' '}
//             {canRefresh
//               ? '✅ Yes (has access token)'
//               : '❌ No (no access token)'}
//           </p>
//           <p>
//             <strong>Is Refreshing:</strong> {isRefreshing ? '🔄 Yes' : '✅ No'}
//           </p>
//         </div>

//         {/* Error Display */}
//         {refreshError && (
//           <div className="text-red-600 text-sm">
//             <strong>Refresh Error:</strong> {refreshError.message}
//           </div>
//         )}

//         {/* Action Buttons */}
//         <div className="space-y-2">
//           <button
//             onClick={handleManualRefresh}
//             disabled={isRefreshing || !canRefresh}
//             className="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed">
//             {isRefreshing ? 'Refreshing...' : 'Manual Token Refresh'}
//           </button>

//           <button
//             onClick={handleTestApiCall}
//             className="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
//             Test API Call (Auto Refresh)
//           </button>

//           <button
//             onClick={handleTestExpiredToken}
//             className="w-full px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
//             Test Events API (Auto Refresh)
//           </button>
//         </div>

//         {/* Instructions */}
//         <div className="text-xs text-gray-600 mt-4">
//           <p>
//             <strong>Instructions:</strong>
//           </p>
//           <ul className="list-disc list-inside space-y-1">
//             <li>Manual refresh: Explicitly refresh the access token</li>
//             <li>API calls: Will automatically refresh if token is expired</li>
//             <li>Check browser console for detailed logs</li>
//             <li>Watch network tab to see refresh requests</li>
//           </ul>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default TokenRefreshExample;
