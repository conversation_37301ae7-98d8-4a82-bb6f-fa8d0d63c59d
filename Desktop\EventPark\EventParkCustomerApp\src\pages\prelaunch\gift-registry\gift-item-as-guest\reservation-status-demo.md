# Guest Gift Reservation Status Implementation

## Overview
This implementation adds reservation status indicators to the guest gift viewing interface, preventing users from attempting to reserve fully reserved gifts.

## Key Features Implemented

### 1. API Updates
- Added `total_reservations` field to both `CashGift` and `ItemGift` interfaces
- Added `total_contributions` and `total_contributors` fields to `CashGift` interface
- Created new `getItemGift(giftId: string)` API function for fetching individual item gifts

### 2. Reservation Status Logic
- **Item Gifts**: Fully reserved when `total_reservations >= quantity`
- **Cash Gifts**: Fully reserved when `total_reservations > 0` AND `is_crowd_gift = false`
- **Crowd Gifts**: Never fully reserved (can accept multiple contributions)

### 3. Visual Indicators

#### Item Gifts
- **Reservation Counter**: Shows "X/Y reserved" for items with quantity > 1
- **Fully Reserved Badge**: Red overlay on image with "Fully Reserved" text
- **Opacity Reduction**: Fully reserved items appear with 60% opacity
- **Cursor Change**: Fully reserved items show "not-allowed" cursor

#### Cash Gifts
- **Reserved Overlay**: Red overlay on image with "Reserved" text for non-crowd gifts
- **Crowd Gift Stats**: Shows contributor count and total raised amount
- **Button State**: "Send Cashgift" becomes "Reserved" and is disabled
- **Status Badges**: "Already Reserved" indicator for reserved non-crowd gifts

### 4. User Interaction Prevention
- **Toast Messages**: Informative messages when users try to interact with reserved gifts
- **Navigation Blocking**: Prevents navigation to reservation flows for fully reserved items
- **Button Disabling**: Disables action buttons for reserved gifts

## Code Examples

### Reservation Status Check Functions
```typescript
// Check if item gift is fully reserved
const isItemGiftFullyReserved = (item: ItemGift) => {
  return item.total_reservations >= item.quantity;
};

// Check if cash gift is fully reserved (for non-crowd gifts)
const isCashGiftFullyReserved = (cashGift: CashGift) => {
  return !cashGift.is_crowd_gift && cashGift.total_reservations > 0;
};
```

### Visual Indicator Implementation
```typescript
// Item gift with reservation status
{isFullyReserved && (
  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
    <div className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
      Fully Reserved
    </div>
  </div>
)}

// Reservation counter for items with multiple quantities
{item.quantity > 1 && (
  <div className="flex items-center gap-1 bg-gray-100 px-2 py-1 rounded-full">
    <span className="text-xs text-gray-600">
      {item.total_reservations}/{item.quantity} reserved
    </span>
  </div>
)}
```

### User Interaction Prevention
```typescript
// Handle item gift click with reservation check
const handleItemGiftClick = (gift: ItemGift) => {
  if (isItemGiftFullyReserved(gift)) {
    toast.info("This gift is fully reserved");
    return;
  }
  // Continue with normal flow...
};
```

## API Endpoints Used
- `GET /v1/guest/events/gifts/cash/{id}` - Fetch individual cash gift with metrics
- `GET /v1/guest/events/gifts/item/{id}` - Fetch individual item gift with reservation data
- `GET /v1/guest/events/{id}/gifts/cash` - List cash gifts with reservation counts
- `GET /v1/guest/events/{id}/gifts/item` - List item gifts with reservation counts

## Testing
The implementation includes comprehensive test coverage for:
- Reservation status detection
- Visual indicator rendering
- User interaction prevention
- Toast message display
- Crowd gift contribution display

## Benefits
1. **Better UX**: Users immediately see which gifts are available
2. **Prevents Errors**: Stops users from attempting impossible reservations
3. **Clear Feedback**: Visual and textual indicators provide clear status
4. **Responsive Design**: Works across all device sizes
5. **Accessibility**: Uses semantic HTML and proper contrast ratios
