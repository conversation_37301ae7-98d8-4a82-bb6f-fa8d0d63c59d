/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect } from "react";
import upload from "../../assets/images/upload.png";
import { AuthServices } from "../../lib/services/auth";
import { useEventStore } from "../../lib/store/event";
import { useEventManagement } from "../../lib/hooks/useEventManagement";
import { events } from "../../lib/services/events";
import { toast } from "react-toastify";

interface UploadIVModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUploadComplete?: () => void;
}

export const UploadIVModal = ({
  isOpen,
  onClose,
  onUploadComplete,
}: UploadIVModalProps) => {
  const { selectedEvent } = useEventStore();
  const { updateEventOptimistically } = useEventManagement();
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    const files = e.dataTransfer.files;
    if (files && files[0]) {
      handleFile(files[0]);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files[0]) {
      handleFile(files[0]);
    }
  };

  const handleFile = (file: File) => {
    if (file) {
      setUploadedFile(file);
      const fileUrl = URL.createObjectURL(file);
      setPreviewUrl(fileUrl);
    }
  };

  const handleUploadClick = async () => {
    if (!uploadedFile || !selectedEvent?.id) {
      toast.error("Please select a file and ensure an event is selected");
      return;
    }

    setIsUploading(true);
    try {
      const response = await AuthServices.uploadFiles(
        uploadedFile,
        "iv_design",
        selectedEvent.id
      );

      if (response && response.data) {
        // Fetch the updated event data to get the latest iv_preview_url
        try {
          const updatedEventResponse = await events.getEventByID(
            selectedEvent.id
          );
          const completeEventData = updatedEventResponse.data;

          // Transform CompleteEventData to CreatedEventData format for the store
          const updatedEvent = {
            id: completeEventData.id,
            title: completeEventData.title,
            category_id: completeEventData.category_id,
            category_name: completeEventData.category_name,
            date_from: completeEventData.date_from,
            date_to: completeEventData.date_to,
            description: completeEventData.description,
            location_address: completeEventData.location_address,
            gift_registry_title: completeEventData.gift_registry_title || "",
            delivery_address: completeEventData.delivery_address || "",
            banner_image_id: completeEventData.banner_image_id,
            banner_preview_url: completeEventData.banner_preview_url,
            visibility: completeEventData.visibility,
            invite_link: completeEventData.invite_link,
            iv_preview_url: completeEventData.iv_preview_url,
            status: completeEventData.event_status,
            user_id: completeEventData.user_id,
            created_at: completeEventData.created_at,
            updated_at: completeEventData.updated_at,
          };

          // Update the selectedEvent with the latest data
          updateEventOptimistically(updatedEvent);
        } catch (error) {
          console.error("Failed to refresh event data:", error);
        }

        toast.success("IV design uploaded successfully!");
        onClose();
        if (onUploadComplete) {
          onUploadComplete();
        }
      } else {
        throw new Error("Upload response was invalid");
      }
    } catch (error: any) {
      toast.error(
        error?.response?.data?.message || "Failed to upload IV design"
      );
    } finally {
      setIsUploading(false);
    }
  };

  const triggerFileInput = () => {
    document.getElementById("file-upload")?.click();
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
      onClick={onClose}
    >
      <div
        className="bg-white pt-2 px-2  rounded-3xl "
        onClick={(e) => e.stopPropagation()}
      >
        <div className="">
          <div
            onClick={triggerFileInput}
            onDragEnter={handleDrag}
            onDragOver={handleDrag}
            onDragLeave={handleDrag}
            onDrop={handleDrop}
          >
            {previewUrl ? (
              <div className="relative w-80 sm:w-96 h-[440px]  ">
                <div className="relative w-full h-full overflow-hidden rounded-xl">
                  <img
                    src={previewUrl}
                    alt="Uploaded IV"
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute bottom-0 left-0 right-0 h-1/3 bg-gradient-to-t from-black/40 to-transparent" />
                </div>

                <div className="absolute bottom-5 left-22 flex justify-center">
                  <div className="px-3.5 py-2 cursor-pointer rounded-full backdrop-blur-lg bg-white/80 shadow-glass transition-all duration-300 group-hover:shadow-glass-hover">
                    <p className="text-primary text-sm font-bold italic">
                      Click to change your IV
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="w-full h-full relative">
                <img
                  src={upload}
                  alt="Upload"
                  className="w-80 md:w-full h-[440px]"
                />

                <div className="absolute bottom-5 left-22 flex justify-center">
                  <div className="px-3.5 py-2 cursor-pointer rounded-full backdrop-blur-lg bg-white/80 shadow-glass transition-all duration-300 group-hover:shadow-glass-hover">
                    <p className="text-primary text-sm font-bold italic">
                      Upload your IV
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
          <input
            type="file"
            className="hidden"
            id="file-upload"
            accept="image/*"
            onChange={handleFileChange}
          />
        </div>

        <button
          onClick={handleUploadClick}
          disabled={!uploadedFile || isUploading}
          className={`w-full text-white text-base ${
            !uploadedFile || isUploading
              ? "opacity-40 cursor-default"
              : "cursor-pointer "
          } mt-8.5 font-bold py-3 bg-primary mb-4.5 rounded-full `}
        >
          {isUploading ? "Uploading..." : "Upload your IV Design"}
        </button>
      </div>
    </div>
  );
};
