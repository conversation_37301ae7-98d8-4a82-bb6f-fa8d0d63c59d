import { ArrowRight, Bank } from 'iconsax-react';
import { useState } from 'react';
import { TextInput } from '../../../components/inputs/text-input/text-input';

interface AccountDetailsProps {
  onNextStep: (data: { bank: string; accountNumber: string }) => void;
  initialData?: { bank?: string; accountNumber?: string };
}

export const AccountDetails = ({
  onNextStep,
  initialData = {},
}: AccountDetailsProps) => {
  const [bank, setBank] = useState(initialData.bank || '');
  const [accountNumber, setAccountNumber] = useState(
    initialData.accountNumber || ''
  );

  const handleContinue = () => {
    onNextStep({
      bank,
      accountNumber,
    });
  };

  return (
    <div className="px-4 md:px-0 md:ml-3.5">
      <div className="max-w-[560px] w-full mx-auto mt-[43px]">
        <div className="bg-cus-pink flex items-center gap-3 p-3.5 mb-6 rounded-2xl">
          <Bank color="#F7BFA9" size={56} variant="Bulk" />
          <p className="italic text-dark-blue-500 font-medium text-sm">
            Add your bank details so guests can easily contribute cash if
            <br /> they prefer to gift money instead of purchasing an item.
          </p>
        </div>
        <div className="mb-6">
          <label className="block text-grey-500 font-medium text-sm mb-2">
            Bank
          </label>
          <select
            value={bank}
            onChange={(e) => setBank(e.target.value)}
            className="w-full h-[44px] px-3.5 border border-gray-300 rounded-full text-base font-bold text-grey-50 italic outline-0">
            <option value="">Select Bank</option>
            <option value="access">Access Bank</option>
            <option value="gtb">GTBank</option>
            <option value="first">First Bank</option>
          </select>
        </div>
        <div className="mb-6">
          <TextInput
            id="accountNumber"
            label="Account Number"
            value={accountNumber}
            onChange={(e) => setAccountNumber(e.target.value)}
            placeholder="e.g **********"
            className="text-grey-50 font-bold italic placeholder:font-normal placeholder:text-grey-700"
          />
        </div>

        <button
          onClick={handleContinue}
          disabled={!bank || !accountNumber}
          className={`bg-primary-650 text-white py-2.5 px-4 mb-24 rounded-full cursor-pointer flex items-center gap-2 ${
            bank && accountNumber ? '' : 'opacity-50 cursor-not-allowed'
          }`}>
          Continue
          <div className="bg-white/30 rounded-full p-0.5">
            <ArrowRight size="12" color="#fff" />
          </div>
        </button>
      </div>
    </div>
  );
};
