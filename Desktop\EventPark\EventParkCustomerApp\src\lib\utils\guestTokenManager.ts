/**
 * Guest Token Management Utility
 * Handles localStorage operations for guest access tokens separately from authenticated user tokens
 * Enhanced with refresh token support and automatic token refresh capabilities
 */

import moment from "moment";
import axios from "axios";
import { toast } from "react-toastify";

const GUEST_TOKEN_KEY = "guest-access-token";
const GUEST_EVENT_ID_KEY = "guest-event-id";
const GUEST_RESERVATION_ID_KEY = "guest-reservation-id";
const GUEST_REFRESH_TOKEN_KEY = "guest-refresh-token";
const GUEST_DATA_KEY = "guest-data";

// Legacy interface for backward compatibility
export interface GuestTokenData {
  access_token: string;
  expires_at: string;
  event_id?: string;
}

// Enhanced interface with refresh token support
export interface EnhancedGuestTokenData {
  access_token: string;
  access_token_expires_at: string;
  refresh_token: string;
  refresh_token_expires_at: string;
  guest_data?: {
    created_at: string;
    email: string;
    first_name: string;
    id: string;
    last_name: string;
    phone_number: string;
  };
  event_id?: string;
  // Legacy field for backward compatibility
  expires_at?: string;
}

// API Response interfaces
export interface GuestTokenRefreshResponse {
  access_token: string;
  access_token_expires_at: string;
  guest_data: {
    created_at: string;
    email: string;
    first_name: string;
    id: string;
    last_name: string;
    phone_number: string;
  };
  refresh_token: string;
  refresh_token_expires_at: string;
}

// Track if token refresh is in progress
let isRefreshing = false;
let refreshPromise: Promise<string> | null = null;

// Helper function to check if token should be refreshed (refresh if expires in next 5 minutes)
const shouldRefreshToken = (expiresAt: string | null): boolean => {
  if (!expiresAt) return true;
  const expiryTime = moment.utc(expiresAt);
  const now = moment.utc();
  const fiveMinutesFromNow = now.clone().add(5, "minutes");
  return expiryTime.isSameOrBefore(fiveMinutesFromNow);
};

// Helper function to check if token is expired
const isTokenExpired = (expiresAt: string | null): boolean => {
  if (!expiresAt) return true;
  const expiryTime = moment.utc(expiresAt);
  return expiryTime.isSameOrBefore(moment.utc());
};

// Token refresh function
const refreshGuestToken = async (): Promise<string> => {
  const refreshToken = localStorage.getItem(GUEST_REFRESH_TOKEN_KEY);
  const baseURL = import.meta.env.VITE_API_BASE_URL;

  if (!refreshToken) {
    throw new Error("No refresh token available");
  }

  // Get the current (expired) access token for the Authorization header
  const currentTokenData = localStorage.getItem(GUEST_TOKEN_KEY);
  let currentAccessToken = null;
  if (currentTokenData) {
    try {
      const parsed = JSON.parse(currentTokenData);
      currentAccessToken = parsed.access_token;
    } catch (error) {
      console.error("Failed to parse current token data:", error);
    }
  }

  // Check if refresh token is expired
  const refreshTokenExpiresAt = localStorage.getItem(
    "guest-refresh-token-expires-at"
  );
  if (isTokenExpired(refreshTokenExpiresAt)) {
    throw new Error("Refresh token is expired");
  }

  try {
    console.log("🔄 Refreshing guest token...");
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    // Add Authorization header with current (expired) access token if available
    if (currentAccessToken) {
      headers.Authorization = `Bearer ${currentAccessToken}`;
      console.log("🔑 Using expired access token for refresh authorization");
    }

    const response = await axios.post<GuestTokenRefreshResponse>(
      `${baseURL}/v1/guest/reservations/tokens/access/refresh`,
      { refresh_token: refreshToken },
      {
        timeout: 30000,
        headers,
      }
    );

    const {
      access_token,
      access_token_expires_at,
      refresh_token: newRefreshToken,
      refresh_token_expires_at,
      guest_data,
    } = response.data;

    // Store new tokens
    const enhancedTokenData: EnhancedGuestTokenData = {
      access_token,
      access_token_expires_at,
      refresh_token: newRefreshToken,
      refresh_token_expires_at,
      guest_data,
      expires_at: access_token_expires_at, // For backward compatibility
    };

    guestTokenManager.setEnhancedGuestToken(enhancedTokenData);
    console.log("✅ Guest token refreshed successfully");

    return access_token;
  } catch (error) {
    console.error("❌ Guest token refresh failed:", error);
    // Clear tokens on refresh failure
    guestTokenManager.clearGuestToken();
    toast.error("Session expired. Please access again.");
    throw error;
  }
};

export const guestTokenManager = {
  /**
   * Store guest access token in localStorage (legacy method)
   */
  setGuestToken: (tokenData: GuestTokenData): void => {
    try {
      localStorage.setItem(GUEST_TOKEN_KEY, JSON.stringify(tokenData));
      if (tokenData.event_id) {
        localStorage.setItem(GUEST_EVENT_ID_KEY, tokenData.event_id);
      }
    } catch (error) {
      console.error("Failed to store guest token:", error);
    }
  },

  /**
   * Store enhanced guest token data with refresh token support
   */
  setEnhancedGuestToken: (tokenData: EnhancedGuestTokenData): void => {
    try {
      // Store access token data (maintaining legacy format for compatibility)
      const legacyTokenData: GuestTokenData = {
        access_token: tokenData.access_token,
        expires_at: tokenData.access_token_expires_at,
        event_id: tokenData.event_id,
      };
      localStorage.setItem(GUEST_TOKEN_KEY, JSON.stringify(legacyTokenData));

      // Store refresh token and related data separately
      localStorage.setItem(GUEST_REFRESH_TOKEN_KEY, tokenData.refresh_token);
      localStorage.setItem(
        "guest-access-token-expires-at",
        tokenData.access_token_expires_at
      );
      localStorage.setItem(
        "guest-refresh-token-expires-at",
        tokenData.refresh_token_expires_at
      );

      if (tokenData.guest_data) {
        localStorage.setItem(
          GUEST_DATA_KEY,
          JSON.stringify(tokenData.guest_data)
        );
      }

      if (tokenData.event_id) {
        localStorage.setItem(GUEST_EVENT_ID_KEY, tokenData.event_id);
      }
    } catch (error) {
      console.error("Failed to store enhanced guest token:", error);
    }
  },

  /**
   * Get guest access token from localStorage
   */
  getGuestToken: (): GuestTokenData | null => {
    try {
      const tokenData = localStorage.getItem(GUEST_TOKEN_KEY);
      if (!tokenData) return null;

      const parsed = JSON.parse(tokenData);

      // Check if token is expired
      if (parsed.expires_at && new Date(parsed.expires_at) <= new Date()) {
        guestTokenManager.clearGuestToken();
        return null;
      }

      return parsed;
    } catch (error) {
      console.error("Failed to get guest token:", error);
      return null;
    }
  },

  /**
   * Get just the access token string with automatic refresh
   */
  getGuestAccessToken: async (): Promise<string | null> => {
    try {
      const tokenData = guestTokenManager.getGuestToken();
      if (!tokenData) return null;

      const accessTokenExpiresAt =
        localStorage.getItem("guest-access-token-expires-at") ||
        tokenData.expires_at;

      // Check if we should refresh the token (expires in next 5 minutes)
      if (shouldRefreshToken(accessTokenExpiresAt)) {
        // Check if we have a refresh token
        const refreshToken = localStorage.getItem(GUEST_REFRESH_TOKEN_KEY);
        if (refreshToken) {
          // If already refreshing, wait for the existing refresh
          if (isRefreshing && refreshPromise) {
            return await refreshPromise;
          }

          // Start refresh process
          if (!isRefreshing) {
            isRefreshing = true;
            refreshPromise = refreshGuestToken();

            try {
              const newAccessToken = await refreshPromise;
              return newAccessToken;
            } catch (error) {
              console.error("Token refresh failed:", error);
              return null;
            } finally {
              isRefreshing = false;
              refreshPromise = null;
            }
          }
        }
      }

      return tokenData.access_token;
    } catch (error) {
      console.error("Failed to get guest access token:", error);
      return null;
    }
  },

  /**
   * Get just the access token string (synchronous, legacy method)
   */
  getGuestAccessTokenSync: (): string | null => {
    const tokenData = guestTokenManager.getGuestToken();
    return tokenData?.access_token || null;
  },

  /**
   * Clear guest access token from localStorage
   */
  clearGuestToken: (): void => {
    try {
      localStorage.removeItem(GUEST_TOKEN_KEY);
      localStorage.removeItem(GUEST_REFRESH_TOKEN_KEY);
      localStorage.removeItem("guest-access-token-expires-at");
      localStorage.removeItem("guest-refresh-token-expires-at");
      localStorage.removeItem(GUEST_DATA_KEY);
      localStorage.removeItem(GUEST_EVENT_ID_KEY);
      localStorage.removeItem(GUEST_RESERVATION_ID_KEY);

      // Reset refresh state
      isRefreshing = false;
      refreshPromise = null;
    } catch (error) {
      console.error("Failed to clear guest token:", error);
    }
  },

  /**
   * Check if guest token exists and is valid (enhanced with refresh token check)
   */
  hasValidGuestToken: (): boolean => {
    const tokenData = guestTokenManager.getGuestToken();
    if (!tokenData) return false;

    // If token is expired but we have a refresh token, consider it valid
    const accessTokenExpiresAt =
      localStorage.getItem("guest-access-token-expires-at") ||
      tokenData.expires_at;
    if (isTokenExpired(accessTokenExpiresAt)) {
      return guestTokenManager.hasRefreshToken();
    }

    return true;
  },

  /**
   * Check if guest token exists and is valid (synchronous, legacy method)
   */
  hasValidGuestTokenSync: (): boolean => {
    return guestTokenManager.getGuestToken() !== null;
  },

  /**
   * Check if guest has refresh token capability
   */
  hasRefreshToken: (): boolean => {
    const refreshToken = localStorage.getItem(GUEST_REFRESH_TOKEN_KEY);
    const refreshTokenExpiresAt = localStorage.getItem(
      "guest-refresh-token-expires-at"
    );
    return !!(refreshToken && !isTokenExpired(refreshTokenExpiresAt));
  },

  /**
   * Get guest data if available
   */
  getGuestData: (): {
    created_at: string;
    email: string;
    first_name: string;
    id: string;
    last_name: string;
    phone_number: string;
  } | null => {
    try {
      const guestData = localStorage.getItem(GUEST_DATA_KEY);
      return guestData ? JSON.parse(guestData) : null;
    } catch (error) {
      console.error("Failed to get guest data:", error);
      return null;
    }
  },

  /**
   * Force refresh the guest token (for manual refresh)
   */
  forceRefreshToken: async (): Promise<string | null> => {
    try {
      if (!guestTokenManager.hasRefreshToken()) {
        throw new Error("No refresh token available");
      }

      isRefreshing = true;
      refreshPromise = refreshGuestToken();

      const newAccessToken = await refreshPromise;
      return newAccessToken;
    } catch (error) {
      console.error("Force refresh failed:", error);
      return null;
    } finally {
      isRefreshing = false;
      refreshPromise = null;
    }
  },

  /**
   * Store event ID for guest session
   */
  setGuestEventId: (eventId: string): void => {
    try {
      localStorage.setItem(GUEST_EVENT_ID_KEY, eventId);
    } catch (error) {
      console.error("Failed to store guest event ID:", error);
    }
  },

  /**
   * Get event ID for guest session
   */
  getGuestEventId: (): string | null => {
    try {
      return localStorage.getItem(GUEST_EVENT_ID_KEY);
    } catch (error) {
      console.error("Failed to get guest event ID:", error);
      return null;
    }
  },

  /**
   * Store reservation ID for guest session
   */
  setGuestReservationId: (reservationId: string): void => {
    try {
      localStorage.setItem(GUEST_RESERVATION_ID_KEY, reservationId);
    } catch (error) {
      console.error("Failed to store guest reservation ID:", error);
    }
  },

  /**
   * Get reservation ID for guest session
   */
  getGuestReservationId: (): string | null => {
    try {
      return localStorage.getItem(GUEST_RESERVATION_ID_KEY);
    } catch (error) {
      console.error("Failed to get guest reservation ID:", error);
      return null;
    }
  },

  /**
   * Clear reservation ID from guest session
   */
  clearGuestReservationId: (): void => {
    try {
      localStorage.removeItem(GUEST_RESERVATION_ID_KEY);
    } catch (error) {
      console.error("Failed to clear guest reservation ID:", error);
    }
  },

  /**
   * Determine payment status from reservation data
   */
  getPaymentStatus: (reservation: {
    payment_initiated: boolean;
    payment_made: boolean;
    collection_transaction_id?: string | null;
  }): "not_started" | "processing" | "completed" | "failed" => {
    // Payment successful - both flags true and has transaction ID
    if (
      reservation.payment_initiated &&
      reservation.payment_made &&
      reservation.collection_transaction_id
    ) {
      return "completed";
    }

    // Payment processing - initiated but not completed yet
    if (reservation.payment_initiated && !reservation.payment_made) {
      return "processing";
    }

    // Payment failed - initiated but no transaction ID and not marked as made
    if (
      reservation.payment_initiated &&
      !reservation.payment_made &&
      !reservation.collection_transaction_id
    ) {
      return "failed";
    }

    // Payment not started - neither flag is true
    if (!reservation.payment_initiated && !reservation.payment_made) {
      return "not_started";
    }

    // Fallback for edge cases
    return "not_started";
  },

  /**
   * Get human-readable payment status message
   */
  getPaymentStatusMessage: (reservation: {
    payment_initiated: boolean;
    payment_made: boolean;
    collection_transaction_id?: string | null;
  }): string => {
    const status = guestTokenManager.getPaymentStatus(reservation);

    switch (status) {
      case "completed":
        return "Payment successful";
      case "processing":
        return "Payment processing...";
      case "failed":
        return "Payment failed";
      case "not_started":
        return "Payment not started";
      default:
        return "Unknown payment status";
    }
  },
};
