import { InputHTMLAttributes, forwardRef, useState } from 'react';
import { Label } from '../label/label';
import { twMerge } from 'tailwind-merge';
import { Eye, EyeSlash } from 'iconsax-react';

interface Props extends InputHTMLAttributes<HTMLInputElement> {
  id: string;
  label: string;
  error?: string;
}

export const PasswordInput = forwardRef<HTMLInputElement, Props>(
  ({ label, error, className, ...props }, ref) => {
    const [showPassword, setShowPassword] = useState(false);

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword);
    };

    return (
      <div className="font-rethink">
        <Label
          id={`label-${props.id}`}
          htmlFor={props.id}
          className="mb-1.5 block">
          {label}
        </Label>
        <div className="relative">
          <input
            className={twMerge(
              'flex p-2.5 pl-3.5 pr-12 border-grey-200 rounded-full border w-full focus:outline-none disabled:opacity-90 disabled:cursor-not-allowed text-grey-300 placeholder:text-grey-300',
              className
            )}
            type={showPassword ? 'text' : 'password'}
            ref={ref}
            {...props}
          />
          <button
            type="button"
            onClick={togglePasswordVisibility}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-grey-300 hover:text-grey-400 focus:outline-none transition-colors"
            aria-label={showPassword ? 'Hide password' : 'Show password'}>
            {showPassword ? (
              <Eye size={20} color="#B3B3B3" />
            ) : (
              <EyeSlash size={20} color="#B3B3B3" />
            )}
          </button>
        </div>
        {error && <p className="text-red-500 mt-1 text-sm">{error}</p>}
      </div>
    );
  }
);
