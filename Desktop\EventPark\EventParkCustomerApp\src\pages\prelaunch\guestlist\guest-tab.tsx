/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect, useRef, useCallback, useMemo } from "react";
import { SearchStatus, Filter, AddCircle, CloseCircle } from "iconsax-react";
import CreateGuestList from "../create-guest-list/create-guest";

interface GuestTabProps {
  guests: any[];
  onLoadMore: (status: string) => void;
  isLoadingMore: boolean;
  hasMorePages: boolean;
  onSearch: (searchQuery: string) => void;
  searchResults: any[];
  isSearching: boolean;
  isSearchLoading: boolean;
}

export const GuestTab = ({
  guests,
  onLoadMore,
  isLoadingMore,
  hasMorePages,
  onSearch,
  searchResults,
  isSearching,
  isSearchLoading,
}: GuestTabProps) => {
  const [activeTab, setActiveTab] = useState("Confirmed");
  const [hoveredCardId, setHoveredCardId] = useState<number | null>(null);
  const [isScrollable, setIsScrollable] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isGuestListModalOpen, setIsGuestListModalOpen] = useState(false);

  const getFilteredGuests = (guests: any[], status: string) => {
    return guests?.filter(
      (guest) => guest?.invite_status.toLowerCase() === status.toLowerCase()
    );
  };

  const displayedGuests = useMemo(() => {
    if (isSearching) {
      return searchResults;
    }
    return activeTab === "Confirmed"
      ? getFilteredGuests(guests, "accepted")
      : activeTab === "Pending"
      ? getFilteredGuests(guests, "pending")
      : activeTab === "Declined"
      ? getFilteredGuests(guests, "declined")
      : [];
  }, [guests, activeTab, isSearching, searchResults]);

  const handleSearchClick = () => {
    onSearch("__INIT_SEARCH__");
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    onSearch(query);
  };

  const handleSearchCancel = () => {
    setSearchQuery("");
    onSearch("__CANCEL_SEARCH__");
  };

  const checkScrollable = useCallback(() => {
    const container = scrollContainerRef.current;
    if (container) {
      setIsScrollable(container.scrollHeight > container.clientHeight);
    }
  }, []);

  const handleScroll = useCallback(() => {
    const container = scrollContainerRef.current;
    if (!container) {
      console.log("❌ No scroll container found");
      return;
    }

    const { scrollTop, scrollHeight, clientHeight } = container;
    const threshold = 100;
    const shouldLoadMore = scrollTop + clientHeight >= scrollHeight - threshold;

    if (!isLoadingMore && hasMorePages && shouldLoadMore) {
      onLoadMore(activeTab.toLowerCase());
    }
  }, [activeTab, isLoadingMore, hasMorePages, onLoadMore]);

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) {
      console.log("❌ No container found for scroll listener setup");
      return;
    }
    container.addEventListener("scroll", handleScroll);
    checkScrollable();

    return () => {
      container.removeEventListener("scroll", handleScroll);
    };
  }, [handleScroll, checkScrollable]);

  useEffect(() => {
    checkScrollable();
  }, [displayedGuests, checkScrollable]);

  return (
    <div className=" mb-16">
      <div className="flex justify-between items-center mb-6 relative">
        {!isSearching ? (
          <>
            <div className="flex bg-black/3 rounded-full p-1.5">
              {["Confirmed", "Pending", "Declined"].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`md:px-4 px-1 py-1 md:py-[9px] cursor-pointer rounded-full text-sm  ${
                    activeTab === tab
                      ? "bg-white text-primary font-bold"
                      : "text-grey-250 font-medium"
                  }`}
                >
                  {tab}
                </button>
              ))}
            </div>
            <div
              className={`absolute bottom-[-10px] w-1 h-1 bg-cus-orange rounded-full ${
                activeTab === "Confirmed"
                  ? "left-[50px]"
                  : activeTab === "Pending"
                  ? "md:left-[142px] left-[108px]"
                  : "md:left-[228px] left-[170px]"
              }`}
            />
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={() => setIsGuestListModalOpen(true)}
                className="md:h-12 md:w-12 h-7 w-7 rounded-full bg-white flex items-center justify-center"
              >
                <AddCircle size={26} color="#4D55F2" variant="Bold" />
              </button>
              <button
                onClick={handleSearchClick}
                className="md:h-12 md:w-12 h-7 w-7 rounded-full bg-white flex items-center justify-center"
              >
                <SearchStatus size={26} color="#4D55F2" variant="Bulk" />
              </button>
              <button className="md:h-12 md:w-12  h-7 w-7 rounded-full bg-white flex items-center justify-center">
                <Filter size={26} color="#5F66F3" variant="Bulk" />
              </button>
            </div>
          </>
        ) : (
          <div className="flex gap-2 items-center w-full bg-white rounded-full px-4 py-2 border border-grey-150">
            <SearchStatus size={20} color="#4D55F2" variant="Bulk" />
            <input
              type="text"
              placeholder="Search guests by name or email..."
              value={searchQuery}
              onChange={handleSearchChange}
              className="flex-1 outline-none text-sm text-dark-blue-100 placeholder:text-grey-500"
              autoFocus
            />
            <button onClick={handleSearchCancel} className="ml-3">
              <CloseCircle size={24} color="#292D32" variant="Bulk" />
            </button>
          </div>
        )}
      </div>

      <div className="relative ">
        <div
          ref={scrollContainerRef}
          data-testid="guest-scroll-container"
          className="space-y-4 max-h-[600px] overflow-y-auto [&::-webkit-scrollbar]:w-0 [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 [&::-webkit-scrollbar-thumb]:rounded-full"
        >
          {displayedGuests?.length === 0 ? (
            <div className="text-center py-10 text-grey-600">
              <div className="text-2xl font-semibold mb-2">
                {isSearching ? "No Search Results" : "No Guests Found"}
              </div>
              <p className="text-sm text-grey-500">
                {isSearching
                  ? `No guests found matching "${searchQuery}"`
                  : `There are no guests under the ${activeTab} tab.`}
              </p>
            </div>
          ) : (
            <>
              {displayedGuests?.map((guest) => (
                <div
                  key={guest?.id}
                  className="p-4 bg-white  border border-grey-150 rounded-2xl md:flex justify-between items-center"
                  onMouseEnter={() => setHoveredCardId(guest?.id)}
                  onMouseLeave={() => setHoveredCardId(null)}
                >
                  <div className="flex items-center">
                    <div
                      className={`w-10 h-10 bg-gradient-to-br from-[#FEF7F4] capitalize rounded-full flex items-center justify-center mr-4 text-dark-blue-200 font-semibold text-base`}
                    >
                      {`${guest?.first_name.charAt(0)}${guest?.last_name.charAt(
                        0
                      )}`}
                    </div>
                    <div>
                      <h3
                        className={`text-dark-blue-100 capitalize  text-sm font-semibold`}
                      >
                        {`${guest?.first_name} ${guest?.last_name}`}
                      </h3>
                      <p className="text-grey-650 text-xs">
                        {guest.email}{" "}
                        {guest?.phone_number && `• ${guest?.phone_number}`}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start justify-end mt-5 md:mt-0">
                    <div className="flex flex-col items-end mr-2">
                      <span
                        className={`${
                          guest?.invite_status === "declined"
                            ? "text-[#B42318] bg-[#FEF3F2]"
                            : guest?.invite_status === "pending"
                            ? "text-[#B54708] bg-[#FFFAEB] "
                            : "text-green-600 bg-grin"
                        } font-medium px-2.5 py-0.5 rounded-2xl text-sm`}
                      >
                        {guest?.invite_status}
                      </span>
                      <span className="text-grey-650 text-[10px] italic text-right md:text-start">
                        {guest?.invite_type === "manual"
                          ? "Added Manually"
                          : guest?.invite_type === "spreadsheet"
                          ? "Added via Bulk Upload"
                          : "Added Via Email invite"}
                      </span>
                    </div>
                    {hoveredCardId === guest?.id && (
                      <button className="w-6 h-6 cursor-pointer rounded-full bg-gray-100 hidden  items-center justify-center transition-all duration-500 ease-in-out">
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 32 32"
                          fill="#999999"
                        >
                          <circle cx="8" cy="16" r="3" />
                          <circle cx="16" cy="16" r="3" />
                          <circle cx="24" cy="16" r="3" />
                        </svg>
                      </button>
                    )}
                  </div>
                </div>
              ))}

              {(isLoadingMore || isSearchLoading) && (
                <div className="flex justify-center py-4">
                  <div
                    role="status"
                    aria-label={
                      isSearchLoading
                        ? "Searching guests"
                        : "Loading more guests"
                    }
                    className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full"
                  />
                </div>
              )}
            </>
          )}
        </div>
        {isScrollable && displayedGuests?.length > 0 && (
          <div
            className="absolute bottom-0 left-0 right-0 h-40 pointer-events-none rounded-2xl"
            style={{
              background:
                "linear-gradient(to top, rgba(255, 255, 255, 0.6) 0%, transparent 100%)",
            }}
          />
        )}
      </div>
      {isGuestListModalOpen && (
        <CreateGuestList onClose={() => setIsGuestListModalOpen(false)} />
      )}
    </div>
  );
};
