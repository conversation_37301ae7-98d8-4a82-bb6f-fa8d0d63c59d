/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect } from 'react';
import { events } from '../services/events';
import { CompleteEventData } from '../store/event';
import { toast } from 'react-toastify';

// Global event emitter for complete event data updates
class CompleteEventDataEmitter {
  private listeners: (() => void)[] = [];

  subscribe(listener: () => void) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter((l) => l !== listener);
    };
  }

  emit() {
    this.listeners.forEach((listener) => listener());
  }
}

export const completeEventDataEmitter = new CompleteEventDataEmitter();

/**
 * Custom hook to fetch complete event data by ID
 * This hook automatically fetches complete event details when eventId changes
 */
export const useCompleteEventData = (eventId: string | null | undefined) => {
  const [completeEventData, setCompleteEventData] =
    useState<CompleteEventData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Function to manually refetch the data
  const refetch = async (silent = false) => {
    if (!eventId) return;

    if (!silent) {
      setIsLoading(true);
    }
    setError(null);

    try {
      const response = await events.getEventByID(eventId);
      setCompleteEventData(response.data);
    } catch (err: any) {
      const errorMessage =
        err?.response?.data?.message ||
        'Failed to fetch complete event details';
      setError(errorMessage);
      if (!silent) {
        toast.error(errorMessage);
      }
    } finally {
      if (!silent) {
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    if (!eventId) {
      setCompleteEventData(null);
      setError(null);
      return;
    }

    const fetchCompleteEventData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await events.getEventByID(eventId);
        setCompleteEventData(response.data);
      } catch (err: any) {
        const errorMessage =
          err?.response?.data?.message ||
          'Failed to fetch complete event details';
        setError(errorMessage);
        toast.error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCompleteEventData();
  }, [eventId]);

  // Listen for global complete event data update events
  useEffect(() => {
    const unsubscribe = completeEventDataEmitter.subscribe(() => {
      if (eventId) {
        refetch(true); // Silent refetch
      }
    });
    return unsubscribe;
  }, [eventId, refetch]);

  return {
    completeEventData,
    isLoading,
    error,
    refetch,
  };
};
