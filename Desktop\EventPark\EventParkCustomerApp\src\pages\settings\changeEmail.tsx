import { useEffect, useRef, useState } from 'react';

interface ChangeEmailProps {
  initialEmail: string;
  newEmail: string;
  onSave: (email: string) => void;
  onClose: () => void;
}

export const ChangeEmail = ({
  initialEmail,
  newEmail,
  onSave,
  onClose,
}: ChangeEmailProps) => {
  const [emailInput, setEmailInput] = useState(newEmail || '');
  const [error, setError] = useState('');
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);
  const handleSave = () => {
    if (!emailInput) {
      setError('Please enter a new email');
      return;
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailInput)) {
      setError('Please enter a valid email');
      return;
    }
    if (emailInput === initialEmail) {
      setError('New email must be different from current email');
      return;
    }
    setError('');
    onSave(emailInput);
  };

  return (
    <div
    ref={modalRef}  className="relative bg-white rounded-2xl shadow-[0px_12px_120px_0px_#5F5F5F0F] w-full p-4 z-50">
      <div className="mb-6">
        <h3 className="text-sm font-medium mb-4.5 tracking-[0.12em] text-primary">
          ACCOUNT EMAIL
        </h3>
        <h2 className="text-2xl  font-medium mb-1.5">Change Email</h2>
        <p className="text-grey-950 text-base">
          Keep your account updated with a new email.
        </p>
      </div>
      <div className="flex gap-1 mb-6 w-[81px] bg-cus-pink-700 rounded-full">
        <div className="h-[8px] w-10 bg-primary-750 rounded-full"></div>
        <div className="h-[8px] w-10 bg-cus-pink-700 rounded-full"></div>
      </div>
      <div className="mb-3">
        <label className="text-grey-500 text-sm font-medium mb-1.5 block">
          Current Email
        </label>
        <input
          type="email"
          value={initialEmail}
          disabled
          className="w-full text-grey-50 outline-none py-2.5 px-3.5 border border-gray-300 rounded-full bg-gray-50"
        />
      </div>

      <div className="mb-5">
        <label className="text-grey-500 text-sm font-medium mb-1.5 block">
          New Email
        </label>
        <input
          type="email"
          value={emailInput}
          onChange={(e) => setEmailInput(e.target.value)}
          className={`w-full text-grey-50 outline-none py-2.5 px-3.5 border ${
            error ? 'border-red-500' : 'border-gray-300'
          } rounded-full`}
        />
        {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
      </div>

      <button
        onClick={handleSave}
        className="w-fit bg-primary text-white py-2 px-3.5 rounded-full text-sm font-semibold cursor-pointer">
        Continue
      </button>
    </div>
  );
};
