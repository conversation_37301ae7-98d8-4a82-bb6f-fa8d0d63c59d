import { ArrowRight } from 'iconsax-react';
import { useState } from 'react';
import truck from '../../../assets/images/truck-tick.png';
import { TextInput } from '../../../components/inputs/text-input/text-input';
interface DeliveryData {
  state?: string;
  city?: string;
  address?: string;
  shareAddress?: boolean;
}

interface DeliveryDetailsProps {
  onNextStep: (data: DeliveryData) => void;
  initialData?: DeliveryData;
}

export const DeliveryDetails = ({
  onNextStep,
  initialData = {},
}: DeliveryDetailsProps) => {
  const [state, setState] = useState(initialData.state || '');
  const [city, setCity] = useState(initialData.city || '');
  const [address, setAddress] = useState(initialData.address || '');
  const [shareAddress, setShareAddress] = useState(
    initialData.shareAddress || false
  );

  const handleContinue = () => {
    onNextStep({
      state,
      city,
      address,
      shareAddress,
    });
  };

  return (
    <div className="px-4 md:px-0 md:ml-3.5">
      <div className="max-w-[560px] w-full mx-auto mt-11">
        <div className="bg-light-blue-100 flex items-center gap-3 p-3.5 mb-8 rounded-2xl">
          <img src={truck} alt="truck" />{' '}
          <p className="italic text-dark-blue-600 font-medium text-sm">
            Add a delivery address so guests can send physical
            <br /> gifts directly to you, hassle-free.
          </p>
        </div>

        <div className="">
          <label className="block text-grey-500 font-medium text-sm mb-2">
            Select State of Residence
          </label>
          <select
            value={state}
            onChange={(e) => setState(e.target.value)}
            className="w-full h-[44px] px-3.5 border border-gray-300 rounded-full text-base font-bold text-grey-50 placeholder:font-normal placeholder:text-grey-700 italic outline-0">
            <option value="">Select State</option>
            <option value="lagos">Lagos</option>
            <option value="abuja">Abuja</option>
            <option value="rivers">Rivers</option>
            <option value="oyo">Oyo</option>
            <option value="kano">Kano</option>
          </select>
        </div>
        <div className="my-6">
          <TextInput
            id="city"
            label="City"
            value={city}
            onChange={(e) => setCity(e.target.value)}
            placeholder="Enter your city"
            className="text-grey-50 font-bold italic placeholder:font-normal placeholder:text-grey-700"
          />
        </div>
        <TextInput
          id="fullAddress"
          label="Full Address"
          value={address}
          onChange={(e) => setAddress(e.target.value)}
          placeholder="Enter your full Address"
          className="text-grey-50 font-bold italic placeholder:font-normal placeholder:text-grey-700"
        />

        <div className="mb-7 py-3 flex items-center gap-2.5 mt-5 border-t border-grey-150">
          <div className="relative flex items-center">
            <input
              type="checkbox"
              id="shareAddress"
              checked={shareAddress}
              onChange={(e) => setShareAddress(e.target.checked)}
              className="appearance-none w-5.5 h-5.5 rounded-full border border-primary-650 checked:bg-primary-650 cursor-pointer"
            />
            {shareAddress && (
              <svg
                className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 pointer-events-none"
                width="12"
                height="12"
                viewBox="0 0 12 12"
                fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M4.5 8.1L2.4 6L1.5 6.9L4.5 9.9L10.5 3.9L9.6 3L4.5 8.1Z"
                  fill="white"
                />
              </svg>
            )}
          </div>
          <label htmlFor="shareAddress" className="text-sm text-grey-300 italic">
            I consent to sharing my address with friends and family for gift
            delivery.
          </label>
        </div>

        <button
          onClick={handleContinue}
          disabled={!state || !city || !address}
          className={`bg-primary-650 text-white mb-24 py-2.5 px-4 rounded-full cursor-pointer flex items-center gap-2 ${
            state && city && address ? '' : 'opacity-50 cursor-not-allowed'
          }`}>
          Continue
          <div className="bg-white/30 rounded-full p-0.5">
            <ArrowRight size="12" color="#fff" />
          </div>
        </button>
      </div>
    </div>
  );
};

