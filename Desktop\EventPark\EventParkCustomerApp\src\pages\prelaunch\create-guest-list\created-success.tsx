import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { Footer } from "../footer";
import { ArrowRight } from "iconsax-react";
import wed from "../../../assets/images/wed.png";
import { useEventStore } from "../../../lib/store/event";
import { useState } from "react";
export const CreatedSuccess = ({
  onClose,
  invitationPreviewUrl,
  guestCount = 0,
}: {
  onClose: () => void;
  invitationPreviewUrl?: string | null;
  guestCount?: number;
}) => {
  const navigate = useNavigate();
  const { selectedEvent } = useEventStore();
  const [copied, setCopied] = useState(false);

  const isOpenEvent = selectedEvent?.visibility === "public";
  const shareableLink = selectedEvent?.invite_link || "";

  const copyToClipboard = () => {
    navigator.clipboard
      .writeText(shareableLink)
      .then(() => {
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      })
      .catch((err) => {
        console.error("Failed to copy: ", err);
      });
  };
  // const createGiftRegistry = () => {
  //   onClose();
  //   // Navigate to gift registry creation
  //   navigate('/gift-registry/create');
  // };
  const goHome = () => {
    onClose();
    navigate("/guest-lists");
  };
  return (
    <div>
      <div className="relative w-full pt-20 md:pt-34 px-4 md:px-0 pb-56">
        <div
          className="absolute inset-0  h-[720px] md:h-[774px] top-0 bg-[url(/src/assets/animations/gift.gif)] opacity-40 z-0"
          style={{ backgroundSize: "cover" }}
        />
        <motion.div
          initial={{ scale: 0.5, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="relative z-20 bg-white border-t border-white rounded-[20px] text-center max-w-[450px] w-full mx-auto shadow-[0px_12px_120px_0px_#5F5F5F0F]"
        >
          <div
            className="bg-[linear-gradient(180deg,_#FDEFE9_0%,_#FEF7F4_100%)] rounded-t-[20px] h-[262px] w-full overflow-hidden"
            style={{
              clipPath:
                "polygon(0 0, 100% 0, 100% 100%, 70% 95%, 30% 95%, 0 100%)",
            }}
          />
          <div className="absolute left-1/2 top-[16%] -translate-x-1/2">
            <div className="relative h-[200px]">
              <div className="overflow-hidden border-4 border-white rounded-[17px] bg-[#FEF7F4] w-[161px] h-[166px] backdrop-blur-[17.52px] shadow-[0px_24.64px_24.64px_0px_#A6A6A60A,0px_49.28px_61.6px_0px_#A6A6A61A,0px_28.97px_144.87px_0px_#0000000F] rotate-[-5deg] z-10 relative">
                <div>
                  <img
                    src={invitationPreviewUrl || wed}
                    alt="nvitationPreviewUrl"
                    className="h-[96px] w-full object-cover"
                  />
                  <p className="font-bold text-base w-full px-2 truncate text-left">
                    {selectedEvent?.title || ""}
                  </p>
                  <div className="text-left px-2 w-full text-xs text-gray-400">
                    {guestCount} {guestCount === 1 ? "Guest" : "Guests"}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="flex flex-col items-center text-center py-12 px-4 w-full">
            <h2 className="text-2xl md:text-4xl font-medium my-2 text-dark-200">
              Your Invite has <br />{" "}
              <span className="text-[18px] md:text-[26px] text-grey-250">
                been sent Successfully!
              </span>
            </h2>

            {isOpenEvent ? (
              <p className="text-grey-250 text-base mt-2 max-w-[335px] mb-7.5">
                You can also copy the link below to share. Please proceed to
                create gift registry for your event by clicking the button below
              </p>
            ) : (
              <p className="text-grey-250 text-base mt-2 mb-7.5">
                Your guests will receive them shortly. <br />
                Please proceed to create gift registry for your <br />
                guests by clicking the button below
              </p>
            )}
            {isOpenEvent && (
              <div className="bg-[#EDEEFE] rounded-t-2xl p-4 w-full max-w-[342px] ">
                <p className="text-gray-700 text-xs font-medium mb-3">
                  Copy this link to share your event with family and friends
                </p>
                <div className="flex items-center justify-between bg-white rounded-full p-2 pl-4 italic">
                  <span className="text-sm text-[#000059] truncate mr-2">
                    <strong>Link:</strong> {shareableLink}
                  </span>
                  <button
                    type="button"
                    onClick={copyToClipboard}
                    className="bg-primary-100 text-primary flex items-center gap-1 py-1.5 px-4 rounded-full"
                  >
                    <span className="text-sm font-medium">
                      {copied ? "Copied!" : "Copy"}
                    </span>
                    <svg
                      width="12"
                      height="12"
                      viewBox="0 0 12 12"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M8 6.45V8.55C8 10.3 7.3 11 5.55 11H3.45C1.7 11 1 10.3 1 8.55V6.45C1 4.7 1.7 4 3.45 4H5.55C7.3 4 8 4.7 8 6.45Z"
                        fill="#4D55F2"
                      />
                      <path
                        opacity="0.4"
                        d="M8.55281 1H6.45281C4.72781 1 4.02781 1.685 4.00781 3.375H5.55281C7.65281 3.375 8.62781 4.35 8.62781 6.45V7.995C10.3178 7.975 11.0028 7.275 11.0028 5.55V3.45C11.0028 1.7 10.3028 1 8.55281 1Z"
                        fill="#4D55F2"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            )}
            {isOpenEvent ? (
              <button
                type="button"
                onClick={goHome}
                className="underline text-cus-orange-100 italic font-bold text-lg cursor-pointer mt-6"
              >
                <span>Back to dashboard</span>
              </button>
            ) : (
              <button
                type="button"
                onClick={() => navigate("/guest-lists")}
                className="bg-primary cursor-pointer text-lg text-white flex items-center py-3 px-6 font-semibold rounded-full gap-2 hover:bg-[#4A48E0] transition-colors mt-4"
              >
                <span>Go To GuestList</span>
                <div className="rounded-full bg-white/30 p-0.5">
                  <ArrowRight size="16" color="#fff" />
                </div>
              </button>
            )}
          </div>
        </motion.div>
        {!isOpenEvent && (
          <div className="flex justify-center mt-10">
            <button
              type="button"
              onClick={goHome}
              className="underline text-cus-orange-100 italic font-bold text-lg cursor-pointer"
            >
              Back to dashboard
            </button>
          </div>
        )}
      </div>
      <Footer />
    </div>
  );
};
