import { CloseCircle } from 'iconsax-react';
import { useState } from 'react';
import priv from '../../assets/images/private.png';
import open from '../../assets/images/open.png';
interface EventPreferenceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSetPreference: (preference: 'public' | 'private') => void;
  isLoading?: boolean;
}

export const EventPreferenceModal = ({
  isOpen,
  onClose,
  onSetPreference,
  isLoading = false,
}: EventPreferenceModalProps) => {
  const [selectedPreference, setSelectedPreference] = useState<
    'public' | 'private' | null
  >(null);

  const handleSetPreference = () => {
    if (selectedPreference) {
      onSetPreference(selectedPreference);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
      <div className="bg-white rounded-[16px] pt-12 md:pt-6 pb-6 max-w-[664px] w-full relative max-h-[90vh] overflow-y-auto [&::-webkit-scrollbar]:hidden">
        <button
          onClick={onClose}
          className="absolute top-2 right-2 sm:p-2 rounded-full hover:bg-gray-100 transition-colors z-10"
          disabled={isLoading}>
          <CloseCircle size={32} color="#4D55F2" variant="Bulk" />
        </button>

        <div className="text-center mb-6 px-4 sm:px-6">
          <h2 className="text-2xl sm:text-[28px] font-bold text-gray-900 tracking-[-0.03em] mb-2">
            Set a Preference for your Event
          </h2>
          <p className="text-[#666666] text-sm sm:text-base">
            Please select an option to continue
          </p>
        </div>

        <div className="space-y-4 mb-6 px-4 sm:px-0 max-w-[400px] mx-auto">
          <div
            onClick={() => setSelectedPreference('public')}
            className={`relative p-3 sm:p-4 rounded-2xl border-2 bg-[#FAFAFA] cursor-pointer transition-all ${
              selectedPreference === 'public'
                ? 'border-primary'
                : 'border-gray-200 hover:border-gray-300'
            }`}>
            {selectedPreference === 'public' && (
              <div className="absolute top-3 right-3 sm:top-4 sm:right-4 w-5 h-5 sm:w-6 sm:h-6 bg-primary rounded-full flex items-center justify-center">
                <svg
                  className="w-3 h-3 sm:w-4 sm:h-4 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
            )}

            <div className="flex items-start gap-3 sm:gap-4">
              <div className="flex-1">
                <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-1 sm:mb-2">
                  Open Event
                </h3>
                <p className="text-gray-500 text-xs sm:text-sm leading-relaxed">
                  Ideal for broader audiences or public gatherings, can be
                  joined by anyone who has access to the event link
                </p>
              </div>
              <div className="flex-shrink-0">
                <img
                  src={open}
                  alt="an open lock"
                  className="w-8 h-8 sm:w-auto sm:h-auto"
                />
              </div>
            </div>
          </div>

          <div
            onClick={() => setSelectedPreference('private')}
            className={`relative p-3 sm:p-4 rounded-2xl border-2 bg-[#FAFAFA] cursor-pointer transition-all ${
              selectedPreference === 'private'
                ? 'border-primary '
                : 'border-gray-200 hover:border-gray-300'
            }`}>
            {selectedPreference === 'private' && (
              <div className="absolute top-3 right-3 sm:top-4 sm:right-4 w-5 h-5 sm:w-6 sm:h-6 bg-primary rounded-full flex items-center justify-center">
                <svg
                  className="w-3 h-3 sm:w-4 sm:h-4 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
            )}

            <div className="flex items-start gap-3 sm:gap-4">
              <div className="flex-1">
                <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-1 sm:mb-2">
                  Private Event
                </h3>
                <p className="text-gray-500 text-xs sm:text-sm leading-relaxed">
                  Restrict access, only guests who have been specifically added
                  to the guest list can accept an invitation and attend
                </p>
              </div>
              <div className="flex-shrink-0">
                <img
                  src={priv}
                  alt="a private lock"
                  className="w-8 h-8 sm:w-auto sm:h-auto"
                />
              </div>
            </div>
          </div>

          <button
            onClick={handleSetPreference}
            disabled={!selectedPreference || isLoading}
            className={`w-full h-12 sm:h-[52px] rounded-full  mt-6 sm:mt-[46px] text-white font-semibold text-base sm:text-lg transition-colors ${
              !selectedPreference || isLoading
                ? 'bg-primary/50 cursor-not-allowed'
                : 'bg-primary cursor-pointer hover:bg-primary/90'
            }`}>
            {isLoading ? 'Setting Preference...' : 'Set Preference'}
          </button>
        </div>
      </div>
    </div>
  );
};
