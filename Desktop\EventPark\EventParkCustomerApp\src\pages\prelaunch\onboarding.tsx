import {
  ArrowRight,
  CloseCircle,

  // Crown,
} from "iconsax-react";
// import { Button } from "../../components/button/onboardingButton";
import gift from "../../assets/images/gift-box.png";
// import box from '../../assets/images/box.png';
import { useNavigate } from "react-router-dom";
import { Footer } from "./footer";
// import img from '../../assets/images/res.png';
// import { Head } from '../../components/reuseables/head';
// import CreateGuestList from "./create-guest-list/create-guest";
import { useEffect, useState } from "react";
// import { CreateGiftRegistry } from './gift-registry/create-gift-registry';
// import { useEventStore } from "../../lib/store/event";
// import { CreateGiftRegistry } from "./gift-registry/create-gift-registry";
import { Head } from "../../components/reuseables/head";

interface OnboardingProps {
  eventName?: string;
}

const GiftRegistryPopup = ({
  onClose,
  navigate,
}: {
  onClose: () => void;
  navigate: (path: string) => void;
}) => {
  return (
    <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white w-[472px] h-[480px] mt-[70px] rounded-[20px] relative">
        {/* Close Button - exactly positioned per Figma at x:416, y:20 */}
        <button
          onClick={onClose}
          className="absolute w-10 h-10 flex items-center justify-center"
          style={{ left: "416px", top: "20px" }}
        >
          <CloseCircle size={40} color="#634C42" variant="Bulk" />
        </button>

        {/* Main Content - positioned per Figma at x:40, y:72 */}
        <div
          className="absolute"
          style={{ left: "40px", top: "72px", width: "385px" }}
        >
          {/* Gift Registry Title */}
          <div className="mb-5">
            <h1 className="mb-4">
              <span className="text-[56px] font-semibold leading-[1em] tracking-[-3%] text-[#4D55F2] ">
                Gift
              </span>
              <br />
              <span className="text-[56px] font-semibold leading-[1em] tracking-[-3%] text-[#000073]">
                Registry
              </span>
            </h1>
            <p className="text-sm font-normal leading-[1em] tracking-[16%] text-[#808080] uppercase">
              MAKE GIFTING EASY FOR EVERYONE
            </p>
          </div>

          {/* Description Text */}
          <p className="text-lg font-normal leading-[1.7] tracking-[-3%] text-[#666666] mb-8 w-full">
            Curate, Share & Receive gifts you'll truly love with ease with our
            smart gift registry
          </p>

          {/* Create Event Button - positioned per Figma at y:392 from container */}
          <div className="absolute" style={{ top: "320px" }}>
            <button
              onClick={() => navigate("/create-event")}
              className="flex items-center gap-2 bg-[#4D55F2] border border-[#4D55F2] rounded-full text-white font-semibold text-base leading-[1.5] hover:bg-[#3d44e8] transition-colors shadow-sm"
              style={{ padding: "12px 20px 12px 20px" }}
            >
              Create an Event
              <ArrowRight size={20} className="text-white" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

const GuestListPopup = ({
  onClose,
  navigate,
}: {
  onClose: () => void;
  navigate: (path: string) => void;
}) => {
  return (
    <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white w-[472px] h-[480px] mt-[70px] rounded-[20px] relative">
        {/* Close Button - exactly positioned per Figma at x:416, y:20 */}
        <button
          onClick={onClose}
          className="absolute w-10 h-10 flex items-center justify-center"
          style={{ left: "416px", top: "20px" }}
        >
          <CloseCircle size={40} color="#634C42" variant="Bulk" />
        </button>

        {/* Main Content - positioned per Figma at x:40, y:72 */}
        <div
          className="absolute"
          style={{ left: "40px", top: "72px", width: "385px" }}
        >
          {/* Tool Selection Component */}
          <div className="w-[385px] h-[143px] mb-5">
            <div className="flex flex-col gap-[75px]">
              {/* Guestlist Manager - HIGHLIGHTED/SELECTED */}
              <div className="flex flex-col gap-4 pb-6 border-b  border-[#F0F0F0]">
                <h3 className="text-[56px] font-semibold leading-[1em] tracking-[-3%] text-[#4D55F2] ">
                  Guestlist
                  <br />
                  <span className="text-[#000073]">Manager</span>
                </h3>
                <p className="text-sm font-normal leading-[1em] tracking-[14%] text-[#808080] uppercase">
                  PLAN LIKE A PRO
                </p>
              </div>
            </div>
          </div>

          {/* Description Text */}
          <p className="text-lg font-normal leading-[1.7] pt-6 tracking-[-3%] text-[#666666] mb-8 w-full">
            Send invites, track RSVPs, and manage your guest list with ease.
            Everything you need to host confidently, all in one place!
          </p>

          {/* Create Event Button - positioned per Figma at y:392 from container */}
          <div className="absolute" style={{ top: "320px" }}>
            <button
              onClick={() => navigate("/create-event")}
              className="flex items-center gap-2 bg-[#4D55F2] border border-[#4D55F2] rounded-full text-white font-semibold text-base leading-[1.5] hover:bg-[#3d44e8] transition-colors shadow-sm"
              style={{ padding: "12px 20px 12px 20px" }}
            >
              Create an Event
              <ArrowRight size={20} className="text-white" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

const BudgetPlannerPopup = ({
  onClose,
  navigate,
}: {
  onClose: () => void;
  navigate: (path: string) => void;
}) => {
  return (
    <div className="fixed inset-0 bg-black/50  flex items-center justify-center z-50">
      <div className="bg-white w-[472px] h-[480px] mt-[70px] rounded-[20px] relative">
        {/* Close Button - exactly positioned per Figma at x:416, y:20 */}
        <button
          onClick={onClose}
          className="absolute w-10 h-10 flex items-center justify-center"
          style={{ left: "416px", top: "20px" }}
        >
          <CloseCircle size={40} color="#634C42" variant="Bulk" />
        </button>

        {/* Coming Soon Badge - positioned per Figma at x:40, y:20 */}
        <div
          className="absolute bg-[#EDEEFE] border border-[#B8BBFA] rounded-[9px] flex items-center justify-center"
          style={{
            left: "40px",
            top: "20px",
            padding: "6px 8px",
          }}
        >
          <span className="text-[13px] font-medium leading-[1.54] text-[#4D55F2]">
            Coming Soon 🎉
          </span>
        </div>

        {/* Main Content - positioned per Figma at x:40, y:72 */}
        <div
          className="absolute"
          style={{ left: "40px", top: "72px", width: "385px" }}
        >
          {/* Budget Planner Title */}
          <div className="mb-5">
            <h1 className="mb-4">
              <span className="text-[40px] italic font-normal leading-[1em] tracking-[-3%] text-[#FF9975]">
                Budget
              </span>
              <br />
              <span className="text-[56px] font-semibold leading-[1em] tracking-[-3%] text-[#4D0000]">
                Planner
              </span>
            </h1>
            <p className="text-sm font-normal leading-[1em] tracking-[16%] text-[#808080] uppercase">
              PLAN SMART AND MANAGE COSTS
            </p>
          </div>

          {/* Description Text */}
          <p className="text-lg font-normal leading-[1.7] tracking-[-3%] text-[#666666] mb-8 w-full">
            Plan Smart and manage costs accurately Make every penny count and
            track your event spending with smart budget planner
          </p>

          {/* Create Event Button - positioned per Figma at y:392 from container */}
          <div className="absolute" style={{ top: "320px" }}>
            <button
              onClick={() => navigate("/create-event")}
              className="flex items-center gap-2 bg-[#4D55F2] border border-[#4D55F2] rounded-full text-white font-semibold text-base leading-[1.5] hover:bg-[#3d44e8] transition-colors shadow-sm"
              style={{ padding: "12px 20px 12px 20px" }}
            >
              Create an Event
              <ArrowRight size={20} className="text-white" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export const Onboarding = ({ eventName }: OnboardingProps) => {
  console.log(eventName);
  const navigate = useNavigate();
  // const { createdEventData, setSelectedEvent } = useEventStore();
  const [isGuestListModalOpen, setIsGuestListModalOpen] = useState(false);
  const [isGiftRegistryModalOpen, setIsGiftRegistryModalOpen] = useState(false);
  const [isBudgetPlannerModalOpen, setIsBudgetPlannerModalOpen] =
    useState(false);

  useEffect(() => {
    if (
      isGuestListModalOpen ||
      isGiftRegistryModalOpen ||
      isBudgetPlannerModalOpen
    ) {
      document.body.style.overflow = "hidden";
      document.body.style.position = "fixed";
      document.body.style.width = "100%";
    } else {
      document.body.style.overflow = "";
      document.body.style.position = "";
      document.body.style.width = "";
    }

    return () => {
      document.body.style.overflow = "";
      document.body.style.position = "";
      document.body.style.width = "";
    };
  }, [isGuestListModalOpen, isGiftRegistryModalOpen, isBudgetPlannerModalOpen]);
  return (
    <div className="fixed inset-0 px-4 md:px-0 [&::-webkit-scrollbar]:hidden overflow-y-auto z-50 bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)]">
      {/* Fixed Header */}
      <div className="fixed top-0 left-0 right-0 z-[60] bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] px-4 md:px-0">
        <Head />
      </div>

      <div className="relative pt-[77px]">
        <div
          className="absolute inset-0 h-[520px] md:h-[444px] top-[77px] bg-[url(/src/assets/animations/gift.gif)] opacity-40 z-50"
          style={{ backgroundSize: "cover" }}
        />
        <div className="relative z-50">
          <div className="max-w-[572px] w-full mx-auto mb-32 pt-10">
            <div className="relative mb-10 rounded-[20px] bg-[#4D55F2] z-10 px-3 sm:px-7 py-7">
              <svg
                className="absolute top-0 left-0"
                width="565"
                height="188"
                viewBox="0 0 565 188"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  opacity="0.2"
                  d="M564.312 356.12L553.618 400.023C499.737 384.142 404.928 349.349 344.859 288.824C311.689 296.405 276.752 297.976 241.624 293.112C179.968 284.56 120.995 257.191 73.1254 215.267C27.9781 234.167 -21.3327 236.207 -60.0848 218.511L-44.2082 176.553C-21.2378 187.036 8.58992 187.514 37.9566 179.544C-23.6286 106.703 -13.4568 51.0915 -9.94314 31.7889C1.05899 -28.5974 47.177 -64.3601 99.6377 -53.1848C137.989 -44.9657 179.912 -13.9441 187.075 36.8434C194.559 90.0256 173.546 142.337 129.431 180.416C124.555 184.625 119.494 188.623 114.27 192.312C173.109 237.208 245.345 256.565 312.883 248.186C292.535 214.06 283.663 173.8 294.508 126.543C294.651 125.958 294.805 125.324 294.948 124.739C306.13 78.8354 340.131 43.0567 380.017 35.4156C424.65 26.8211 470.127 53.4004 488.2 98.6199C508.869 150.356 490.267 210.332 442.971 244.492C427.16 255.935 410.231 265.533 392.484 273.216C395.146 275.36 397.865 277.468 400.672 279.603C454.6 320.071 526.352 344.932 564.357 356.133L564.312 356.12ZM357.56 238.244C379.709 231.022 400.863 220.474 420.199 206.463C445.638 188.08 464.813 153.264 450.246 116.869C439.735 90.5566 413.12 75.1311 386.995 80.1358C362.757 84.8079 341.069 108.478 334.442 137.634C326.113 173.973 333.656 207.201 357.515 238.231L357.56 238.244ZM80.0359 161.727C88.4639 156.786 96.458 151.091 103.861 144.699C122.697 128.448 153.208 93.7318 146.197 43.8781C142.106 15.0143 115.104 -3.50554 91.7047 -8.52012C57.0049 -15.9244 36.0375 10.8342 30.5455 40.7948C23.3011 80.4864 39.0161 118.998 80.0241 161.775L80.0359 161.727Z"
                  fill="#9CC1FC"
                />
              </svg>

              <div className="flex items-start justify-between">
                <div>
                  <h1 className="text-[30px] sm:text-[40px] font-bold leading-[0.95] tracking-[-4%] text-white mb-3">
                    Welcome to
                    <br />
                    <span className="text-[#9CC1FC]">Eventpark</span>
                  </h1>
                  <p className="text-sm  sm:text-base leading-[1.5] tracking-[-3%] text-[#F1F4F7] mb-0 max-w-[343px]">
                    Create your first event and unlock a seamless planning
                    experience.
                    <span className="font-medium italics">
                      {" "}
                      Plan, Book, Celebrate{" "}
                    </span>{" "}
                    .
                  </p>
                </div>

                {/* Small Icon */}
                <div className="min-w-9 min-h-9 bg-[#010139] rounded-full flex items-center justify-center mt-1">
                  <svg
                    width="21"
                    height="20"
                    viewBox="0 0 21 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M14.828 0.817745C13.9879 1.14897 13.0136 1.87854 12.2276 2.76614C12.0826 2.2509 11.8206 1.73349 11.4352 1.19011C11.2209 0.889186 10.8571 0.731148 10.4912 0.7896C9.88065 0.88702 9.51691 1.52999 9.75291 2.10152C10.1773 3.13417 10.1664 4.28373 9.67929 5.25576C9.40649 5.80131 9.00161 6.2581 8.52095 6.59582C8.53177 6.53088 8.5426 6.47026 8.55126 6.40748C8.75911 4.83577 8.32825 3.16664 7.37343 1.82441C6.43376 0.506 4.63021 0.0167387 3.18174 0.683523C1.24395 1.57546 0.436361 3.5888 1.2223 5.57616C1.55357 6.41614 2.28322 7.39033 3.17092 8.17619C2.65562 8.32123 2.13815 8.58318 1.59254 8.96853C1.29159 9.18286 1.13353 9.54656 1.19199 9.91243C1.28942 10.5229 1.93246 10.8866 2.50406 10.6506C3.53682 10.2263 4.68651 10.2372 5.65865 10.7243C6.20426 10.997 6.6611 11.4019 6.99886 11.8825C6.93391 11.8716 6.87329 11.8608 6.8105 11.8522C5.23861 11.6443 3.5693 12.0751 2.22692 13.032C0.908359 13.9716 0.41904 15.7749 1.0859 17.2232C1.97793 19.1608 3.9915 19.9683 5.97909 19.1825C6.81916 18.8512 7.79347 18.1217 8.57941 17.2341C8.72447 17.7493 8.98645 18.2667 9.37184 18.8101C9.58619 19.111 9.94993 19.2691 10.3158 19.2106C10.9264 19.1132 11.2901 18.4702 11.0541 17.8987C10.6298 16.866 10.6406 15.7165 11.1278 14.7444C11.4006 14.1989 11.8054 13.7421 12.2861 13.4044C12.2753 13.4693 12.2645 13.5299 12.2558 13.5927C12.0479 15.1644 12.4788 16.8336 13.4336 18.1758C14.3733 19.4942 16.1768 19.9835 17.6253 19.3167C19.5631 18.4247 20.3707 16.4114 19.5848 14.424C19.2535 13.5841 18.5238 12.6099 17.6361 11.824C18.1514 11.679 18.6689 11.417 19.2123 11.0317C19.5133 10.8173 19.6714 10.4536 19.6129 10.0878C19.5155 9.47728 18.8724 9.11358 18.3008 9.34956C17.2681 9.77387 16.1184 9.76305 15.1462 9.27595C14.6006 9.00317 14.1438 8.59834 13.806 8.11774C13.871 8.12856 13.9316 8.13938 13.9944 8.14804C15.5663 8.35587 17.2356 7.92506 18.578 6.97034C19.8965 6.03078 20.3858 4.22744 19.719 2.77913C18.827 0.841558 16.8134 0.0340588 14.8258 0.819912L14.828 0.817745ZM3.30732 4.72969C3.1471 4.32486 2.90028 3.35715 3.87891 2.77913C3.93954 2.74233 4.00666 2.70769 4.07811 2.67521C4.58691 2.44141 5.23861 2.71851 5.52874 3.12335C6.16529 4.01744 6.46624 5.1605 6.32984 6.18233C6.29736 6.4248 6.18694 6.78633 6.02889 7.19983C4.74064 6.98117 3.71869 5.78183 3.30299 4.72969H3.30732ZM5.13252 17.0977C4.72764 17.2579 3.75983 17.5047 3.18174 16.5261C3.14494 16.4655 3.11029 16.3984 3.07782 16.327C2.84398 15.8182 3.12112 15.1666 3.526 14.8765C4.4202 14.24 5.56338 13.9391 6.58532 14.0755C6.82782 14.108 7.18939 14.2184 7.60293 14.3764C7.38426 15.6645 6.18478 16.6863 5.13252 17.102V17.0977ZM9.74208 12.8393C9.4736 11.4668 8.70282 10.2155 7.56613 9.33873C8.93882 9.07245 10.1903 8.29959 11.0671 7.16302C11.3356 8.53556 12.1064 9.78686 13.2431 10.6636C11.8704 10.9299 10.619 11.7028 9.74208 12.8393ZM17.4997 15.2727C17.66 15.6775 17.9068 16.6452 16.9281 17.2232C16.8675 17.26 16.8004 17.2947 16.7289 17.3272C16.2201 17.561 15.5684 17.2839 15.2783 16.879C14.6418 15.9849 14.3408 14.8419 14.4772 13.82C14.5097 13.5776 14.6201 13.216 14.7782 12.8025C16.0664 13.0212 17.0884 14.2205 17.5041 15.2727H17.4997ZM17.7292 3.67756C17.9631 4.1863 17.6859 4.83794 17.2811 5.12803C16.3869 5.76451 15.2437 6.06542 14.2217 5.92903C13.9792 5.89656 13.6177 5.78616 13.2041 5.62812C13.4228 4.34001 14.6223 3.31819 15.6745 2.90253C16.0794 2.74233 17.0472 2.49553 17.6253 3.47406C17.6621 3.53467 17.6968 3.60179 17.7292 3.67323V3.67756Z"
                      fill="#B8BBFA"
                    />
                  </svg>
                </div>
              </div>

              {/* Create Event Button */}
              <div className="absolute bottom-3 sm:bottom-7 right-3 sm:right-7">
                <button
                  onClick={() => navigate("/create-event")}
                  className="flex items-center gap-2 bg-white border border-white rounded-full sm:px-4 px-2 py-2.5 text-sm font-semibold text-[#343CD8] hover:bg-gray-50 transition-colors shadow-sm"
                >
                  Create an Event
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      opacity="0.4"
                      d="M9.9974 18.3327C14.5998 18.3327 18.3307 14.6017 18.3307 9.99935C18.3307 5.39698 14.5998 1.66602 9.9974 1.66602C5.39502 1.66602 1.66406 5.39698 1.66406 9.99935C1.66406 14.6017 5.39502 18.3327 9.9974 18.3327Z"
                      fill="#343CD8"
                    />
                    <path
                      d="M13.3609 9.5582L10.8609 7.0582C10.6193 6.81654 10.2193 6.81654 9.9776 7.0582C9.73594 7.29987 9.73594 7.69987 9.9776 7.94154L11.4109 9.37487H7.08594C6.74427 9.37487 6.46094 9.6582 6.46094 9.99987C6.46094 10.3415 6.74427 10.6249 7.08594 10.6249H11.4109L9.9776 12.0582C9.73594 12.2999 9.73594 12.6999 9.9776 12.9415C10.1026 13.0665 10.2609 13.1249 10.4193 13.1249C10.5776 13.1249 10.7359 13.0665 10.8609 12.9415L13.3609 10.4415C13.6026 10.1999 13.6026 9.79987 13.3609 9.5582Z"
                      fill="#343CD8"
                    />
                  </svg>
                </button>
              </div>
            </div>
            <div className="flex flex-col md:flex-row justify-between">
              <div className="md:max-w-[270px] w-full ">
                <div
                  onClick={() => setIsGuestListModalOpen(true)}
                  className="bg-white cursor-pointer rounded-[20px]  [box-shadow:0px_33.75px_33.75px_0px_#A6A6A60A,0px_67.49px_84.37px_0px_#A6A6A61A,0px_39.68px_198.42px_0px_#0000000F] p-4 md:max-w-[270px] w-full h-[195px] relative overflow-hidden"
                >
                  <div className="relative  flex flex-col justify-between h-full items-start">
                    <svg
                      width="48"
                      height="48"
                      viewBox="0 0 48 48"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <circle cx="24" cy="24" r="24" fill="#EBF3FE" />
                      <path
                        opacity="0.4"
                        d="M21 14C18.38 14 16.25 16.13 16.25 18.75C16.25 21.32 18.26 23.4 20.88 23.49C20.96 23.48 21.04 23.48 21.1 23.49C21.12 23.49 21.13 23.49 21.15 23.49C21.16 23.49 21.16 23.49 21.17 23.49C23.73 23.4 25.74 21.32 25.75 18.75C25.75 16.13 23.62 14 21 14Z"
                        fill="#365B96"
                      />
                      <path
                        d="M26.0809 26.1509C23.2909 24.2909 18.7409 24.2909 15.9309 26.1509C14.6609 27.0009 13.9609 28.1509 13.9609 29.3809C13.9609 30.6109 14.6609 31.7509 15.9209 32.5909C17.3209 33.5309 19.1609 34.0009 21.0009 34.0009C22.8409 34.0009 24.6809 33.5309 26.0809 32.5909C27.3409 31.7409 28.0409 30.6009 28.0409 29.3609C28.0309 28.1309 27.3409 26.9909 26.0809 26.1509Z"
                        fill="#365B96"
                      />
                      <path
                        opacity="0.4"
                        d="M31.9894 19.3401C32.1494 21.2801 30.7694 22.9801 28.8594 23.2101C28.8494 23.2101 28.8494 23.2101 28.8394 23.2101H28.8094C28.7494 23.2101 28.6894 23.2101 28.6394 23.2301C27.6694 23.2801 26.7794 22.9701 26.1094 22.4001C27.1394 21.4801 27.7294 20.1001 27.6094 18.6001C27.5394 17.7901 27.2594 17.0501 26.8394 16.4201C27.2194 16.2301 27.6594 16.1101 28.1094 16.0701C30.0694 15.9001 31.8194 17.3601 31.9894 19.3401Z"
                        fill="#365B96"
                      />
                      <path
                        d="M33.9922 28.5904C33.9122 29.5604 33.2922 30.4004 32.2522 30.9704C31.2522 31.5204 29.9922 31.7804 28.7422 31.7504C29.4622 31.1004 29.8822 30.2904 29.9622 29.4304C30.0622 28.1904 29.4722 27.0004 28.2922 26.0504C27.6222 25.5204 26.8422 25.1004 25.9922 24.7904C28.2022 24.1504 30.9822 24.5804 32.6922 25.9604C33.6122 26.7004 34.0822 27.6304 33.9922 28.5904Z"
                        fill="#365B96"
                      />
                    </svg>

                    <div>
                      <p className=" text-xs uppercase text-primary-750 tracking-[0.12em] mb-2">
                        Guest Management
                      </p>
                      <p className="text-xs md:text-xl font-semibold">
                        Add & Manage
                        <br /> guest for your event{" "}
                      </p>
                    </div>
                  </div>
                  <div className="absolute bottom-[20px] right-[-10px] pointer-events-none">
                    <div className="space-y-2 flex flex-col justify-end items-end">
                      <div className="bg-[#EBF3FE] rounded-lg p-2 w-[140px]">
                        <p className="text-[#00008C] text-[7px] font-semibold">
                          Olivia Rhye
                        </p>
                        <p className="text-[#535862] text-[6px]">
                          <EMAIL>
                        </p>
                      </div>
                      <div className="bg-[#EBF3FE] rounded-lg p-2 w-[100px]">
                        <p className="text-[#00008C] text-[7px] font-semibold">
                          Jacon Meyer
                        </p>
                        <p className="text-[#535862] text-[6px]">
                          <EMAIL>
                        </p>
                      </div>
                      <div className="bg-[#EBF3FE] rounded-lg p-2 w-[70px]">
                        <p className="text-[#00008C] text-[7px] font-semibold">
                          Ray Donovan
                        </p>
                        <p className="text-[#535862] text-[6px]">
                          donovan@untit...
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  onClick={() => setIsBudgetPlannerModalOpen(true)}
                  className="bg-white cursor-pointer rounded-[20px] mt-5 p-4 md:max-w-[270px] w-full h-[195px] relative overflow-hidden "
                >
                  <div className="relative  flex flex-col justify-between h-full items-start">
                    <div className="flex justify-between items-center w-full">
                      <svg
                        width="48"
                        height="48"
                        viewBox="0 0 48 48"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <circle cx="24" cy="24" r="24" fill="#FDF2ED" />
                        <path
                          d="M35.2799 18.1083C34.5332 15.5766 32.4216 13.465 29.8899 12.7183C27.9649 12.1583 26.6349 12.205 25.7132 12.8933C24.6049 13.7216 24.4766 15.215 24.4766 16.2766V19.1816C24.4766 22.0516 25.7832 23.51 28.3499 23.51H31.6982C32.7482 23.51 34.2532 23.3816 35.0816 22.2733C35.7932 21.3633 35.8516 20.0333 35.2799 18.1083Z"
                          fill="#FF6630"
                        />
                        <path
                          opacity="0.4"
                          d="M32.0613 25.5854C31.758 25.2354 31.3146 25.037 30.8596 25.037H26.683C24.6296 25.037 22.9613 23.3687 22.9613 21.3154V17.1387C22.9613 16.6837 22.763 16.2404 22.413 15.937C22.0746 15.6337 21.608 15.4937 21.1646 15.552C18.423 15.902 15.903 17.407 14.258 19.6704C12.6013 21.9454 11.9946 24.722 12.5196 27.4987C13.278 31.512 16.4863 34.7204 20.5113 35.4787C21.153 35.607 21.7946 35.6654 22.4363 35.6654C24.548 35.6654 26.5896 35.012 28.328 33.7404C30.5913 32.0954 32.0963 29.5754 32.4463 26.8337C32.5046 26.3787 32.3646 25.9237 32.0613 25.5854Z"
                          fill="#FF6630"
                        />
                      </svg>

                      <div className="bg-white px-3 py-1 rounded-full border border-gray-300"></div>
                    </div>

                    <div>
                      <p className=" text-xs uppercase text-primary-750 tracking-[0.12em] mb-2">
                        BUDGET PLANNER{" "}
                      </p>
                      <p className="text-xs md:text-xl font-medium">
                        Plan a Budget for <br /> your event
                      </p>
                    </div>
                  </div>
                  <div className="absolute bottom-[15px] right-[-10px] pointer-events-none ">
                    <svg
                      width="270"
                      height="195"
                      viewBox="0 0 270 195"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M135 109C135 147.66 166.34 179 205 179C243.66 179 275 147.66 275 109C275 70.3401 243.66 39 205 39C166.34 39 135 70.3401 135 109ZM247.714 109C247.714 132.59 228.59 151.714 205 151.714C181.41 151.714 162.286 132.59 162.286 109C162.286 85.4098 181.41 66.2861 205 66.2861C228.59 66.2861 247.714 85.4098 247.714 109Z"
                        fill="#FEFAF8"
                      />
                      <path
                        d="M259.796 97.9002C267.423 96.3551 275.011 101.319 275 109.101C274.986 118.446 273.101 127.743 269.403 136.428C263.852 149.461 254.478 160.503 242.52 168.095C230.561 175.688 216.581 179.474 202.425 178.953C192.991 178.605 183.776 176.356 175.313 172.393C168.265 169.092 167.001 160.114 171.644 153.868C176.287 147.622 185.121 146.604 192.551 148.921C196.069 150.018 199.737 150.651 203.462 150.789C211.918 151.1 220.27 148.838 227.414 144.303C234.558 139.767 240.157 133.171 243.473 125.385C244.933 121.956 245.921 118.367 246.424 114.716C247.488 107.007 252.168 99.4453 259.796 97.9002Z"
                        fill="#FDF2ED"
                      />
                      <path
                        d="M218.355 54.6629C220.206 47.1298 227.908 42.4011 234.921 45.7169C239.728 47.99 244.275 50.8101 248.467 54.1313C256.343 60.3706 262.784 68.2324 267.351 77.1823C271.918 86.1321 274.505 95.9603 274.936 105.999C275.165 111.342 274.78 116.679 273.8 121.906C272.37 129.53 264.022 132.991 256.836 130.07C249.649 127.149 246.459 118.881 246.854 111.133C246.921 109.827 246.926 108.515 246.87 107.203C246.612 101.193 245.063 95.3091 242.329 89.9509C239.595 84.5927 235.739 79.8859 231.024 76.1504C229.994 75.3349 228.929 74.5698 227.832 73.857C221.327 69.6309 216.504 62.1959 218.355 54.6629Z"
                        fill="#FDEAE2"
                      />
                      <path
                        d="M173.291 62.8979C168.895 56.5065 170.483 47.6098 177.622 44.5759C186.524 40.7933 196.161 38.8759 205.933 39.0062C215.705 39.1365 225.288 41.31 234.085 45.3286C241.141 48.5517 242.492 57.4877 237.927 63.7596C233.362 70.0315 224.569 71.1376 217.144 68.8896C213.407 67.7582 209.509 67.1478 205.559 67.0952C201.608 67.0425 197.695 67.5487 193.929 68.5801C186.448 70.6294 177.687 69.2893 173.291 62.8979Z"
                        fill="#FBDACB"
                      />
                      <g filter="url(#filter0_ddd_14963_117703)">
                        <rect
                          x="209.234"
                          y="25"
                          width="51.2"
                          height="27.2"
                          rx="3.2"
                          transform="rotate(12.8664 209.234 25)"
                          fill="white"
                        />
                        <path
                          d="M217.104 37.874L217.456 36.3329L217.094 36.2502L217.199 35.7885L217.561 35.8712L217.664 35.4219L217.302 35.3393L217.407 34.8776L217.769 34.9602L218.121 33.4191L219.101 33.6429L219.236 35.2951L219.803 35.4248L220.155 33.8837L220.848 34.0419L220.496 35.583L220.852 35.6643L220.746 36.126L220.39 36.0447L220.288 36.494L220.644 36.5752L220.538 37.0369L220.182 36.9557L219.83 38.4968L218.845 38.2716L218.697 36.6165L218.148 36.4911L217.796 38.0322L217.104 37.874ZM218.254 36.0294L218.666 36.1234L218.631 35.6428L218.344 35.5773L218.254 36.0294ZM219.379 37.3695L219.41 37.3767L219.515 36.8032L219.346 36.7647L219.379 37.3695ZM218.437 35.1127L218.612 35.1526L218.568 34.5123L218.543 34.5066L218.437 35.1127ZM219.321 36.2731L219.608 36.3386L219.698 35.8865L219.28 35.7911L219.321 36.2731ZM220.556 38.6625L220.75 37.8139L221.81 38.0562L222.366 35.6229L221.311 35.5854L221.488 34.8117L223.02 34.6299L223.538 34.7482L222.734 38.2671L223.707 38.4895L223.513 39.338L220.556 38.6625ZM223.467 40.0757L224.198 38.4834L225.128 38.6958L224.097 40.2197L223.467 40.0757ZM226.892 40.1885C226.571 40.1153 226.313 39.9928 226.116 39.8208C225.924 39.6457 225.782 39.4405 225.692 39.2054C225.61 38.9679 225.567 38.7173 225.561 38.4535C225.557 38.1855 225.585 37.9205 225.645 37.6584C225.757 37.1676 225.92 36.7606 226.134 36.4375C226.353 36.1111 226.609 35.8807 226.902 35.7464C227.049 35.6751 227.205 35.6341 227.37 35.6235C227.535 35.6088 227.707 35.6218 227.886 35.6627C228.194 35.733 228.437 35.8563 228.616 36.0328C228.795 36.2051 228.921 36.4023 228.992 36.6244C229.028 36.7375 229.048 36.8515 229.052 36.9663C229.062 37.0779 229.056 37.1882 229.036 37.2973L228.144 37.0935C228.146 36.9278 228.114 36.7914 228.047 36.6841C227.984 36.5735 227.868 36.4988 227.697 36.4599C227.535 36.4228 227.388 36.4439 227.256 36.5232C227.128 36.6034 227.017 36.7377 226.921 36.9259C226.826 37.1142 226.743 37.3534 226.672 37.6437C226.803 37.5116 226.961 37.4121 227.147 37.3452C227.337 37.2792 227.541 37.2709 227.757 37.3203C227.965 37.3678 228.155 37.4726 228.328 37.6347C228.506 37.7977 228.628 37.9986 228.696 38.2373C228.768 38.477 228.771 38.7445 228.703 39.0398C228.642 39.3102 228.523 39.5435 228.347 39.7396C228.17 39.9357 227.957 40.0772 227.705 40.1641C227.446 40.2449 227.175 40.2531 226.892 40.1885ZM227.033 39.341C227.141 39.3657 227.244 39.3631 227.343 39.3331C227.446 39.3041 227.535 39.25 227.61 39.1708C227.689 39.0926 227.743 38.9891 227.773 38.8601C227.817 38.6688 227.793 38.5016 227.704 38.3585C227.614 38.2154 227.477 38.123 227.294 38.0812C227.19 38.0574 227.086 38.0622 226.982 38.0953C226.88 38.1243 226.789 38.1779 226.709 38.2562C226.63 38.3344 226.577 38.4338 226.549 38.5544C226.52 38.6834 226.521 38.8018 226.554 38.9099C226.59 39.0188 226.65 39.1113 226.733 39.1872C226.816 39.2631 226.916 39.3144 227.033 39.341ZM230.152 40.9332C229.84 40.8619 229.576 40.7447 229.359 40.5814C229.147 40.4191 228.993 40.222 228.897 39.99C228.802 39.7539 228.779 39.4992 228.829 39.226L229.783 39.4441C229.786 39.5848 229.837 39.7188 229.934 39.8462C230.032 39.9736 230.166 40.0568 230.337 40.0957C230.462 40.1242 230.58 40.1161 230.69 40.0714C230.805 40.0277 230.905 39.9563 230.989 39.8573C231.074 39.7541 231.132 39.6318 231.164 39.4904C231.215 39.2699 231.191 39.0851 231.094 38.936C231.002 38.7837 230.864 38.6866 230.681 38.6448C230.528 38.6096 230.383 38.6204 230.247 38.677C230.117 38.7303 230.016 38.808 229.945 38.9098L229.003 38.6946L229.963 36.2158L232.471 36.7887L232.274 37.6497L230.49 37.2421L230.145 38.0037C230.247 37.9395 230.377 37.897 230.535 37.8762C230.694 37.8512 230.863 37.8591 231.042 37.9C231.371 37.975 231.618 38.1082 231.784 38.2993C231.956 38.4873 232.069 38.7034 232.122 38.9476C232.141 39.0745 232.15 39.2012 232.147 39.3275C232.15 39.4506 232.137 39.5746 232.108 39.6994C232.041 39.9947 231.914 40.2503 231.729 40.4663C231.545 40.6781 231.316 40.8271 231.043 40.9135C230.774 41.0007 230.477 41.0073 230.152 40.9332ZM233.488 41.695C233.088 41.6038 232.791 41.4308 232.595 41.1761C232.399 40.9213 232.286 40.611 232.256 40.2453C232.226 39.8795 232.259 39.4844 232.356 39.0602C232.453 38.6359 232.595 38.2656 232.781 37.9492C232.967 37.6328 233.203 37.4024 233.49 37.2578C233.777 37.1132 234.12 37.0866 234.519 37.1778C234.919 37.269 235.215 37.4394 235.407 37.6891C235.604 37.9397 235.717 38.2479 235.747 38.6137C235.777 38.9794 235.743 39.3786 235.644 39.8112C235.546 40.2438 235.403 40.6183 235.217 40.9347C235.031 41.2511 234.795 41.4795 234.509 41.6198C234.227 41.7612 233.887 41.7863 233.488 41.695ZM233.687 40.8215C233.912 40.8728 234.104 40.7877 234.264 40.566C234.425 40.3402 234.554 40.0131 234.652 39.5846C234.75 39.1562 234.775 38.8074 234.727 38.5382C234.68 38.2649 234.544 38.1026 234.32 38.0513C234.095 38 233.9 38.0868 233.735 38.3116C233.575 38.5333 233.446 38.8583 233.348 39.2868C233.25 39.7152 233.225 40.0661 233.272 40.3394C233.324 40.6095 233.462 40.7702 233.687 40.8215ZM235.093 42.7313L235.824 41.139L236.754 41.3513L235.723 42.8752L235.093 42.7313ZM238.381 42.8127C237.981 42.7215 237.684 42.5485 237.488 42.2937C237.292 42.0389 237.179 41.7287 237.149 41.3629C237.119 40.9971 237.152 40.6021 237.249 40.1778C237.346 39.7535 237.488 39.3832 237.674 39.0668C237.86 38.7504 238.096 38.52 238.383 38.3754C238.67 38.2309 239.013 38.2042 239.412 38.2954C239.812 38.3866 240.108 38.5571 240.3 38.8067C240.497 39.0573 240.61 39.3655 240.64 39.7313C240.67 40.0971 240.636 40.4963 240.537 40.9289C240.439 41.3614 240.296 41.7359 240.11 42.0523C239.924 42.3687 239.688 42.5971 239.402 42.7375C239.12 42.8788 238.78 42.9039 238.381 42.8127ZM238.58 41.9392C238.805 41.9905 238.997 41.9053 239.157 41.6836C239.318 41.4578 239.447 41.1307 239.545 40.7023C239.643 40.2738 239.668 39.925 239.62 39.6559C239.573 39.3825 239.437 39.2202 239.213 39.1689C238.988 39.1176 238.793 39.2044 238.628 39.4293C238.468 39.6509 238.339 39.976 238.241 40.4044C238.143 40.8328 238.118 41.1837 238.165 41.457C238.217 41.7272 238.355 41.8879 238.58 41.9392ZM241.747 43.5816C241.348 43.4904 241.05 43.3174 240.855 43.0627C240.659 42.8079 240.546 42.4976 240.516 42.1319C240.486 41.7661 240.519 41.371 240.616 40.9468C240.713 40.5225 240.854 40.1522 241.04 39.8358C241.226 39.5194 241.463 39.2889 241.75 39.1444C242.036 38.9998 242.38 38.9732 242.779 39.0644C243.178 39.1556 243.474 39.326 243.666 39.5757C243.863 39.8263 243.977 40.1345 244.007 40.5003C244.037 40.866 244.003 41.2652 243.904 41.6978C243.805 42.1304 243.663 42.5049 243.477 42.8213C243.291 43.1377 243.055 43.366 242.769 43.5064C242.487 43.6478 242.146 43.6728 241.747 43.5816ZM241.947 42.7081C242.171 42.7594 242.364 42.6743 242.524 42.4526C242.685 42.2268 242.814 41.8997 242.912 41.4712C243.01 41.0428 243.035 40.694 242.987 40.4248C242.94 40.1515 242.804 39.9892 242.579 39.9379C242.355 39.8866 242.16 39.9734 241.995 40.1982C241.835 40.4199 241.706 40.7449 241.608 41.1734C241.51 41.6018 241.485 41.9527 241.532 42.226C241.584 42.4961 241.722 42.6568 241.947 42.7081ZM245.114 44.3506C244.714 44.2594 244.417 44.0864 244.221 43.8316C244.025 43.5769 243.913 43.2666 243.882 42.9008C243.852 42.535 243.885 42.14 243.982 41.7157C244.079 41.2915 244.221 40.9211 244.407 40.6048C244.593 40.2884 244.829 40.0579 245.116 39.9134C245.403 39.7688 245.746 39.7421 246.145 39.8333C246.545 39.9246 246.841 40.095 247.033 40.3446C247.23 40.5952 247.343 40.9034 247.373 41.2692C247.404 41.635 247.369 42.0342 247.27 42.4668C247.172 42.8994 247.029 43.2739 246.843 43.5902C246.657 43.9066 246.421 44.135 246.135 44.2754C245.853 44.4167 245.513 44.4418 245.114 44.3506ZM245.313 43.4771C245.538 43.5284 245.73 43.4432 245.89 43.2216C246.051 42.9958 246.181 42.6686 246.278 42.2402C246.376 41.8118 246.401 41.463 246.353 41.1938C246.306 40.9205 246.17 40.7582 245.946 40.7069C245.721 40.6555 245.526 40.7423 245.361 40.9672C245.201 41.1889 245.072 41.5139 244.974 41.9423C244.877 42.3708 244.851 42.7216 244.898 42.995C244.95 43.2651 245.088 43.4258 245.313 43.4771Z"
                          fill="#414651"
                        />
                        <path
                          d="M217.124 47.0458L221.804 48.1146L222.873 43.4352L218.193 42.3663L217.124 47.0458ZM224.791 48.5114C224.548 48.4558 224.35 48.3714 224.199 48.258C224.051 48.1454 223.94 48.0133 223.866 47.8619C223.795 47.7112 223.754 47.5541 223.742 47.3905C223.731 47.2239 223.744 47.061 223.78 46.9019C223.846 46.6149 223.952 46.3569 224.1 46.128C224.251 45.8967 224.433 45.7051 224.644 45.5533C224.86 45.4022 225.095 45.3 225.35 45.2466C225.605 45.1902 225.871 45.1933 226.145 45.256C226.52 45.3415 226.789 45.4982 226.954 45.7262C227.119 45.9511 227.169 46.2252 227.105 46.5487L226.661 46.4471C226.702 46.2367 226.67 46.0569 226.563 45.9077C226.456 45.7554 226.272 45.6493 226.01 45.5895C225.795 45.5403 225.589 45.541 225.394 45.5916C225.202 45.6428 225.023 45.7334 224.859 45.8633C224.699 45.9908 224.561 46.1496 224.445 46.3398C224.333 46.5277 224.251 46.7354 224.199 46.9632C224.127 47.2783 224.156 47.5424 224.284 47.7556C224.413 47.9687 224.627 48.1095 224.926 48.1779C225.188 48.2378 225.419 48.2281 225.617 48.1487C225.819 48.0701 225.984 47.9386 226.111 47.7544L226.556 47.8559C226.361 48.1463 226.111 48.3501 225.806 48.4674C225.5 48.5815 225.162 48.5962 224.791 48.5114ZM227.452 49.1192C227.312 49.0872 227.191 49.0349 227.089 48.9625C226.988 48.89 226.913 48.8007 226.865 48.6946C226.821 48.586 226.814 48.4631 226.846 48.3259C226.889 48.1356 226.978 47.9917 227.112 47.8942C227.249 47.7944 227.412 47.7348 227.601 47.7154C227.792 47.6968 227.989 47.7106 228.192 47.7569L228.814 47.8991C228.83 47.8599 228.842 47.8267 228.852 47.7993C228.862 47.7688 228.87 47.7411 228.876 47.7162C228.91 47.5664 228.892 47.4426 228.823 47.3446C228.753 47.2467 228.643 47.1806 228.494 47.1464C228.35 47.1136 228.206 47.1185 228.062 47.1611C227.918 47.2036 227.8 47.2964 227.708 47.4396L227.301 47.3466C227.394 47.184 227.511 47.0582 227.653 46.9691C227.795 46.8801 227.948 46.8249 228.114 46.8037C228.284 46.78 228.453 46.7875 228.621 46.8259C228.786 46.8637 228.925 46.9234 229.038 47.0049C229.151 47.0864 229.229 47.1896 229.273 47.3145C229.32 47.4369 229.325 47.5808 229.287 47.7462C229.277 47.7899 229.267 47.8269 229.257 47.8574C229.247 47.8848 229.234 47.9196 229.218 47.9618L228.929 48.6443C228.915 48.6772 228.904 48.7073 228.894 48.7347C228.884 48.762 228.877 48.7882 228.871 48.8131C228.858 48.8724 228.861 48.924 228.88 48.9678C228.904 49.0093 228.956 49.0392 229.037 49.0578L228.897 49.3902C228.732 49.3524 228.612 49.2873 228.538 49.1949C228.464 49.1025 228.438 48.9784 228.46 48.8227C228.392 48.8924 228.308 48.957 228.209 49.0165C228.11 49.0759 227.997 49.1174 227.871 49.141C227.745 49.1614 227.605 49.1542 227.452 49.1192ZM227.645 48.8137C227.801 48.8493 227.948 48.8401 228.085 48.7861C228.225 48.7328 228.347 48.6523 228.45 48.5446C228.556 48.4376 228.636 48.3196 228.688 48.1904L228.099 48.0557C227.977 48.0279 227.858 48.0172 227.742 48.0234C227.629 48.0304 227.53 48.0619 227.445 48.118C227.36 48.171 227.305 48.2567 227.277 48.3753C227.25 48.4938 227.273 48.5892 227.345 48.6615C227.42 48.7345 227.52 48.7852 227.645 48.8137ZM230.191 49.6857C229.997 49.6415 229.856 49.5699 229.767 49.4708C229.679 49.3685 229.656 49.2254 229.698 49.0413C229.705 49.0101 229.714 48.9777 229.725 48.9441C229.737 48.9074 229.75 48.8695 229.766 48.8304L230.28 47.6135L229.92 47.5312L230.056 47.2129L230.417 47.2952L230.642 46.7608L231.04 46.8516L230.81 47.385L231.498 47.5421L231.361 47.8604L230.678 47.7044L230.159 48.9201C230.15 48.9444 230.141 48.9671 230.133 48.9882C230.128 49.01 230.124 49.0303 230.119 49.049C230.097 49.1457 230.107 49.2185 230.148 49.2673C230.194 49.3138 230.274 49.3502 230.389 49.3765L230.693 49.446L230.556 49.769L230.191 49.6857ZM232.015 50.1614C231.728 50.0959 231.517 49.9542 231.383 49.7365C231.252 49.5195 231.221 49.2612 231.289 48.9618C231.343 48.7278 231.429 48.5292 231.549 48.3661C231.671 48.2037 231.812 48.0766 231.97 47.9849C232.133 47.8907 232.303 47.831 232.479 47.8056C232.656 47.7803 232.828 47.7869 232.996 47.8254C233.224 47.8774 233.395 47.9639 233.508 48.0851C233.626 48.2038 233.698 48.3385 233.726 48.4893C233.758 48.6377 233.758 48.7821 233.726 48.9225C233.711 48.9849 233.687 49.0548 233.653 49.1324C233.623 49.2075 233.595 49.2733 233.569 49.3297L231.716 48.9065C231.707 48.9307 231.698 48.955 231.689 48.9792C231.684 49.001 231.679 49.0229 231.674 49.0447C231.624 49.2662 231.648 49.4458 231.748 49.5835C231.849 49.7181 231.983 49.8046 232.152 49.8431C232.314 49.8801 232.464 49.8751 232.603 49.828C232.746 49.7785 232.866 49.6992 232.963 49.5901L233.351 49.6789C233.256 49.8081 233.138 49.9157 232.997 50.0016C232.859 50.0883 232.706 50.1469 232.538 50.1775C232.373 50.2088 232.199 50.2035 232.015 50.1614ZM231.846 48.6162L233.306 48.9497C233.313 48.9317 233.319 48.9149 233.322 48.8993C233.33 48.8813 233.335 48.8645 233.339 48.8489C233.379 48.6711 233.357 48.5198 233.27 48.395C233.184 48.2702 233.05 48.1872 232.869 48.1458C232.682 48.1031 232.493 48.1205 232.301 48.1982C232.11 48.2727 231.958 48.4121 231.846 48.6162ZM233.658 50.4776L234.595 48.2496L234.955 48.3319L234.819 48.7341C234.952 48.6134 235.109 48.5294 235.29 48.4822C235.475 48.4358 235.672 48.4364 235.881 48.4842L235.716 48.8799L235.609 48.8553C235.422 48.8126 235.26 48.8085 235.124 48.8431C234.989 48.8746 234.875 48.9371 234.781 49.0306C234.688 49.121 234.613 49.2335 234.556 49.3682L234.051 50.5674L233.658 50.4776ZM235.427 50.8818L236.365 48.6538L236.758 48.7435L235.82 50.9716L235.427 50.8818ZM236.819 48.216C236.741 48.1982 236.681 48.1582 236.64 48.0962C236.601 48.0349 236.591 47.9652 236.609 47.8873C236.626 47.8093 236.666 47.751 236.727 47.7125C236.792 47.6747 236.863 47.6647 236.941 47.6825C237.016 47.6996 237.074 47.7392 237.116 47.8012C237.157 47.8632 237.169 47.9333 237.151 48.0112C237.134 48.0892 237.092 48.1471 237.028 48.1849C236.964 48.2227 236.894 48.2331 236.819 48.216ZM236.514 51.13L237.451 48.902L237.807 48.9832L237.664 49.3937C237.798 49.2667 237.953 49.179 238.128 49.1303C238.303 49.0817 238.479 49.0778 238.657 49.1184C238.888 49.1711 239.056 49.2783 239.16 49.44C239.265 49.5985 239.29 49.7979 239.235 50.0381C239.225 50.0818 239.212 50.1263 239.195 50.1717C239.182 50.2146 239.165 50.2584 239.145 50.3031L238.596 51.6056L238.203 51.5159L238.733 50.2533C238.748 50.2142 238.762 50.1762 238.774 50.1395C238.788 50.1035 238.8 50.0683 238.808 50.034C238.884 49.7002 238.758 49.4959 238.431 49.4211C238.296 49.3905 238.163 49.3943 238.029 49.4328C237.899 49.4719 237.779 49.5415 237.671 49.6415C237.563 49.7416 237.476 49.8695 237.411 50.0253L236.907 51.2198L236.514 51.13ZM239.692 52.9983C239.524 52.9598 239.371 52.8985 239.232 52.8144C239.094 52.7303 238.989 52.6276 238.919 52.5064C238.851 52.3859 238.834 52.2508 238.868 52.1011C238.898 51.9732 238.962 51.8647 239.061 51.7758C239.16 51.6868 239.282 51.6327 239.428 51.6134C239.37 51.5641 239.331 51.5044 239.311 51.4341C239.294 51.3646 239.293 51.2971 239.308 51.2316C239.335 51.113 239.395 51.0152 239.489 50.938C239.582 50.8608 239.684 50.807 239.796 50.7768C239.762 50.6968 239.743 50.6137 239.74 50.5276C239.737 50.4384 239.752 50.3434 239.785 50.2426C239.84 50.0746 239.925 49.9397 240.04 49.838C240.159 49.7338 240.298 49.6654 240.456 49.6327C240.618 49.6007 240.788 49.605 240.965 49.6456C241.096 49.6755 241.214 49.722 241.318 49.7851L242.179 49.9818L242.047 50.3012L241.644 50.2093C241.664 50.2795 241.672 50.3535 241.667 50.4312C241.667 50.5065 241.654 50.584 241.629 50.6637C241.573 50.838 241.484 50.9752 241.363 51.0755C241.241 51.1758 241.1 51.242 240.938 51.274C240.78 51.3036 240.616 51.2988 240.444 51.2596C240.357 51.2397 240.276 51.2114 240.203 51.1749C240.132 51.1391 240.069 51.0969 240.015 51.0483C239.95 51.0434 239.892 51.0547 239.84 51.0822C239.791 51.1103 239.759 51.1541 239.746 51.2133C239.714 51.3506 239.813 51.4452 240.04 51.4973L240.452 51.5913C240.683 51.644 240.86 51.7386 240.983 51.8751C241.107 52.0084 241.146 52.1764 241.099 52.3792C241.057 52.5664 240.97 52.7141 240.841 52.8223C240.71 52.9337 240.546 53.0045 240.349 53.035C240.155 53.0661 239.936 53.0539 239.692 52.9983ZM239.814 52.6814C239.951 52.7128 240.082 52.7245 240.205 52.7165C240.327 52.7117 240.431 52.6812 240.516 52.6252C240.601 52.5691 240.657 52.4833 240.683 52.3679C240.707 52.265 240.69 52.1741 240.632 52.0953C240.575 52.0165 240.476 51.9611 240.336 51.929L239.938 51.8382C239.779 51.8019 239.636 51.8071 239.511 51.8539C239.385 51.9007 239.305 51.999 239.271 52.1487C239.241 52.2798 239.281 52.3907 239.392 52.4816C239.502 52.5756 239.643 52.6422 239.814 52.6814ZM240.538 50.9363C240.7 50.9734 240.848 50.9579 240.982 50.89C241.115 50.8221 241.205 50.7178 241.25 50.5771C241.295 50.4365 241.28 50.3083 241.205 50.1926C241.133 50.0777 241.015 50.0016 240.853 49.9646C240.691 49.9275 240.546 49.9437 240.419 50.0131C240.291 50.0824 240.205 50.1874 240.16 50.3281C240.114 50.4687 240.123 50.5955 240.186 50.7083C240.252 50.8218 240.369 50.8979 240.538 50.9363Z"
                          fill="#535862"
                        />
                        <path
                          d="M230.729 56.7793C231.076 56.8587 231.154 57.3185 230.853 57.508L227.944 59.3347C227.757 59.4522 227.51 59.3958 227.393 59.2088L225.566 56.3005C225.377 55.9988 225.647 55.6185 225.994 55.6978L230.729 56.7793Z"
                          fill="white"
                        />
                      </g>
                      <g filter="url(#filter1_ddd_14963_117703)">
                        <path
                          d="M209.489 161.094C209.139 161.031 209.04 160.575 209.333 160.372L212.155 158.416C212.337 158.29 212.586 158.335 212.712 158.517L214.668 161.339C214.871 161.632 214.619 162.024 214.268 161.961L209.489 161.094Z"
                          fill="white"
                        />
                        <rect
                          x="186.086"
                          y="155.83"
                          width="52.8"
                          height="27.2"
                          rx="3.2"
                          transform="rotate(10.2736 186.086 155.83)"
                          fill="white"
                        />
                        <path
                          d="M195.311 168.479L195.593 166.924L195.228 166.858L195.312 166.392L195.678 166.458L195.76 166.005L195.395 165.938L195.479 165.472L195.844 165.539L196.126 163.983L197.115 164.162L197.324 165.807L197.897 165.911L198.179 164.355L198.878 164.482L198.596 166.037L198.955 166.102L198.871 166.568L198.512 166.503L198.43 166.957L198.789 167.022L198.704 167.488L198.345 167.423L198.063 168.978L197.068 168.798L196.846 167.151L196.292 167.051L196.01 168.606L195.311 168.479ZM196.377 166.585L196.792 166.66L196.736 166.182L196.446 166.129L196.377 166.585ZM197.561 167.873L197.593 167.878L197.671 167.301L197.501 167.27L197.561 167.873ZM196.518 165.661L196.694 165.693L196.621 165.055L196.596 165.05L196.518 165.661ZM197.454 166.78L197.743 166.832L197.813 166.377L197.391 166.3L197.454 166.78ZM198.795 169.111L198.951 168.255L200.021 168.449L200.466 165.993L199.411 166.003L199.552 165.222L201.074 164.971L201.597 165.066L200.953 168.618L201.936 168.796L201.78 169.652L198.795 169.111ZM201.767 170.391L202.426 168.767L203.364 168.937L202.403 170.506L201.767 170.391ZM205.194 170.349C204.871 170.29 204.607 170.179 204.402 170.017C204.202 169.85 204.052 169.652 203.951 169.421C203.858 169.187 203.804 168.939 203.786 168.676C203.77 168.408 203.786 168.142 203.834 167.878C203.923 167.382 204.068 166.968 204.267 166.636C204.471 166.3 204.716 166.058 205.003 165.911C205.147 165.833 205.301 165.785 205.465 165.767C205.629 165.745 205.802 165.75 205.982 165.783C206.293 165.839 206.542 165.951 206.728 166.119C206.915 166.283 207.049 166.475 207.131 166.693C207.172 166.805 207.197 166.918 207.206 167.032C207.221 167.143 207.22 167.254 207.205 167.364L206.304 167.2C206.3 167.035 206.261 166.9 206.189 166.796C206.122 166.688 206.002 166.619 205.83 166.588C205.666 166.558 205.52 166.586 205.392 166.671C205.268 166.757 205.163 166.896 205.076 167.088C204.989 167.281 204.917 167.523 204.859 167.817C204.984 167.679 205.138 167.572 205.32 167.497C205.507 167.422 205.71 167.405 205.928 167.444C206.138 167.482 206.333 167.579 206.514 167.733C206.698 167.887 206.83 168.083 206.908 168.318C206.991 168.554 207.006 168.821 206.952 169.119C206.902 169.392 206.794 169.631 206.627 169.835C206.46 170.038 206.253 170.189 206.005 170.288C205.75 170.38 205.479 170.401 205.194 170.349ZM205.296 169.496C205.406 169.516 205.509 169.508 205.606 169.474C205.708 169.44 205.794 169.382 205.865 169.3C205.941 169.218 205.99 169.112 206.014 168.982C206.049 168.789 206.018 168.623 205.922 168.484C205.826 168.345 205.685 168.259 205.501 168.225C205.396 168.206 205.292 168.216 205.19 168.254C205.088 168.287 205 168.345 204.924 168.427C204.849 168.508 204.8 168.61 204.778 168.732C204.754 168.862 204.761 168.98 204.798 169.087C204.839 169.194 204.903 169.284 204.99 169.356C205.077 169.428 205.179 169.474 205.296 169.496ZM208.485 170.945C208.17 170.888 207.9 170.783 207.677 170.63C207.457 170.477 207.294 170.287 207.188 170.06C207.083 169.828 207.048 169.575 207.085 169.3L208.049 169.474C208.058 169.615 208.114 169.746 208.218 169.869C208.321 169.992 208.459 170.069 208.631 170.1C208.757 170.123 208.875 170.11 208.983 170.06C209.096 170.011 209.192 169.935 209.272 169.833C209.352 169.726 209.405 169.601 209.431 169.458C209.471 169.236 209.439 169.052 209.335 168.907C209.236 168.759 209.095 168.669 208.91 168.635C208.755 168.607 208.61 168.624 208.478 168.687C208.35 168.746 208.253 168.828 208.186 168.933L207.235 168.761L208.082 166.241L210.613 166.7L210.456 167.569L208.655 167.243L208.345 168.019C208.444 167.95 208.572 167.902 208.729 167.874C208.887 167.842 209.056 167.842 209.236 167.875C209.568 167.935 209.821 168.057 209.996 168.24C210.176 168.42 210.298 168.631 210.363 168.873C210.388 168.999 210.402 169.125 210.405 169.251C210.413 169.374 210.406 169.498 210.383 169.624C210.329 169.922 210.214 170.183 210.039 170.407C209.865 170.627 209.643 170.787 209.374 170.885C209.108 170.985 208.812 171.005 208.485 170.945ZM211.851 171.555C211.448 171.482 211.143 171.323 210.936 171.077C210.729 170.832 210.602 170.527 210.555 170.163C210.509 169.799 210.524 169.403 210.602 168.974C210.679 168.546 210.804 168.17 210.975 167.845C211.147 167.521 211.373 167.28 211.653 167.123C211.933 166.965 212.274 166.923 212.677 166.996C213.08 167.069 213.384 167.226 213.587 167.467C213.795 167.708 213.922 168.011 213.969 168.375C214.016 168.739 214 169.139 213.92 169.576C213.841 170.013 213.716 170.393 213.544 170.718C213.373 171.042 213.147 171.281 212.868 171.434C212.593 171.588 212.254 171.628 211.851 171.555ZM212.011 170.674C212.238 170.715 212.426 170.621 212.576 170.392C212.726 170.16 212.841 169.827 212.919 169.394C212.998 168.962 213.007 168.612 212.947 168.346C212.887 168.075 212.744 167.919 212.518 167.878C212.291 167.837 212.1 167.932 211.945 168.164C211.796 168.393 211.681 168.723 211.603 169.156C211.525 169.588 211.515 169.94 211.574 170.211C211.639 170.478 211.784 170.633 212.011 170.674ZM213.501 172.518L214.16 170.894L215.098 171.064L214.138 172.633L213.501 172.518ZM216.79 172.451C216.387 172.377 216.082 172.218 215.875 171.972C215.668 171.727 215.541 171.422 215.494 171.058C215.447 170.694 215.463 170.298 215.54 169.87C215.618 169.441 215.742 169.065 215.914 168.741C216.086 168.416 216.311 168.175 216.591 168.018C216.871 167.86 217.213 167.818 217.616 167.891C218.019 167.964 218.322 168.121 218.526 168.362C218.734 168.603 218.861 168.906 218.908 169.27C218.954 169.634 218.938 170.034 218.859 170.471C218.78 170.908 218.655 171.288 218.483 171.613C218.311 171.937 218.086 172.176 217.807 172.329C217.532 172.483 217.193 172.524 216.79 172.451ZM216.949 171.569C217.176 171.61 217.364 171.516 217.514 171.288C217.665 171.055 217.779 170.722 217.858 170.29C217.936 169.857 217.945 169.508 217.885 169.241C217.826 168.97 217.683 168.814 217.456 168.773C217.229 168.732 217.039 168.827 216.884 169.059C216.734 169.288 216.62 169.619 216.542 170.051C216.463 170.483 216.454 170.835 216.513 171.106C216.577 171.374 216.723 171.528 216.949 171.569ZM220.187 173.066C219.784 172.993 219.479 172.834 219.272 172.588C219.065 172.343 218.939 172.038 218.892 171.674C218.845 171.31 218.861 170.914 218.938 170.485C219.016 170.057 219.14 169.681 219.312 169.356C219.483 169.032 219.709 168.791 219.989 168.634C220.269 168.476 220.611 168.434 221.014 168.507C221.417 168.58 221.72 168.737 221.924 168.978C222.131 169.219 222.259 169.522 222.305 169.886C222.352 170.25 222.336 170.65 222.257 171.087C222.178 171.524 222.052 171.904 221.881 172.229C221.709 172.553 221.484 172.792 221.205 172.945C220.93 173.099 220.59 173.139 220.187 173.066ZM220.347 172.185C220.574 172.226 220.762 172.132 220.912 171.903C221.063 171.671 221.177 171.338 221.256 170.905C221.334 170.473 221.343 170.123 221.283 169.857C221.224 169.586 221.081 169.43 220.854 169.389C220.627 169.348 220.437 169.443 220.282 169.675C220.132 169.904 220.018 170.235 219.939 170.667C219.861 171.099 219.852 171.451 219.911 171.722C219.975 171.989 220.121 172.144 220.347 172.185ZM223.585 173.682C223.182 173.609 222.877 173.45 222.67 173.204C222.463 172.959 222.336 172.654 222.29 172.29C222.243 171.926 222.258 171.53 222.336 171.101C222.414 170.673 222.538 170.297 222.71 169.972C222.881 169.648 223.107 169.407 223.387 169.249C223.667 169.092 224.009 169.05 224.412 169.123C224.815 169.196 225.118 169.353 225.322 169.594C225.529 169.835 225.657 170.138 225.703 170.502C225.75 170.866 225.734 171.266 225.655 171.703C225.576 172.139 225.45 172.52 225.279 172.844C225.107 173.169 224.882 173.408 224.602 173.561C224.327 173.715 223.988 173.755 223.585 173.682ZM223.745 172.801C223.972 172.842 224.16 172.748 224.31 172.519C224.461 172.286 224.575 171.954 224.653 171.521C224.732 171.089 224.741 170.739 224.681 170.473C224.622 170.202 224.479 170.046 224.252 170.005C224.025 169.964 223.834 170.059 223.68 170.291C223.53 170.52 223.416 170.85 223.337 171.283C223.259 171.715 223.249 172.067 223.309 172.338C223.373 172.605 223.518 172.76 223.745 172.801Z"
                          fill="#414651"
                        />
                        <path
                          d="M198.212 178.086L202.935 178.942L203.792 174.219L199.068 173.363L198.212 178.086ZM205.428 175.638L205.858 175.716L205.871 178.684L207.95 176.095L208.38 176.173L205.948 179.147L205.485 179.063L205.428 175.638ZM208.349 179.641C208.059 179.588 207.842 179.456 207.699 179.245C207.558 179.034 207.515 178.777 207.57 178.475C207.613 178.239 207.69 178.037 207.802 177.868C207.917 177.701 208.051 177.567 208.206 177.468C208.364 177.367 208.531 177.3 208.706 177.266C208.881 177.233 209.054 177.232 209.224 177.263C209.454 177.304 209.628 177.383 209.747 177.499C209.869 177.612 209.948 177.744 209.983 177.893C210.021 178.04 210.028 178.184 210.002 178.326C209.99 178.389 209.969 178.46 209.939 178.539C209.912 178.615 209.887 178.682 209.863 178.74L207.993 178.401C207.985 178.425 207.978 178.45 207.97 178.474C207.966 178.496 207.962 178.519 207.958 178.541C207.917 178.764 207.95 178.942 208.056 179.075C208.163 179.205 208.301 179.286 208.471 179.317C208.635 179.346 208.785 179.334 208.921 179.281C209.062 179.225 209.178 179.141 209.27 179.027L209.662 179.098C209.573 179.232 209.459 179.344 209.322 179.437C209.189 179.529 209.038 179.595 208.872 179.633C208.709 179.672 208.535 179.674 208.349 179.641ZM208.11 178.105L209.584 178.372C209.59 178.353 209.595 178.336 209.598 178.321C209.604 178.302 209.609 178.285 209.612 178.27C209.645 178.09 209.615 177.94 209.523 177.819C209.431 177.699 209.294 177.622 209.111 177.589C208.922 177.554 208.734 177.58 208.546 177.667C208.358 177.75 208.213 177.896 208.11 178.105ZM210.004 179.882L210.84 177.614L211.199 177.679L211.075 178.096C211.203 177.963 211.353 177.868 211.526 177.812C211.699 177.755 211.875 177.743 212.055 177.776C212.288 177.818 212.46 177.917 212.571 178.074C212.683 178.228 212.717 178.426 212.673 178.668C212.665 178.712 212.654 178.758 212.639 178.804C212.628 178.847 212.613 178.892 212.595 178.937L212.106 180.263L211.709 180.191L212.181 178.906C212.195 178.866 212.207 178.828 212.217 178.791C212.23 178.754 212.24 178.718 212.246 178.684C212.307 178.347 212.172 178.148 211.842 178.088C211.706 178.064 211.573 178.074 211.441 178.118C211.313 178.163 211.197 178.238 211.093 178.343C210.99 178.448 210.909 178.579 210.851 178.738L210.401 179.954L210.004 179.882ZM213.547 180.583C213.314 180.541 213.141 180.443 213.03 180.289C212.918 180.132 212.884 179.933 212.928 179.69C212.944 179.602 212.97 179.512 213.006 179.421L213.495 178.095L213.892 178.167L213.42 179.453C213.406 179.492 213.393 179.531 213.38 179.567C213.37 179.604 213.362 179.64 213.355 179.675C213.294 180.012 213.429 180.21 213.76 180.27C213.958 180.306 214.15 180.269 214.336 180.16C214.522 180.051 214.66 179.871 214.751 179.621L215.2 178.404L215.597 178.476L214.762 180.745L214.403 180.679L214.527 180.263C214.399 180.396 214.248 180.491 214.075 180.547C213.903 180.603 213.726 180.615 213.547 180.583ZM216.448 181.109C216.158 181.056 215.942 180.924 215.798 180.713C215.657 180.502 215.614 180.245 215.669 179.943C215.712 179.707 215.789 179.505 215.901 179.336C216.016 179.169 216.151 179.035 216.305 178.936C216.463 178.835 216.63 178.768 216.805 178.734C216.98 178.701 217.153 178.7 217.323 178.731C217.553 178.772 217.727 178.851 217.846 178.967C217.969 179.08 218.047 179.212 218.082 179.361C218.12 179.508 218.127 179.652 218.101 179.794C218.09 179.857 218.069 179.928 218.038 180.007C218.011 180.083 217.986 180.15 217.963 180.208L216.092 179.869C216.085 179.893 216.077 179.918 216.069 179.942C216.065 179.965 216.061 179.987 216.057 180.009C216.017 180.232 216.049 180.41 216.155 180.543C216.262 180.673 216.4 180.754 216.57 180.785C216.734 180.814 216.884 180.802 217.021 180.749C217.161 180.693 217.277 180.609 217.369 180.495L217.761 180.566C217.672 180.7 217.559 180.812 217.422 180.905C217.288 180.998 217.138 181.063 216.971 181.101C216.808 181.14 216.634 181.142 216.448 181.109ZM216.209 179.573L217.683 179.84C217.69 179.822 217.694 179.804 217.697 179.789C217.704 179.77 217.708 179.753 217.711 179.738C217.744 179.558 217.714 179.408 217.622 179.287C217.53 179.167 217.393 179.09 217.21 179.057C217.021 179.022 216.833 179.048 216.645 179.135C216.458 179.218 216.312 179.364 216.209 179.573Z"
                          fill="#535862"
                        />
                      </g>
                      <g filter="url(#filter2_ddd_14963_117703)">
                        <rect
                          x="216"
                          y="95.1992"
                          width="47.2"
                          height="27.2"
                          rx="3.2"
                          fill="white"
                        />
                        <path
                          d="M224.537 106V104.419H224.166V103.946H224.537V103.485H224.166V103.011H224.537V101.43H225.542L226.041 103.011H226.623V101.43H227.334V103.011H227.698V103.485H227.334V103.946H227.698V104.419H227.334V106H226.322L225.81 104.419H225.247V106H224.537ZM225.247 103.946H225.67L225.529 103.485H225.234L225.247 103.946ZM226.642 105.002H226.674L226.649 104.419H226.476L226.642 105.002ZM225.222 103.011H225.401L225.215 102.397H225.19L225.222 103.011ZM226.342 103.946H226.636L226.623 103.485H226.194L226.342 103.946ZM228.078 106V105.13H229.166V102.634L228.129 102.832V102.038L229.582 101.52H230.113V105.13H231.111V106H228.078ZM231.23 106.73L231.589 105.014H232.542L231.877 106.73H231.23ZM234.594 106.077C234.266 106.077 233.986 106.015 233.756 105.891C233.53 105.763 233.346 105.595 233.205 105.386C233.073 105.172 232.975 104.938 232.911 104.682C232.847 104.421 232.815 104.157 232.815 103.888C232.815 103.385 232.883 102.951 233.02 102.589C233.161 102.222 233.359 101.94 233.615 101.744C233.743 101.642 233.886 101.567 234.044 101.52C234.202 101.469 234.372 101.443 234.556 101.443C234.872 101.443 235.136 101.509 235.349 101.642C235.563 101.77 235.729 101.934 235.849 102.134C235.908 102.237 235.953 102.343 235.983 102.454C236.017 102.561 236.036 102.67 236.041 102.781H235.125C235.091 102.619 235.029 102.493 234.94 102.403C234.855 102.309 234.724 102.262 234.549 102.262C234.383 102.262 234.244 102.316 234.133 102.422C234.027 102.529 233.948 102.685 233.897 102.89C233.845 103.094 233.818 103.346 233.813 103.645C233.912 103.487 234.044 103.355 234.21 103.248C234.381 103.141 234.577 103.088 234.799 103.088C235.012 103.088 235.221 103.148 235.426 103.267C235.635 103.387 235.8 103.555 235.919 103.773C236.043 103.99 236.105 104.251 236.105 104.554C236.105 104.831 236.041 105.085 235.913 105.315C235.785 105.546 235.608 105.731 235.381 105.872C235.147 106.009 234.884 106.077 234.594 106.077ZM234.543 105.219C234.654 105.219 234.754 105.194 234.844 105.142C234.938 105.091 235.012 105.019 235.068 104.925C235.128 104.831 235.157 104.718 235.157 104.586C235.157 104.389 235.098 104.231 234.978 104.112C234.859 103.993 234.705 103.933 234.517 103.933C234.411 103.933 234.311 103.961 234.217 104.016C234.123 104.067 234.046 104.14 233.986 104.234C233.927 104.327 233.897 104.436 233.897 104.56C233.897 104.692 233.924 104.807 233.98 104.906C234.04 105.004 234.119 105.081 234.217 105.136C234.315 105.191 234.424 105.219 234.543 105.219ZM237.939 106.077C237.619 106.077 237.335 106.021 237.088 105.91C236.844 105.799 236.65 105.642 236.505 105.437C236.36 105.228 236.281 104.985 236.268 104.707H237.248C237.282 104.844 237.361 104.963 237.484 105.066C237.608 105.168 237.757 105.219 237.932 105.219C238.06 105.219 238.173 105.185 238.272 105.117C238.374 105.049 238.455 104.957 238.515 104.842C238.574 104.722 238.604 104.59 238.604 104.445C238.604 104.219 238.54 104.044 238.412 103.92C238.289 103.792 238.133 103.728 237.945 103.728C237.787 103.728 237.649 103.771 237.529 103.856C237.414 103.937 237.333 104.035 237.286 104.15H236.32L236.704 101.52H239.276V102.403H237.446L237.28 103.222C237.365 103.137 237.482 103.067 237.632 103.011C237.781 102.951 237.947 102.922 238.131 102.922C238.468 102.922 238.739 102.996 238.944 103.146C239.153 103.291 239.31 103.476 239.417 103.702C239.464 103.822 239.5 103.943 239.526 104.067C239.556 104.187 239.571 104.31 239.571 104.438C239.571 104.741 239.505 105.019 239.372 105.27C239.24 105.518 239.05 105.714 238.803 105.859C238.56 106.004 238.272 106.077 237.939 106.077ZM241.36 106.077C240.95 106.077 240.622 105.974 240.374 105.77C240.127 105.565 239.948 105.287 239.837 104.938C239.726 104.588 239.67 104.195 239.67 103.76C239.67 103.325 239.726 102.932 239.837 102.582C239.948 102.233 240.127 101.955 240.374 101.75C240.622 101.546 240.95 101.443 241.36 101.443C241.77 101.443 242.096 101.543 242.339 101.744C242.587 101.945 242.766 102.22 242.877 102.57C242.988 102.919 243.043 103.316 243.043 103.76C243.043 104.204 242.988 104.601 242.877 104.95C242.766 105.3 242.587 105.575 242.339 105.776C242.096 105.977 241.77 106.077 241.36 106.077ZM241.36 105.181C241.59 105.181 241.759 105.055 241.866 104.803C241.972 104.547 242.026 104.199 242.026 103.76C242.026 103.321 241.972 102.975 241.866 102.723C241.759 102.467 241.59 102.339 241.36 102.339C241.13 102.339 240.959 102.467 240.848 102.723C240.741 102.975 240.688 103.321 240.688 103.76C240.688 104.199 240.741 104.547 240.848 104.803C240.959 105.055 241.13 105.181 241.36 105.181ZM243.156 106.73L243.514 105.014H244.468L243.802 106.73H243.156ZM246.379 106.077C245.969 106.077 245.641 105.974 245.393 105.77C245.146 105.565 244.967 105.287 244.856 104.938C244.745 104.588 244.689 104.195 244.689 103.76C244.689 103.325 244.745 102.932 244.856 102.582C244.967 102.233 245.146 101.955 245.393 101.75C245.641 101.546 245.969 101.443 246.379 101.443C246.789 101.443 247.115 101.543 247.358 101.744C247.606 101.945 247.785 102.22 247.896 102.57C248.007 102.919 248.062 103.316 248.062 103.76C248.062 104.204 248.007 104.601 247.896 104.95C247.785 105.3 247.606 105.575 247.358 105.776C247.115 105.977 246.789 106.077 246.379 106.077ZM246.379 105.181C246.609 105.181 246.778 105.055 246.885 104.803C246.991 104.547 247.045 104.199 247.045 103.76C247.045 103.321 246.991 102.975 246.885 102.723C246.778 102.467 246.609 102.339 246.379 102.339C246.149 102.339 245.978 102.467 245.867 102.723C245.76 102.975 245.707 103.321 245.707 103.76C245.707 104.199 245.76 104.547 245.867 104.803C245.978 105.055 246.149 105.181 246.379 105.181ZM249.832 106.077C249.423 106.077 249.094 105.974 248.847 105.77C248.599 105.565 248.42 105.287 248.309 104.938C248.198 104.588 248.143 104.195 248.143 103.76C248.143 103.325 248.198 102.932 248.309 102.582C248.42 102.233 248.599 101.955 248.847 101.75C249.094 101.546 249.423 101.443 249.832 101.443C250.242 101.443 250.568 101.543 250.811 101.744C251.059 101.945 251.238 102.22 251.349 102.57C251.46 102.919 251.515 103.316 251.515 103.76C251.515 104.204 251.46 104.601 251.349 104.95C251.238 105.3 251.059 105.575 250.811 105.776C250.568 105.977 250.242 106.077 249.832 106.077ZM249.832 105.181C250.063 105.181 250.231 105.055 250.338 104.803C250.444 104.547 250.498 104.199 250.498 103.76C250.498 103.321 250.444 102.975 250.338 102.723C250.231 102.467 250.063 102.339 249.832 102.339C249.602 102.339 249.431 102.467 249.32 102.723C249.214 102.975 249.16 103.321 249.16 103.76C249.16 104.199 249.214 104.547 249.32 104.803C249.431 105.055 249.602 105.181 249.832 105.181ZM253.285 106.077C252.876 106.077 252.547 105.974 252.3 105.77C252.052 105.565 251.873 105.287 251.762 104.938C251.651 104.588 251.596 104.195 251.596 103.76C251.596 103.325 251.651 102.932 251.762 102.582C251.873 102.233 252.052 101.955 252.3 101.75C252.547 101.546 252.876 101.443 253.285 101.443C253.695 101.443 254.021 101.543 254.265 101.744C254.512 101.945 254.691 102.22 254.802 102.57C254.913 102.919 254.969 103.316 254.969 103.76C254.969 104.204 254.913 104.601 254.802 104.95C254.691 105.3 254.512 105.575 254.265 105.776C254.021 105.977 253.695 106.077 253.285 106.077ZM253.285 105.181C253.516 105.181 253.684 105.055 253.791 104.803C253.898 104.547 253.951 104.199 253.951 103.76C253.951 103.321 253.898 102.975 253.791 102.723C253.684 102.467 253.516 102.339 253.285 102.339C253.055 102.339 252.884 102.467 252.773 102.723C252.667 102.975 252.613 103.321 252.613 103.76C252.613 104.199 252.667 104.547 252.773 104.803C252.884 105.055 253.055 105.181 253.285 105.181Z"
                          fill="#414651"
                        />
                        <path
                          d="M220.602 114.936H225.402V110.136H220.602V114.936ZM226.919 114.6L227.514 111.24H229.458L229.401 111.571H227.86L227.668 112.66H228.997L228.94 112.987H227.61L227.38 114.268H228.921L228.863 114.6H226.919ZM229.501 114.6L229.919 112.219H230.284L230.236 112.651C230.338 112.497 230.469 112.377 230.629 112.291C230.789 112.204 230.961 112.161 231.143 112.161C231.38 112.161 231.567 112.228 231.705 112.363C231.842 112.494 231.911 112.683 231.911 112.929C231.911 112.974 231.908 113.02 231.901 113.068C231.898 113.113 231.892 113.16 231.882 113.208L231.637 114.6H231.234L231.469 113.251C231.476 113.209 231.481 113.169 231.484 113.131C231.49 113.092 231.493 113.056 231.493 113.02C231.493 112.678 231.325 112.507 230.989 112.507C230.852 112.507 230.722 112.54 230.601 112.608C230.482 112.675 230.381 112.769 230.298 112.891C230.215 113.012 230.159 113.156 230.13 113.323L229.905 114.6H229.501ZM233.112 114.6C232.914 114.6 232.76 114.561 232.651 114.484C232.543 114.404 232.488 114.27 232.488 114.081C232.488 114.049 232.49 114.016 232.493 113.98C232.496 113.942 232.501 113.902 232.507 113.86L232.738 112.56H232.368L232.431 112.219H232.8L232.901 111.648H233.309L233.203 112.219H233.909L233.847 112.56H233.146L232.911 113.86C232.907 113.886 232.904 113.91 232.901 113.932C232.901 113.955 232.901 113.976 232.901 113.995C232.901 114.094 232.927 114.163 232.978 114.201C233.032 114.236 233.119 114.254 233.237 114.254H233.549L233.487 114.6H233.112ZM234.996 114.657C234.702 114.657 234.465 114.566 234.286 114.384C234.11 114.201 234.022 113.956 234.022 113.649C234.022 113.409 234.062 113.196 234.142 113.011C234.225 112.825 234.334 112.67 234.468 112.545C234.606 112.417 234.758 112.321 234.924 112.257C235.091 112.193 235.26 112.161 235.433 112.161C235.667 112.161 235.852 112.208 235.99 112.3C236.131 112.39 236.232 112.505 236.292 112.646C236.356 112.784 236.388 112.924 236.388 113.068C236.388 113.132 236.38 113.206 236.364 113.289C236.352 113.369 236.339 113.44 236.326 113.5H234.425C234.422 113.526 234.419 113.552 234.416 113.577C234.416 113.6 234.416 113.622 234.416 113.644C234.416 113.872 234.48 114.041 234.608 114.153C234.736 114.262 234.886 114.316 235.059 114.316C235.225 114.316 235.371 114.278 235.496 114.201C235.624 114.121 235.723 114.017 235.793 113.889H236.192C236.128 114.036 236.036 114.168 235.918 114.283C235.803 114.398 235.667 114.489 235.51 114.556C235.356 114.624 235.185 114.657 234.996 114.657ZM234.488 113.188H235.985C235.988 113.169 235.99 113.152 235.99 113.136C235.993 113.116 235.995 113.099 235.995 113.083C235.995 112.9 235.939 112.758 235.827 112.656C235.715 112.553 235.566 112.502 235.38 112.502C235.188 112.502 235.008 112.561 234.838 112.68C234.668 112.795 234.552 112.964 234.488 113.188ZM236.669 114.6L237.086 112.219H237.456L237.413 112.641C237.515 112.494 237.649 112.377 237.816 112.291C237.985 112.204 238.177 112.161 238.392 112.161L238.32 112.584H238.209C238.017 112.584 237.859 112.616 237.734 112.68C237.609 112.74 237.512 112.827 237.441 112.939C237.371 113.048 237.323 113.174 237.297 113.318L237.072 114.6H236.669ZM239.3 114.6C239.101 114.6 238.948 114.561 238.839 114.484C238.73 114.404 238.676 114.27 238.676 114.081C238.676 114.049 238.677 114.016 238.68 113.98C238.684 113.942 238.688 113.902 238.695 113.86L238.925 112.56H238.556L238.618 112.219H238.988L239.088 111.648H239.496L239.391 112.219H240.096L240.034 112.56H239.333L239.098 113.86C239.095 113.886 239.092 113.91 239.088 113.932C239.088 113.955 239.088 113.976 239.088 113.995C239.088 114.094 239.114 114.163 239.165 114.201C239.22 114.236 239.306 114.254 239.424 114.254H239.736L239.674 114.6H239.3ZM240.985 114.657C240.841 114.657 240.711 114.633 240.596 114.585C240.481 114.537 240.388 114.467 240.318 114.374C240.251 114.278 240.217 114.16 240.217 114.019C240.217 113.824 240.271 113.664 240.38 113.539C240.492 113.411 240.638 113.316 240.817 113.256C240.999 113.195 241.195 113.164 241.403 113.164H242.041C242.047 113.123 242.052 113.088 242.055 113.059C242.059 113.027 242.06 112.998 242.06 112.972C242.06 112.819 242.015 112.702 241.926 112.622C241.836 112.542 241.715 112.502 241.561 112.502C241.414 112.502 241.275 112.539 241.143 112.612C241.012 112.686 240.918 112.803 240.86 112.963H240.443C240.497 112.784 240.583 112.635 240.702 112.516C240.82 112.398 240.958 112.31 241.115 112.252C241.275 112.192 241.441 112.161 241.614 112.161C241.783 112.161 241.932 112.188 242.06 112.243C242.188 112.297 242.287 112.38 242.358 112.492C242.431 112.601 242.468 112.74 242.468 112.91C242.468 112.955 242.467 112.993 242.463 113.025C242.46 113.054 242.455 113.091 242.449 113.136L242.319 113.865C242.313 113.9 242.308 113.932 242.305 113.961C242.302 113.99 242.3 114.017 242.3 114.043C242.3 114.104 242.315 114.153 242.343 114.192C242.375 114.227 242.433 114.244 242.516 114.244L242.454 114.6C242.284 114.6 242.153 114.563 242.06 114.489C241.967 114.416 241.915 114.3 241.902 114.144C241.851 114.227 241.783 114.308 241.7 114.388C241.617 114.468 241.516 114.534 241.398 114.585C241.279 114.633 241.142 114.657 240.985 114.657ZM241.105 114.316C241.265 114.316 241.406 114.275 241.527 114.192C241.652 114.108 241.753 114.003 241.83 113.875C241.91 113.747 241.961 113.614 241.983 113.476H241.379C241.254 113.476 241.135 113.492 241.023 113.524C240.915 113.556 240.825 113.609 240.755 113.683C240.684 113.753 240.649 113.849 240.649 113.971C240.649 114.092 240.692 114.18 240.779 114.235C240.868 114.289 240.977 114.316 241.105 114.316ZM242.965 114.6L243.382 112.219H243.786L243.368 114.6H242.965ZM243.728 111.691C243.648 111.691 243.581 111.665 243.526 111.614C243.475 111.563 243.45 111.497 243.45 111.417C243.45 111.337 243.475 111.272 243.526 111.22C243.581 111.169 243.648 111.144 243.728 111.144C243.805 111.144 243.87 111.169 243.925 111.22C243.979 111.272 244.006 111.337 244.006 111.417C244.006 111.497 243.979 111.563 243.925 111.614C243.87 111.665 243.805 111.691 243.728 111.691ZM244.079 114.6L244.497 112.219H244.862L244.814 112.651C244.916 112.497 245.047 112.377 245.207 112.291C245.367 112.204 245.539 112.161 245.721 112.161C245.958 112.161 246.145 112.228 246.283 112.363C246.42 112.494 246.489 112.683 246.489 112.929C246.489 112.974 246.486 113.02 246.479 113.068C246.476 113.113 246.47 113.16 246.46 113.208L246.215 114.6H245.812L246.047 113.251C246.054 113.209 246.059 113.169 246.062 113.131C246.068 113.092 246.071 113.056 246.071 113.02C246.071 112.678 245.903 112.507 245.567 112.507C245.43 112.507 245.3 112.54 245.179 112.608C245.06 112.675 244.959 112.769 244.876 112.891C244.793 113.012 244.737 113.156 244.708 113.323L244.483 114.6H244.079ZM246.831 114.6L247.249 112.219H247.609L247.575 112.608C247.668 112.47 247.783 112.361 247.921 112.281C248.058 112.201 248.207 112.161 248.367 112.161C248.543 112.161 248.695 112.201 248.823 112.281C248.951 112.361 249.039 112.483 249.087 112.646C249.189 112.496 249.319 112.377 249.476 112.291C249.636 112.204 249.799 112.161 249.965 112.161C250.17 112.161 250.327 112.201 250.436 112.281C250.548 112.361 250.626 112.459 250.671 112.574C250.716 112.689 250.738 112.803 250.738 112.915C250.738 113.004 250.729 113.102 250.709 113.208L250.465 114.6H250.061L250.297 113.251C250.303 113.206 250.308 113.164 250.311 113.126C250.317 113.084 250.321 113.046 250.321 113.011C250.321 112.841 250.281 112.715 250.201 112.632C250.121 112.548 250.01 112.507 249.869 112.507C249.751 112.507 249.636 112.539 249.524 112.603C249.415 112.667 249.321 112.756 249.241 112.872C249.164 112.987 249.111 113.123 249.082 113.28L248.847 114.6H248.449L248.684 113.251C248.69 113.209 248.695 113.169 248.698 113.131C248.701 113.092 248.703 113.057 248.703 113.025C248.703 112.852 248.663 112.723 248.583 112.636C248.506 112.55 248.396 112.507 248.252 112.507C248.137 112.507 248.023 112.54 247.911 112.608C247.799 112.675 247.701 112.772 247.618 112.9C247.538 113.025 247.482 113.18 247.45 113.366L247.234 114.6H246.831ZM252.134 114.657C251.84 114.657 251.603 114.566 251.424 114.384C251.248 114.201 251.16 113.956 251.16 113.649C251.16 113.409 251.2 113.196 251.28 113.011C251.363 112.825 251.472 112.67 251.606 112.545C251.744 112.417 251.896 112.321 252.062 112.257C252.228 112.193 252.398 112.161 252.571 112.161C252.804 112.161 252.99 112.208 253.128 112.3C253.268 112.39 253.369 112.505 253.43 112.646C253.494 112.784 253.526 112.924 253.526 113.068C253.526 113.132 253.518 113.206 253.502 113.289C253.489 113.369 253.476 113.44 253.464 113.5H251.563C251.56 113.526 251.556 113.552 251.553 113.577C251.553 113.6 251.553 113.622 251.553 113.644C251.553 113.872 251.617 114.041 251.745 114.153C251.873 114.262 252.024 114.316 252.196 114.316C252.363 114.316 252.508 114.278 252.633 114.201C252.761 114.121 252.86 114.017 252.931 113.889H253.329C253.265 114.036 253.174 114.168 253.056 114.283C252.94 114.398 252.804 114.489 252.648 114.556C252.494 114.624 252.323 114.657 252.134 114.657ZM251.625 113.188H253.123C253.126 113.169 253.128 113.152 253.128 113.136C253.131 113.116 253.132 113.099 253.132 113.083C253.132 112.9 253.076 112.758 252.964 112.656C252.852 112.553 252.704 112.502 252.518 112.502C252.326 112.502 252.145 112.561 251.976 112.68C251.806 112.795 251.689 112.964 251.625 113.188ZM253.806 114.6L254.224 112.219H254.588L254.54 112.651C254.643 112.497 254.774 112.377 254.934 112.291C255.094 112.204 255.265 112.161 255.448 112.161C255.684 112.161 255.872 112.228 256.009 112.363C256.147 112.494 256.216 112.683 256.216 112.929C256.216 112.974 256.212 113.02 256.206 113.068C256.203 113.113 256.196 113.16 256.187 113.208L255.942 114.6H255.539L255.774 113.251C255.78 113.209 255.785 113.169 255.788 113.131C255.795 113.092 255.798 113.056 255.798 113.02C255.798 112.678 255.63 112.507 255.294 112.507C255.156 112.507 255.027 112.54 254.905 112.608C254.787 112.675 254.686 112.769 254.603 112.891C254.52 113.012 254.464 113.156 254.435 113.323L254.209 114.6H253.806ZM257.417 114.6C257.218 114.6 257.065 114.561 256.956 114.484C256.847 114.404 256.793 114.27 256.793 114.081C256.793 114.049 256.794 114.016 256.798 113.98C256.801 113.942 256.806 113.902 256.812 113.86L257.042 112.56H256.673L256.735 112.219H257.105L257.206 111.648H257.614L257.508 112.219H258.214L258.151 112.56H257.45L257.215 113.86C257.212 113.886 257.209 113.91 257.206 113.932C257.206 113.955 257.206 113.976 257.206 113.995C257.206 114.094 257.231 114.163 257.282 114.201C257.337 114.236 257.423 114.254 257.542 114.254H257.854L257.791 114.6H257.417Z"
                          fill="#535862"
                        />
                        <g clip-path="url(#clip0_14963_117703)">
                          <path
                            d="M262.199 106.372C262.199 106.016 262.63 105.838 262.882 106.09L265.31 108.518C265.466 108.674 265.466 108.927 265.31 109.084L262.882 111.512C262.63 111.764 262.199 111.586 262.199 111.229L262.199 106.372Z"
                            fill="white"
                          />
                        </g>
                      </g>
                      <defs>
                        <filter
                          id="filter0_ddd_14963_117703"
                          x="4.57759"
                          y="-133.738"
                          width="452.993"
                          height="435.542"
                          filterUnits="userSpaceOnUse"
                          color-interpolation-filters="sRGB"
                        >
                          <feFlood
                            flood-opacity="0"
                            result="BackgroundImageFix"
                          />
                          <feColorMatrix
                            in="SourceAlpha"
                            type="matrix"
                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                            result="hardAlpha"
                          />
                          <feOffset dy="33.7472" />
                          <feGaussianBlur stdDeviation="16.8736" />
                          <feColorMatrix
                            type="matrix"
                            values="0 0 0 0 0.65098 0 0 0 0 0.65098 0 0 0 0 0.65098 0 0 0 0.04 0"
                          />
                          <feBlend
                            mode="normal"
                            in2="BackgroundImageFix"
                            result="effect1_dropShadow_14963_117703"
                          />
                          <feColorMatrix
                            in="SourceAlpha"
                            type="matrix"
                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                            result="hardAlpha"
                          />
                          <feOffset dy="67.4944" />
                          <feGaussianBlur stdDeviation="42.184" />
                          <feColorMatrix
                            type="matrix"
                            values="0 0 0 0 0.65098 0 0 0 0 0.65098 0 0 0 0 0.65098 0 0 0 0.1 0"
                          />
                          <feBlend
                            mode="normal"
                            in2="effect1_dropShadow_14963_117703"
                            result="effect2_dropShadow_14963_117703"
                          />
                          <feColorMatrix
                            in="SourceAlpha"
                            type="matrix"
                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                            result="hardAlpha"
                          />
                          <feOffset dy="39.6845" />
                          <feGaussianBlur stdDeviation="99.2112" />
                          <feComposite in2="hardAlpha" operator="out" />
                          <feColorMatrix
                            type="matrix"
                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"
                          />
                          <feBlend
                            mode="normal"
                            in2="effect2_dropShadow_14963_117703"
                            result="effect3_dropShadow_14963_117703"
                          />
                          <feBlend
                            mode="normal"
                            in="SourceGraphic"
                            in2="effect3_dropShadow_14963_117703"
                            result="shape"
                          />
                        </filter>
                        <filter
                          id="filter1_ddd_14963_117703"
                          x="-17.188"
                          y="-5.26918"
                          width="454.079"
                          height="435.388"
                          filterUnits="userSpaceOnUse"
                          color-interpolation-filters="sRGB"
                        >
                          <feFlood
                            flood-opacity="0"
                            result="BackgroundImageFix"
                          />
                          <feColorMatrix
                            in="SourceAlpha"
                            type="matrix"
                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                            result="hardAlpha"
                          />
                          <feOffset dy="33.7472" />
                          <feGaussianBlur stdDeviation="16.8736" />
                          <feColorMatrix
                            type="matrix"
                            values="0 0 0 0 0.65098 0 0 0 0 0.65098 0 0 0 0 0.65098 0 0 0 0.04 0"
                          />
                          <feBlend
                            mode="normal"
                            in2="BackgroundImageFix"
                            result="effect1_dropShadow_14963_117703"
                          />
                          <feColorMatrix
                            in="SourceAlpha"
                            type="matrix"
                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                            result="hardAlpha"
                          />
                          <feOffset dy="67.4944" />
                          <feGaussianBlur stdDeviation="42.184" />
                          <feColorMatrix
                            type="matrix"
                            values="0 0 0 0 0.65098 0 0 0 0 0.65098 0 0 0 0 0.65098 0 0 0 0.1 0"
                          />
                          <feBlend
                            mode="normal"
                            in2="effect1_dropShadow_14963_117703"
                            result="effect2_dropShadow_14963_117703"
                          />
                          <feColorMatrix
                            in="SourceAlpha"
                            type="matrix"
                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                            result="hardAlpha"
                          />
                          <feOffset dy="39.6845" />
                          <feGaussianBlur stdDeviation="99.2112" />
                          <feComposite in2="hardAlpha" operator="out" />
                          <feColorMatrix
                            type="matrix"
                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"
                          />
                          <feBlend
                            mode="normal"
                            in2="effect2_dropShadow_14963_117703"
                            result="effect3_dropShadow_14963_117703"
                          />
                          <feBlend
                            mode="normal"
                            in="SourceGraphic"
                            in2="effect3_dropShadow_14963_117703"
                            result="shape"
                          />
                        </filter>
                        <filter
                          id="filter2_ddd_14963_117703"
                          x="17.5776"
                          y="-63.5387"
                          width="446.446"
                          height="424.044"
                          filterUnits="userSpaceOnUse"
                          color-interpolation-filters="sRGB"
                        >
                          <feFlood
                            flood-opacity="0"
                            result="BackgroundImageFix"
                          />
                          <feColorMatrix
                            in="SourceAlpha"
                            type="matrix"
                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                            result="hardAlpha"
                          />
                          <feOffset dy="33.7472" />
                          <feGaussianBlur stdDeviation="16.8736" />
                          <feColorMatrix
                            type="matrix"
                            values="0 0 0 0 0.65098 0 0 0 0 0.65098 0 0 0 0 0.65098 0 0 0 0.04 0"
                          />
                          <feBlend
                            mode="normal"
                            in2="BackgroundImageFix"
                            result="effect1_dropShadow_14963_117703"
                          />
                          <feColorMatrix
                            in="SourceAlpha"
                            type="matrix"
                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                            result="hardAlpha"
                          />
                          <feOffset dy="67.4944" />
                          <feGaussianBlur stdDeviation="42.184" />
                          <feColorMatrix
                            type="matrix"
                            values="0 0 0 0 0.65098 0 0 0 0 0.65098 0 0 0 0 0.65098 0 0 0 0.1 0"
                          />
                          <feBlend
                            mode="normal"
                            in2="effect1_dropShadow_14963_117703"
                            result="effect2_dropShadow_14963_117703"
                          />
                          <feColorMatrix
                            in="SourceAlpha"
                            type="matrix"
                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                            result="hardAlpha"
                          />
                          <feOffset dy="39.6845" />
                          <feGaussianBlur stdDeviation="99.2112" />
                          <feComposite in2="hardAlpha" operator="out" />
                          <feColorMatrix
                            type="matrix"
                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"
                          />
                          <feBlend
                            mode="normal"
                            in2="effect2_dropShadow_14963_117703"
                            result="effect3_dropShadow_14963_117703"
                          />
                          <feBlend
                            mode="normal"
                            in="SourceGraphic"
                            in2="effect3_dropShadow_14963_117703"
                            result="shape"
                          />
                        </filter>
                        <clipPath id="clip0_14963_117703">
                          <rect
                            width="6.4"
                            height="2.4"
                            fill="white"
                            transform="matrix(0 -1 1 0 263.203 112)"
                          />
                        </clipPath>
                      </defs>
                    </svg>
                  </div>
                  {/* Coming Soon Watermark */}
                </div>
              </div>
              <div
                // onClick={() => navigate('/create-gift-registry')}
                onClick={() => setIsGiftRegistryModalOpen(true)}
                className="md:max-w-[270px] cursor-pointer  mt-6 md:mt-0 flex flex-col justify-between  w-full relative rounded-[20px] bg-[linear-gradient(180deg,_#1A22BF_13.41%,_#000059_100%)]"
              >
                <svg
                  className="absolute top-0 left-0"
                  width="270"
                  height="416"
                  viewBox="0 0 270 416"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M748.803 440.025L733.792 496.044C658.166 475.78 525.096 431.385 440.784 354.156C394.229 363.83 345.192 365.834 295.887 359.628C209.349 348.715 126.577 313.794 59.3879 260.3C-3.97944 284.416 -73.1904 287.018 -127.582 264.439L-105.298 210.902C-73.0572 224.278 -31.192 224.888 10.0261 214.718C-76.413 121.776 -62.1361 50.8165 -57.2044 26.1869C-41.7622 -50.8644 22.9675 -96.4965 96.5998 -82.2372C150.429 -71.7499 209.27 -32.1671 219.325 32.6363C229.828 100.495 200.335 167.244 138.416 215.831C131.573 221.202 124.469 226.303 117.137 231.01C199.721 288.296 301.11 312.994 395.905 302.303C367.345 258.759 354.891 207.389 370.114 147.091C370.314 146.344 370.531 145.535 370.731 144.788C386.425 86.2168 434.149 40.5643 490.131 30.8144C552.777 19.848 616.606 53.7625 641.974 111.461C670.985 177.475 644.875 254.002 578.492 297.59C556.3 312.192 532.538 324.437 507.629 334.241C511.366 336.977 515.182 339.667 519.122 342.39C594.814 394.027 695.522 425.748 748.865 440.041L748.803 440.025ZM458.611 289.618C489.7 280.403 519.391 266.944 546.53 249.065C582.235 225.61 609.148 181.186 588.703 134.746C573.949 101.173 536.594 81.4903 499.925 87.8761C465.906 93.8376 435.464 124.04 426.163 161.242C414.473 207.61 425.061 250.008 458.549 289.602L458.611 289.618ZM69.0873 191.984C80.9166 185.68 92.1369 178.413 102.528 170.256C128.965 149.521 171.789 105.224 161.949 41.6124C156.206 4.78295 118.308 -18.8478 85.4653 -25.2463C36.7617 -34.6939 7.33248 -0.550679 -0.375836 37.6782C-10.5439 88.3235 11.5133 137.463 69.0707 192.046L69.0873 191.984Z"
                    fill="white"
                    fill-opacity="0.06"
                  />
                </svg>

                <div className="pt-8 pl-4">
                  <h3 className="text-xs text-primary-200 mb-1 tracking-[0.12em]">
                    GIFT REGISTRY
                  </h3>
                  <p className="text-2xl font-medium text-primary-900">
                    Curate gifts for <br /> your event and share with friends 🎉
                  </p>
                </div>
                <img src={gift} alt="gift" />
              </div>
            </div>
            {/* <div className="bg-white mt-5 px-6 pt-6  rounded-2xl shadow-[0px_12px_120px_0px_#5F5F5F0F] flex justify-between md:flex-row flex-col">
              <div>
                <h3 className="mb-2 text-cus-orange-100 text-xs tracking-[0.12em]">
                  EVENT WEBSITES
                </h3>
                <p className="text-2xl font-semibold text-cus-orange-200 mb-5">
                  Create a Website <br /> particular to your event
                </p>
                <div className="border border-pink-400 w-fit p-0.5 rounded-full mb-9">
                  <Button
                    variant="primary"
                    size="sm"
                    className="bg-[linear-gradient(184.41deg,_#FFFCFB_3.55%,_#FDF2ED_96.42%)] text-grey-500"
                    iconLeft={
                      <Crown size="12" color="#967F75" variant="Bulk" />
                    }>
                    Coming Soon
                  </Button>
                </div>
              </div>
              <img src={box} alt="box" />
            </div> */}
          </div>
          <Footer />
        </div>
      </div>
      {isGuestListModalOpen && (
        <GuestListPopup
          onClose={() => setIsGuestListModalOpen(false)}
          navigate={navigate}
        />
      )}
      {isGiftRegistryModalOpen && (
        <GiftRegistryPopup
          onClose={() => setIsGiftRegistryModalOpen(false)}
          navigate={navigate}
        />
      )}
      {isBudgetPlannerModalOpen && (
        <BudgetPlannerPopup
          onClose={() => setIsBudgetPlannerModalOpen(false)}
          navigate={navigate}
        />
      )}
    </div>
  );
};
