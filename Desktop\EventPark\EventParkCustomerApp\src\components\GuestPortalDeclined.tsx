import { ArrowRight2 } from "iconsax-react";
import logo from "../assets/icons/Ep-Logo.svg";
import icon from "../assets/images/close-circle.png";
import { useEffect } from "react";

interface GuestPortalDeclinedProps {
  onClose?: () => void;
}

export const GuestPortalDeclined = ({ onClose }: GuestPortalDeclinedProps) => {
  useEffect(() => {
    document.body.style.position = "fixed";
    document.body.style.width = "100%";
    document.body.style.overflow = "hidden";

    return () => {
      document.body.style.position = "";
      document.body.style.width = "";
      document.body.style.overflow = "";
    };
  }, []);

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)]">
      <img src={logo} alt="logo" className="py-7 mx-auto " />

      <div className="min-h-screen flex items-center justify-center py-12 px-4">
        <div className="relative w-full max-w-[450px]">
          <div className="relative  bg-white border-t border-white rounded-[20px] text-center w-full mx-auto shadow-[0px_12px_120px_0px_#5F5F5F0F]">
            <div
              className="bg-[linear-gradient(180deg,_#FDEFE9_0%,_#FEF7F4_100%)] relative rounded-t-[20px] h-[262px] w-full overflow-hidden"
              style={{
                clipPath:
                  "polygon(0 0, 100% 0, 100% 100%, 70% 95%, 30% 95%, 0 100%)",
              }}
            >
              {" "}
              <div className="flex justify-center items-center  h-full">
                <img src={icon} alt="cancel" />
              </div>
            </div>

            <div className="flex flex-col items-center text-center py-12 px-4 w-full">
              <h2 className="text-base md:text-4xl font-medium my-2 text-dark-blue-100 md:text-nowrap">
                You just declined <br />{" "}
                <span className="text-base md:text-[32px] text-grey-250">
                  your invitation
                </span>
              </h2>
              <p className="text-grey-250 text-sm md:text-base mt-4 mb-7.5">
                We'll miss you at the event! Stay connected <br />
                for updates and future celebrations
              </p>

              <button
                type="button"
                onClick={() => {
                  window.location.href = `${window.location.origin}/login`;
                }}
                className="bg-primary cursor-pointer text-base  text-white flex items-center py-3 px-6 font-semibold rounded-full gap-2 hover:bg-[#4A48E0] transition-colors"
              >
                <span>Create your own event</span> 
                <div className="rounded-full bg-white p-0.5">
                  <ArrowRight2 size="12" color="#4D55F2" />
                </div>{" "}
              </button>
            </div>
          </div>
          <div className="flex justify-center mt-10">
            <button
              type="button"
              onClick={onClose}
              className="text-white text-base font-medium underline hover:no-underline transition-all"
            >
              Back to Event Details
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
