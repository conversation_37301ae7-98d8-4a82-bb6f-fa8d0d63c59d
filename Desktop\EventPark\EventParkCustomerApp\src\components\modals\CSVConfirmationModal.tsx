import { useEffect } from "react";
import flag from "../../assets/images/flag.png";
import { CloseCircle } from "iconsax-react";
interface CSVConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onEnable: () => void;
  onContinue: () => void;
}

export const CSVConfirmationModal: React.FC<CSVConfirmationModalProps> = ({
  isOpen,
  onClose,
  onEnable,
  onContinue,
}) => {
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="absolute inset-0 bg-black/20 bg-opacity-50"
        onClick={onClose}
      />

      <div className="relative bg-white mx-3 md:mx-0 rounded-[20px] max-w-[782px] w-full shadow-2xl">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 transition-colors"
          aria-label="Close modal"
        >
          <CloseCircle size="40" color="#634C42" variant="Bulk" />
        </button>
        <div className="flex flex-col lg:flex-row ">
          <div className="bg-gradient-to-b from-[#FDEFE9] to-[#FEF7F4] rounded-t-[20px] lg:rounded-t-[0px] max-h-[200px] sm:max-h-auto lg:rounded-l-[20px] flex justify-center items-center lg:max-w-[256px] w-full">
            <img src={flag} alt="flag" />
          </div>

          <div className="py-8 md:ml-8 px-2 md:px-0 text-center lg:text-start lg:max-w-[430px]">
            <h2 className="lg:text-[32px] text-2xl font-medium mb-6">
              Please confirm how you would like to continue
            </h2>
            <p className="text-grey-250 text-base lg:text-lg leading-relaxed mb-10 lg:mb-11">
              We found some errors in the document you upload, would you like to
              override these errors and{" "}
              <span className="text-dark-400 font-medium">
                proceed with records found
              </span>{" "}
              or{" "}
              <span className="text-dark-400 font-medium">
                Upload a new document
              </span>
              ?
            </p>
            <div className="flex justify-center lg:justify-between gap-3">
              <button
                onClick={onEnable}
                className=" bg-primary hover:bg-primary/50 text-base text-nowrap text-white font-medium py-3 px-4 rounded-full transition-colors"
              >
                Upload new document
              </button>
              <button
                onClick={onContinue}
                className=" bg-primary-250 hover:bg-primary-250/50 text-base text-nowrap text-primary-650 font-medium py-3 px-4 rounded-full transition-colors"
              >
                Proceed with records found{" "}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
