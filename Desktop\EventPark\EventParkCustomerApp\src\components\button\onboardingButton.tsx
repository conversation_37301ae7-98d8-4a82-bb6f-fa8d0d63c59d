import { twMerge } from 'tailwind-merge';

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  isLoading?: boolean;
  variant: 'primary' | 'secondary' | 'neutral' | 'outline' | 'icon';
  size?: 'sm' | 'md' | 'lg';
  iconLeft?: React.ReactNode;
  iconRight?: React.ReactNode;
  fullWidth?: boolean;
}

export function Button({
  isLoading,
  children,
  className,
  variant = 'primary',
  size = 'md',
  iconLeft,
  iconRight,
  fullWidth = false,
  ...props
}: ButtonProps) {
  return (
    <button
      {...props}
      className={twMerge(
        'flex items-center justify-center rounded-full font-medium cursor-pointer transition-colors duration-200 disabled:opacity-90 disabled:cursor-default',
        size === 'sm' && 'py-2 px-3.5 text-xs md:text-sm font-semibold',
        size === 'md' && 'py-2.5 px-4.5 text-xs md:text-base',
        variant === 'primary' && 'bg-primary-200  text-primary-500',
        variant === 'secondary' && 'border border-cus-pink-200 ',
        variant === 'neutral' &&
          'text-primary-500 bg-primary-150 border-primary-150',
        variant === 'icon' && 'bg-gray-100 ',
        fullWidth ? 'w-full' : 'w-auto',
        className
      )}>
      {isLoading ? (
        <span className="flex items-center justify-center">Loading...</span>
      ) : (
        <span className="flex items-center justify-center">
          {iconLeft && <span className="mr-2">{iconLeft}</span>}
          {children}
          {iconRight && <span className="ml-2">{iconRight}</span>}
        </span>
      )}
    </button>
  );
}
