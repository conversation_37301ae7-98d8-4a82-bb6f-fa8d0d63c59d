interface AvatarProps {
  initials: string;
  color?: string;
  offset?: boolean;
}

export const Avatar = ({
  initials,
  color = 'bg-light-blue',
  offset = false,
}: AvatarProps) => {
  return (
    <div
      className={`${color} text-dark-blue-400 font-inter text-base w-10 h-10 rounded-full flex items-center justify-center font-medium border-2 border-white ${
        offset ? '-ml-3' : ''
      }`}>
      {initials}
    </div>
  );
};
