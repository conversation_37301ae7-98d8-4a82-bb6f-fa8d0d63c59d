import {
  AddCircle,
  ArrowCircleLeft2,
  ArrowCircleRight2,
  ArrowRight,
  CloseCircle,
  // Moneys,
} from 'iconsax-react';
import { useState, useEffect } from 'react';
// import { TextInput } from "../../../components/inputs/text-input/text-input";
import { Button } from '../../../components/button/onboardingButton';
import avatar from '../../../assets/images/cash-avatar.png';
import stackMoney from '../../../assets/images/stack-money1.svg';
import { CrowdGiftingModal } from '../../../components/modals/CrowdGiftingModal';
import { SingleItemWarningModal } from '../../../components/modals/SingleItemWarningModal';
import { useCashGift, CashGift } from '../../../lib/contexts/CashGiftContext';

interface CashGiftData {
  amount?: string;
  description?: string;
  giftTypes?: string[];
  proceedToPreview?: boolean;
  cashGifts?: CashGift[];
}

interface AddCashGiftProps {
  onNextStep: (data: CashGiftData) => void;
  initialData?: CashGiftData;
}

export const AddCashGift = ({
  onNextStep,
  initialData = {},
}: AddCashGiftProps) => {
  const { cashGifts, addCashGift, removeCashGift, setCashGifts } =
    useCashGift();
  const [amount, setAmount] = useState(initialData.amount || '');
  const [description, setDescription] = useState(initialData.description || '');
  const [enableCrowdGifting, setEnableCrowdGifting] = useState(false);
  const [showCrowdGiftingModal, setShowCrowdGiftingModal] = useState(false);
  const [showSingleItemWarningModal, setShowSingleItemWarningModal] =
    useState(false);
  const [preview, setPreview] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [showTooltip, setShowTooltip] = useState(false);
  const [itemsPerPage, setItemsPerPage] = useState(
    window.innerWidth < 640 ? 1 : 3
  );

  const formatNumberWithCommas = (num: string | number): string => {
    const numStr = typeof num === 'number' ? num.toString() : num;
    const cleanNum = numStr.replace(/[^\d]/g, '');
    return cleanNum.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  useEffect(() => {
    if (initialData.cashGifts && initialData.cashGifts.length > 0) {
      setCashGifts(initialData.cashGifts);
    }
  }, [initialData.cashGifts, setCashGifts]);

  useEffect(() => {
    const handleResize = () => {
      setItemsPerPage(window.innerWidth < 640 ? 1 : 3);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  const hasValidFormData = () => {
    return amount.trim() !== '' && description.trim() !== '';
  };
  const addCurrentFormToQueue = () => {
    if (!hasValidFormData()) return false;

    const newGift = {
      amount: amount.replace(/[^\d]/g, ''),
      description: description.trim(),
      is_crowd_gift: enableCrowdGifting,
    };
    addCashGift(newGift);
    setAmount('');
    setDescription('');
    setEnableCrowdGifting(false);

    return true;
  };

  const handleAddToQueue = () => {
    if (!amount || !description) return;

    const numericAmount = parseFloat(amount.replace(/,/g, ''));
    if (numericAmount >= 200000 && !enableCrowdGifting) {
      setShowCrowdGiftingModal(true);
      return;
    }
    addToQueue();
  };

  const addToQueue = () => {
    const newGift = {
      amount: amount.replace(/[^\d]/g, ''),
      description,
      is_crowd_gift: enableCrowdGifting,
    };
    addCashGift(newGift);
    setAmount('');
    setDescription('');
    setEnableCrowdGifting(false);
  };

  const handleContinue = () => {
    const hasFormData = hasValidFormData();
    const hasQueueItems = cashGifts.length > 0;

    // If user has form data but no queued items, or only one queued item, show warning
    if ((hasFormData && !hasQueueItems) || cashGifts.length === 1) {
      setShowSingleItemWarningModal(true);
      return;
    }

    // If user has queued items and form data, add current form to queue and proceed
    if (hasQueueItems && hasFormData) {
      addCurrentFormToQueue();
      setTimeout(() => {
        proceedToNextStep();
      }, 100);
      return;
    }

    // If user has queued items but no form data, proceed normally
    if (hasQueueItems && !hasFormData) {
      proceedToNextStep();
      return;
    }
  };

  const proceedToNextStep = () => {
    setTimeout(() => {
      onNextStep({
        cashGifts: cashGifts,
        proceedToPreview: true,
      });
    }, 50);
  };

  const handleNextPage = () => {
    if ((currentPage + 1) * itemsPerPage < cashGifts.length) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  const handlePrevPage = () => {
    if (currentPage > 0) {
      setCurrentPage((prev) => prev - 1);
    }
  };

  const handleRemoveCashGift = (id: number) => {
    removeCashGift(id);
    const updatedLength = cashGifts.length - 1;
    const totalPages = Math.ceil(updatedLength / itemsPerPage);
    if (currentPage >= totalPages && totalPages > 0) {
      setCurrentPage(totalPages - 1);
    } else if (updatedLength === 0) {
      setCurrentPage(0);
    }
  };

  const handleModalClose = () => {
    setShowCrowdGiftingModal(false);
  };

  const handleModalEnable = () => {
    const newGift = {
      amount: amount.replace(/[^\d]/g, ''),
      description,
      is_crowd_gift: true,
    };
    addCashGift(newGift);
    setAmount('');
    setDescription('');
    setEnableCrowdGifting(false);
    setShowCrowdGiftingModal(false);
  };

  const handleModalContinue = () => {
    addToQueue();
    setShowCrowdGiftingModal(false);
  };

  const handleSingleItemModalClose = () => {
    setShowSingleItemWarningModal(false);
  };

  const handleSingleItemAddMore = () => {
    // If user has valid form data, add it to queue
    if (hasValidFormData()) {
      const numericAmount = parseFloat(amount.replace(/,/g, ''));
      if (numericAmount >= 200000 && !enableCrowdGifting) {
        setShowCrowdGiftingModal(true);
        setShowSingleItemWarningModal(false);
        return;
      }
      addCurrentFormToQueue();
    }
    setShowSingleItemWarningModal(false);
  };

  const handleSingleItemContinueAnyway = () => {
    setShowSingleItemWarningModal(false);

    // If user has valid form data but no queued items, add the current item first
    if (hasValidFormData() && cashGifts.length === 0) {
      const numericAmount = parseFloat(amount.replace(/,/g, ''));
      if (numericAmount >= 200000 && !enableCrowdGifting) {
        setShowCrowdGiftingModal(true);
        return;
      }
      addCurrentFormToQueue();
      setTimeout(() => {
        proceedToNextStep();
      }, 100);
    } else {
      // User already has items in queue, proceed normally
      proceedToNextStep();
    }
  };

  return (
    <>
      <div className="px-4 md:px-0 md:pl-3.5 pb-40">
        <div className="max-w-[560px] w-full mx-auto mt-[43px]">
          {!preview ? (
            <>
              {' '}
              <div className="mb-6 ">
                <h2 className="text-sm text-grey-500 font-medium mb-4">
                  Cash gift Amount
                </h2>
                <div className="flex items-center relative">
                  <div className="flex-1 relative ">
                    <span className="absolute left-4 top-[53%] -translate-y-1/2 text-gray-500">
                      ₦
                    </span>
                    <input
                      type="text"
                      inputMode="numeric"
                      pattern="[0-9]*"
                      value={amount}
                      onChange={(e) => {
                        const value = e.target.value;
                        const numericValue = value.replace(/[^\d]/g, '');
                        if (/^\d*$/.test(numericValue)) {
                          const formattedValue = numericValue.replace(
                            /\B(?=(\d{3})+(?!\d))/g,
                            ','
                          );
                          setAmount(formattedValue);
                        }
                      }}
                      placeholder="Enter Amount"
                      className="w-full h-12 pl-8 pr-4 border border-r-0 border-gray-300 rounded-l-full text-base font-normal outline-0"
                    />

                    {/* <input
                      type="number"
                      min="0"
                      value={amount}
                      onChange={(e) => setAmount(e.target.value)}
                      placeholder="Enter Amount"
                      className="w-full h-12 pl-8 pr-4  border border-r-0 border-gray-300 rounded-l-full text-base font-normal outline-0"
                    /> */}
                  </div>
                  <div className="border border-l-0 border-gray-300 rounded-r-full px-4 h-12 flex items-center bg-white">
                    <span className="text-grey-50 mr-1 italic">NGN</span>
                    <svg
                      width="12"
                      height="12"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M19 9l-7 7-7-7"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={handleContinue}
                    disabled={cashGifts.length === 0 && !hasValidFormData()}
                    className={`ml-4 text-base py-3 text-white px-6 rounded-full flex items-center gap-2 absolute -right-[0px] md:top-[unset] top-[-60px] md:-right-[150px] transition-all ${
                      cashGifts.length === 0 && !hasValidFormData()
                        ? 'bg-primary-650/90  cursor-not-allowed'
                        : 'bg-primary-650  hover:bg-primary-700'
                    }`}
                    iconRight={
                      <div
                        className={`rounded-full p-0.5 ${
                          cashGifts.length === 0 ? 'bg-white/40' : 'bg-white/40'
                        }`}>
                        <ArrowRight size="12" color="#ffffff" />
                      </div>
                    }>
                    <span>Continue</span>
                  </Button>
                </div>
              </div>
              <div className="mb-6">
                <h2 className="text-sm text-grey-500  font-medium mb-2">
                  Reason For Cashgift
                </h2>
                <input
                  type="text"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="E.g Trip to Zanzibaar"
                  className="w-full h-12 px-4 border border-gray-300 rounded-full text-base font-normal outline-0"
                />
              </div>
              <Button
                variant="primary"
                size="md"
                className="bg-primary-250 h-7 text-primary !text-sm"
                iconLeft={
                  <AddCircle size="18" color="#4D55F2" variant="Bulk" />
                }
                onClick={handleAddToQueue}>
                Add to Queue
              </Button>
              <div className="mb-8 mt-6 relative">
                <p
                  onMouseEnter={() => setShowTooltip(true)}
                  onMouseLeave={() => setShowTooltip(false)}
                  className="text-xs cursor-pointer text-primary-960 mb-2 underline underline-offset-2 font-medium w-fit">
                  Do you want to enable crowd gifting
                </p>
                {showTooltip && (
                  <div className="flex absolute left-0 top-[22px] z-50  bg-[#9499F7] text-white p-3 rounded-lg shadow-[0px_12px_120px_0px_rgba(95,95,95,0.06)]">
                    <p className="text-sm leading-[1.667] tracking-[-0.03em] max-w-[322px]">
                      Crowd gifting allows your guests to be able to contribute
                      as a group towards this particular gift item
                    </p>
                    <CloseCircle
                      size="20"
                      color="white"
                      variant="Bulk"
                      className="cursor-pointer"
                      onClick={() => setShowTooltip(false)}
                    />
                  </div>
                )}
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => setEnableCrowdGifting(!enableCrowdGifting)}
                    className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                      enableCrowdGifting ? 'bg-primary-650' : 'bg-grey-850'
                    }`}>
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-grey-350 shadow-[0px_1px_2px_0px_#0A0D120F,_0px_1px_3px_0px_#0A0D121A] transition-transform ${
                        enableCrowdGifting ? 'translate-x-4' : 'translate-x-1'
                      }`}
                    />
                  </button>
                  <span className="text-sm text-grey-500 font-medium">
                    Enable Crowd gifting
                  </span>
                </div>
              </div>
              {cashGifts.length > 0 && (
                <>
                  {' '}
                  <div className="flex items-center justify-center lg:ml-20 gap-2 md:gap-9 ">
                    <div
                      className={`p-2 rounded-full transition-all ${
                        currentPage > 0
                          ? 'bg-primary-750 cursor-pointer hover:bg-primary-750/90'
                          : 'bg-gray-200 cursor-not-allowed opacity-50'
                      }`}
                      onClick={currentPage > 0 ? handlePrevPage : undefined}>
                      <ArrowCircleLeft2
                        color={currentPage > 0 ? '#fff' : '#9CA3AF'}
                        size={20}
                        variant="Bulk"
                      />
                    </div>
                    <div className="flex justify-center items-center gap-4">
                      {cashGifts
                        .slice(
                          currentPage * itemsPerPage,
                          (currentPage + 1) * itemsPerPage
                        )
                        .map((cashGift) => (
                          <div
                            key={cashGift.id}
                            className="bg-white rounded-2xl px-3 pt-3 pb-6 h-[164px] w-[173px] overflow-hidden shadow-[0px_12px_120px_0px_#5F5F5F0F] border border-[#F0F0F0]">
                            <div className="flex justify-between mb-6">
                              <img src={stackMoney} alt="" />

                              <CloseCircle
                                color="#8288F6"
                                size={24}
                                variant="Bulk"
                                className="cursor-pointer hover:opacity-70 transition-opacity"
                                onClick={() =>
                                  handleRemoveCashGift(cashGift.id)
                                }
                              />
                            </div>
                            <p className="text-2xl font-medium">
                              ₦{formatNumberWithCommas(cashGift.amount)}
                            </p>
                            <p className="text-gray-500 text-xs mt-1 ">
                              {cashGift.description}
                            </p>
                            {/* {cashGift.is_crowd_gift && (
                              <div className="flex items-center gap-1 mt-1">
                                <TickCircle
                                  size="12"
                                  variant="Bulk"
                                  color="#3CC35C"
                                />
                                <span className="text-[10px] font-medium text-[#007AFF]">
                                  Crowd gifting
                                </span>
                              </div>
                            )} */}
                          </div>
                        ))}
                    </div>
                    <div
                      className={`p-2 rounded-full transition-all ${
                        (currentPage + 1) * itemsPerPage < cashGifts.length
                          ? 'bg-primary-750 cursor-pointer hover:bg-primary-750/90'
                          : 'bg-gray-200 cursor-not-allowed opacity-50'
                      }`}
                      onClick={
                        (currentPage + 1) * itemsPerPage < cashGifts.length
                          ? handleNextPage
                          : undefined
                      }>
                      <ArrowCircleRight2
                        color={
                          (currentPage + 1) * itemsPerPage < cashGifts.length
                            ? '#fff'
                            : '#9CA3AF'
                        }
                        size={20}
                        variant="Bulk"
                      />
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={() => setPreview(true)}
                    className={`text-sm font-semibold border py-2 px-3.5 rounded-full mt-5 transition-colors ${
                      cashGifts.length === 0
                        ? 'text-gray-400 bg-gray-100 border-gray-300 cursor-not-allowed'
                        : 'text-primary bg-white border-primary-950'
                    }`}>
                    View All
                  </button>
                </>
              )}
            </>
          ) : (
            <div>
              <div className="flex items-center mb-4">
                <button
                  onClick={() => setPreview(false)}
                  className="flex items-center cursor-pointer gap-2 italic text-primary-750 font-medium">
                  <ArrowCircleLeft2 color="#5F66F3" variant="Bulk" size={20} />
                  Back
                </button>
              </div>

              <div className="space-y-4">
                {cashGifts.length > 0 ? (
                  cashGifts.map((item) => (
                    <div
                      key={item.id}
                      className="flex items-center rounded-2xl p-2.5 bg-white shadow-[0px_12px_120px_0px_#5F5F5F0F] border border-grey-150">
                      <img
                        src={avatar}
                        alt="cash-gift"
                        className="object-contain w-10 h-10 rounded-full mr-4"
                      />
                      <div className="flex items-start justify-between w-full">
                        <div>
                          <h3 className="font-semibold text-sm text-dark-blue-200">
                            ₦{formatNumberWithCommas(item.amount)}
                          </h3>
                          <p className="text-grey-650 text-xs">
                            {item.description}
                          </p>
                          {/* {item.is_crowd_gift && (
                            <div className="flex items-center gap-1 mt-1">
                              <TickCircle
                                size="12"
                                variant="Bulk"
                                color="#3CC35C"
                              />
                              <span className="text-[10px] font-medium text-[#007AFF]">
                                Crowd gifting enabled
                              </span>
                            </div>
                          )} */}
                        </div>
                        <button
                          onClick={() => handleRemoveCashGift(item.id)}
                          className="hover:opacity-70 transition-opacity">
                          <CloseCircle
                            color="#9499F7"
                            variant="Bulk"
                            size={20}
                          />
                        </button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="flex flex-col items-center justify-center py-8 px-4">
                    <img
                      src={avatar}
                      alt="No cash gifts"
                      className="w-16 h-16 mb-4 opacity-50"
                    />
                    <h3 className="text-lg font-medium text-gray-600 mb-2">
                      No Cash Gifts Added
                    </h3>
                    <p className="text-gray-500 text-center text-sm">
                      Add some cash gifts to see them here.
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Crowd Gifting Modal */}
      <CrowdGiftingModal
        isOpen={showCrowdGiftingModal}
        onClose={handleModalClose}
        onEnable={handleModalEnable}
        onContinue={handleModalContinue}
      />

      {/* Single Item Warning Modal */}
      <SingleItemWarningModal
        isOpen={showSingleItemWarningModal}
        onClose={handleSingleItemModalClose}
        onAddMore={handleSingleItemAddMore}
        onContinueAnyway={handleSingleItemContinueAnyway}
      />
    </>
  );
};
