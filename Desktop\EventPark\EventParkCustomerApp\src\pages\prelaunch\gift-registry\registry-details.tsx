/* eslint-disable @typescript-eslint/no-explicit-any */
import { ArrowRight } from 'iconsax-react';
import giftItem from '../../../assets/images/gift-item.png';
import giftCash from '../../../assets/images/cash-gift.png';
// import giftImport from "../../../assets/images/gift-imports.png";
import { useState } from 'react';
import { events } from '../../../lib/services/events';
import { useEventStore } from '../../../lib/store/event';
import { useEventManagement } from '../../../lib/hooks/useEventManagement';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'react-toastify';

interface RegistryDetailsProps {
  onNextStep: (data: { registryTitle: string; giftTypes: string[] }) => void;
  initialData?: { registryTitle?: string; giftTypes?: string[] };
}

export const RegistryDetails = ({
  onNextStep,
  initialData = {},
}: RegistryDetailsProps) => {
  const [registryTitle, setRegistryTitle] = useState(
    initialData.registryTitle || ''
  );
  const [selectedGiftTypes, setSelectedGiftTypes] = useState<string[]>(
    initialData.giftTypes || []
  );
  const { selectedEvent } = useEventStore();
  const { updateEventOptimistically } = useEventManagement();

  const updateEventMutation = useMutation({
    mutationFn: ({
      eventId,
      payload,
    }: {
      eventId: string;
      payload: { gift_registry_title: string };
    }) => events.updateEventDetails(eventId, payload),
    onSuccess: (response) => {
      // Update the selected event optimistically with the latest data
      const updatedEvent = response.data;
      updateEventOptimistically(updatedEvent);

      // toast.success('Gift registry title saved successfully!');
      const titleToUse =
        updatedEvent?.gift_registry_title || registryTitle.trim();
      onNextStep({
        registryTitle: titleToUse,
        giftTypes: selectedGiftTypes,
      });
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || 'Failed to save gift registry title'
      );
    },
  });

  const handleGiftTypeSelect = (type: string) => {
    if (selectedGiftTypes.includes(type)) {
      setSelectedGiftTypes(selectedGiftTypes.filter((t) => t !== type));
    } else {
      setSelectedGiftTypes([...selectedGiftTypes, type]);
    }
  };

  const isGiftTypeSelected = (type: string) => selectedGiftTypes.includes(type);

  const isFormValid =
    (selectedEvent?.gift_registry_title || registryTitle.trim() !== '') &&
    selectedGiftTypes.length > 0;

  const handleContinue = () => {
    // if (!isFormValid) {
    //   toast.error(
    //     'Please fill in the registry title and select at least one gift type'
    //   );
    //   return;
    // }

    if (!selectedEvent?.id) {
      toast.error('No event selected. Please select an event first.');
      return;
    }
    const titleToUse =
      selectedEvent.gift_registry_title || registryTitle.trim();

    updateEventMutation.mutate({
      eventId: selectedEvent.id,
      payload: {
        gift_registry_title: titleToUse,
      },
    });
  };

  return (
    <div className="px-4 md:px-0 md:ml-3.5">
      <div className="max-w-[560px] w-full mx-auto mt-[43px]">
        {!selectedEvent?.gift_registry_title && (
          <div className="mb-6 border-b border-gray-200 pb-6">
            <label className="block text-grey-500 font-medium text-sm mb-2">
              Title your gift registry
            </label>
            <input
              type="text"
              value={registryTitle}
              onChange={(e) => setRegistryTitle(e.target.value)}
              className="w-full h-[44px] px-3.5 border border-gray-300 rounded-full text-base font-bold text-grey-50 placeholder:font-normal placeholder:text-grey-700 italic outline-0"
              placeholder="Oladele’s birthday gifts"
            />
          </div>
        )}

        <div className="">
          <h3 className="text-grey-120 font-bold text-sm tracking-[0.16em]">
            SELECT GIFT TYPE FOR THIS REGISTRY
          </h3>
        </div>
        <div className="mt-6 md:flex gap-4 mb-10">
          <div className="space-y-4 w-full">
            <div
              className={`flex md:w-full h-[130px] pr-4 ${
                isGiftTypeSelected('items')
                  ? 'bg-linear-to-r border-[2px] border-[#A6AAF9]  from-[#FEF7F4] to-[#F5F6FE]'
                  : 'bg-[#FBF9FA]'
              } rounded-2xl cursor-pointer transition-all duration-200`}
              onClick={() => handleGiftTypeSelect('items')}>
              <img
                src={giftItem}
                alt="Gift items"
                className="rounded-l-2xl h-full"
              />
              <div className="py-4 w-full">
                <div className="flex justify-end">
                  <div
                    className={`w-5.5 h-5.5 rounded-full ${
                      isGiftTypeSelected('items')
                        ? 'bg-primary-650 flex items-center justify-center'
                        : 'border border-gray-300'
                    }`}>
                    {isGiftTypeSelected('items') && (
                      <svg
                        width="12"
                        height="12"
                        viewBox="0 0 12 12"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M4.5 8.1L2.4 6L1.5 6.9L4.5 9.9L10.5 3.9L9.6 3L4.5 8.1Z"
                          fill="white"
                        />
                      </svg>
                    )}
                  </div>
                </div>
                <h4
                  className={`text-2xl font-medium ${
                    isGiftTypeSelected('items') ? 'text-dark-blue' : 'text-grey'
                  }`}>
                  Gift Items
                </h4>
                <p
                  className={`text-sm  mt-1.5 ${
                    isGiftTypeSelected('items')
                      ? 'text-dark-blue-100'
                      : 'text-grey-100'
                  }`}>
                  Accept gift items for this registry
                </p>
              </div>
            </div>

            {/* Cash Gift Card */}
            <div
              className={`flex md:w-full h-[130px] pr-4 ${
                isGiftTypeSelected('cash')
                  ? 'bg-linear-to-r border-[2px] border-[#A6AAF9]  from-[#FEF7F4] to-[#F5F6FE]'
                  : 'bg-[#FBF9FA] '
              } rounded-2xl cursor-pointer transition-all duration-200 `}
              onClick={() => handleGiftTypeSelect('cash')}>
              <img
                src={giftCash}
                alt="Cash gift"
                className="rounded-l-2xl h-full w-[236px]"
              />
              <div className="py-4 w-full">
                <div className="flex justify-end">
                  <div
                    className={`w-5.5 h-5.5 rounded-full ${
                      isGiftTypeSelected('cash')
                        ? 'bg-primary-650 flex items-center justify-center'
                        : 'border border-gray-300'
                    }`}>
                    {isGiftTypeSelected('cash') && (
                      <svg
                        width="12"
                        height="12"
                        viewBox="0 0 12 12"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M4.5 8.1L2.4 6L1.5 6.9L4.5 9.9L10.5 3.9L9.6 3L4.5 8.1Z"
                          fill="white"
                        />
                      </svg>
                    )}
                  </div>
                </div>
                <h4
                  className={`text-2xl font-medium ${
                    isGiftTypeSelected('cash') ? 'text-dark-blue' : 'text-grey'
                  }`}>
                  Cash gift
                </h4>
                <p
                  className={`text-sm  mt-1.5 ${
                    isGiftTypeSelected('cash')
                      ? 'text-dark-blue-100'
                      : 'text-grey-100'
                  }`}>
                  Accept cash gifts for this registry
                </p>
              </div>
            </div>
          </div>

          {/* <div
            className={`border border-gray-200 mt-4 md:mt-0 font-bold rounded-xl p-4 bg-gradient-to-b from-[#FEF7F4] via-[#FEF7F4] to-[#F5F6FE] ${
              selectedGiftTypes.length > 0 ? "blur-sm" : ""
            }`}
          >
            <h4 className="text-xl">Import from your favourite Stores</h4>
            <button
              type="button"
              className="my-4 italic bg-primary-650 cursor-pointer text-white py-2 px-3.5 rounded-full text-sm"
            >
              Import Gift Items
            </button>
            <img src={giftImport} alt="gift import" />
          </div> */}
        </div>
        <button
          onClick={handleContinue}
          disabled={!isFormValid || updateEventMutation.isPending}
          className={`py-2.5 px-4 rounded-full mb-24 cursor-pointer flex items-center gap-2 ${
            isFormValid && !updateEventMutation.isPending
              ? 'bg-primary-650 text-white'
              : 'bg-primary-650/35 text-white cursor-not-allowed'
          }`}>
          {updateEventMutation.isPending ? 'Saving...' : 'Continue'}
          <div className="bg-white/30 rounded-full p-0.5">
            <ArrowRight size="12" color="#fff" />
          </div>
        </button>
      </div>
    </div>
  );
};
