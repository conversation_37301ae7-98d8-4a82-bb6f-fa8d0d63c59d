/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect, useCallback } from 'react';
import { events, AddressAutocompleteResponse } from '../services/events';

interface UseAddressAutocompleteReturn {
  suggestions: AddressAutocompleteResponse[];
  isLoading: boolean;
  error: string | null;
  searchAddress: (query: string) => void;
  clearSuggestions: () => void;
}

export const useAddressAutocomplete = (
  debounceMs: number = 1000
): UseAddressAutocompleteReturn => {
  const [suggestions, setSuggestions] = useState<AddressAutocompleteResponse[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  const clearSuggestions = useCallback(() => {
    setSuggestions([]);
    setError(null);
  }, []);

  const searchAddress = useCallback(
    (query: string) => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
      if (!query.trim()) {
        clearSuggestions();
        setIsLoading(false);
        return;
      }
      setIsLoading(true);
      setError(null);
      const timer = setTimeout(async () => {
        try {
          const response = await events.autocompleteAnAddress(query.trim());
          setSuggestions(response.data || []);
          setError(null);
        } catch (err: any) {
          setSuggestions([]);
          setError(
            err?.response?.data?.message || 
            'Failed to fetch address suggestions. Please try again.'
          );
        } finally {
          setIsLoading(false);
        }
      }, debounceMs);

      setDebounceTimer(timer);
    },
    [debounceMs, debounceTimer, clearSuggestions]
  );
  useEffect(() => {
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [debounceTimer]);

  return {
    suggestions,
    isLoading,
    error,
    searchAddress,
    clearSuggestions,
  };
};
