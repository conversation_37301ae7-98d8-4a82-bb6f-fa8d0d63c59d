import React, { InputHTMLAttributes } from 'react';
import { Label } from '../label/label';
import { twMerge } from 'tailwind-merge';

interface Props extends InputHTMLAttributes<HTMLInputElement> {
  id: string;
  label: string;
  error?: string;
}

export const TextInput = React.forwardRef<HTMLInputElement, Props>(
  ({ label, error, className, id, ...props }, ref) => {
    return (
      <div className="font-rethink">
        <Label
          id={`label-${id}`}
          htmlFor={id}
          className="mb-1.5 text-grey-500 font-medium text-sm block">
          {label}
        </Label>
        <input
          id={id}
          ref={ref}
          className={twMerge(
            'flex p-2.5 pl-3.5 border-grey-200 rounded-full border w-full focus:outline-none disabled:opacity-90 disabled:cursor-not-allowed  placeholder:text-grey-300',
            className
          )}
          {...props}
        />
        {error && <p className="text-red-500 mt-1 text-sm">{error}</p>}
      </div>
    );
  }
);
