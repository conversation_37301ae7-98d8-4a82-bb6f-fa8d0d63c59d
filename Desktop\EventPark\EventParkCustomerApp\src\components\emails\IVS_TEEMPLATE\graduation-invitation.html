<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Graduation Invitation</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600&family=Quintessential:wght@400&family=Ruthie:wght@400&display=swap"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Playfair Display", serif;
        background-color: #f5f5f5;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
      }

      .invitation-container {
        position: relative;
        width: 1240px;
        height: 1748px;
        background-image: url("https://customer-preprod.eventpark.africa/assets/email-templates/graduation-invitation-background.png");
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        max-width: 100%;
        max-height: 100vh;
        object-fit: contain;
      }

      .text-element {
        position: absolute;
        color: #ffeb9a;
      }

      .invitation-text {
        font-family: "Playfair Display", serif;
        font-weight: 400;
        font-size: 46px;
        line-height: 1em;
        letter-spacing: 5%;
        text-transform: uppercase;
        text-align: left;
        left: 388px;
        top: 625px;
        width: 465px;
        height: 46px;
      }

      .graduation-title {
        font-family: "Quintessential", cursive;
        font-weight: 400;
        font-size: 120px;
        line-height: 1em;
        text-transform: uppercase;
        text-align: center;
        left: 224px;
        top: 791px;
        width: 793px;
        height: 240px;
        transform-origin: center center;
      }

      .class-year {
        font-family: "Ruthie", cursive;
        font-weight: 400;
        font-size: 132px;
        line-height: 1em;
        text-align: center;
        left: 320.1171875px;
        top: 1029.5127631425858px;
        width: 599.7657879292965px;
        height: 172.9742888212204px;
        transform-origin: center center;
      }

      .event-date {
        font-family: "Playfair Display", serif;
        font-weight: 600;
        font-size: 52px;
        line-height: 1em;
        letter-spacing: 2%;
        text-transform: uppercase;
        text-align: left;
        left: 278px;
        top: 1322px;
        width: 685px;
        height: 52px;
      }

      .venue-address {
        font-family: "Playfair Display", serif;
        font-weight: 500;
        font-size: 40px;
        line-height: 1em;
        letter-spacing: 2%;
        text-transform: uppercase;
        text-align: left;
        left: 338px;
        top: 1410px;
        width: 565px;
        height: 40px;
      }

      /* Dynamic scaling for graduation title */
      .graduation-title[data-length="8"] {
        transform: scale(0.95);
      }
      .graduation-title[data-length="9"] {
        transform: scale(0.9);
      }
      .graduation-title[data-length="10"] {
        transform: scale(0.85);
      }
      .graduation-title[data-length="11"] {
        transform: scale(0.8);
      }
      .graduation-title[data-length="12"] {
        transform: scale(0.75);
      }
      .graduation-title[data-length="13"] {
        transform: scale(0.7);
      }
      .graduation-title[data-length="14"] {
        transform: scale(0.65);
      }
      .graduation-title[data-length="15"] {
        transform: scale(0.6);
      }
      .graduation-title[data-length="16"] {
        transform: scale(0.55);
      }
      .graduation-title[data-length="17"] {
        transform: scale(0.5);
      }
      .graduation-title[data-length="18"],
      .graduation-title[data-length="19"],
      .graduation-title[data-length="20"] {
        transform: scale(0.45);
      }

      /* Dynamic scaling for class year */
      .class-year[data-length="8"] {
        transform: scale(0.95);
      }
      .class-year[data-length="9"] {
        transform: scale(0.9);
      }
      .class-year[data-length="10"] {
        transform: scale(0.85);
      }
      .class-year[data-length="11"] {
        transform: scale(0.8);
      }
      .class-year[data-length="12"] {
        transform: scale(0.75);
      }
      .class-year[data-length="13"] {
        transform: scale(0.7);
      }
      .class-year[data-length="14"] {
        transform: scale(0.65);
      }
      .class-year[data-length="15"] {
        transform: scale(0.6);
      }
      .class-year[data-length="16"] {
        transform: scale(0.55);
      }
      .class-year[data-length="17"] {
        transform: scale(0.5);
      }
      .class-year[data-length="18"],
      .class-year[data-length="19"],
      .class-year[data-length="20"] {
        transform: scale(0.45);
      }

      @media screen and (max-width: 1280px) {
        .invitation-container {
          transform: scale(0.8);
          transform-origin: center center;
        }
      }

      @media screen and (max-width: 1024px) {
        .invitation-container {
          transform: scale(0.6);
          transform-origin: center center;
        }
      }

      @media screen and (max-width: 768px) {
        .invitation-container {
          transform: scale(0.4);
          transform-origin: center center;
        }
      }
    </style>
  </head>
  <body>
    <div class="invitation-container">
      <div class="text-element invitation-text">you're invited to</div>

      <div class="text-element graduation-title">GRADUATION PARTY</div>

      <div class="text-element class-year">Class {{{class_year}}}</div>

      <div class="text-element text-center event-date">{{{event_date}}}</div>

      <div class="text-element text-center venue-address">
        {{{venue_address}}}
      </div>
    </div>

    <script>
      //   function calculateTextLengths() {
      //     const graduationTitle = document.querySelector(".graduation-title");
      //     const classYear = document.querySelector(".class-year");

      //     if (graduationTitle) {
      //       const textContent =
      //         graduationTitle.textContent || graduationTitle.innerText;
      //       const cleanText = textContent.replace(/\s+/g, " ").trim();
      //       const totalLength = cleanText.length;
      //       graduationTitle.setAttribute("data-length", totalLength.toString());
      //       console.log(`Graduation title length: ${totalLength} characters`);
      //     }

      //     if (classYear) {
      //       const textContent = classYear.textContent || classYear.innerText;
      //       const cleanText = textContent.replace(/\s+/g, " ").trim();
      //       const totalLength = cleanText.length;
      //       classYear.setAttribute("data-length", totalLength.toString());
      //       console.log(`Class year length: ${totalLength} characters`);
      //     }
      //   }

      //   // Run the calculation when the page loads
      //   document.addEventListener("DOMContentLoaded", calculateTextLengths);

      //   // Also run it after a short delay to ensure all content is loaded
      //   setTimeout(calculateTextLengths, 100);
      //
      //
    </script>
  </body>
</html>
