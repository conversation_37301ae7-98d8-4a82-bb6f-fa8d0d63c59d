/* eslint-disable @typescript-eslint/no-explicit-any */
import { Link } from 'react-router-dom';
import { Button } from '../../components/button/button';
import { Icon } from '../../components/icons/icon';
import { TextInput } from '../../components/inputs/text-input/text-input';
import { ArrowRight3 } from 'iconsax-react';
import { useMutation } from '@tanstack/react-query';
import { AuthServices } from '../../lib/services/auth';
import { toast } from 'react-toastify';
import { useForm } from 'react-hook-form';
import { getOrCreateKeyPair } from '../../lib/utils/crypto';

type FormData = {
  firstname: string;
  lastname: string;
  email: string;
};

export function StepOne({
  handleNext,
}: {
  handleNext: (data: {
    email: string;
    first_name: string;
    last_name: string;
    expiresAt: string;
  }) => void;
}) {
  const {
    register,
    handleSubmit,
    getValues,
    formState: { errors, isValid, isDirty, touchedFields },
  } = useForm<FormData>({ mode: 'onChange' });

  const mutation = useMutation({
    mutationFn: (data: FormData) =>
      AuthServices.initiateRegistration({
        first_name: data.firstname,
        last_name: data.lastname,
        email: data.email,
      }),
    onSuccess: (data) => {
      const formValues = getValues();
      handleNext({
        email: formValues.email,
        first_name: formValues.firstname,
        last_name: formValues.lastname,
        expiresAt: data?.data?.expires_at,
      });
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || error?.message);
    },
  });
  const googleMutation = useMutation({
    mutationFn: (publicKey: string) =>
      AuthServices.initiateGoogleOAuth(publicKey),
    onSuccess: (data) => {
      window.location.href = data.data.consent_url;
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || error?.message);
    },
  });
  const googleOAuthMutation = async () => {
    const { publicKey } = await getOrCreateKeyPair();
    googleMutation.mutate(publicKey);
  };
  const onSubmit = handleSubmit((data) => {
    mutation.mutate(data);
  });

  return (
    <div>
      <div className="mb-[4vh] ">
        <h1 className="font-semibold text-[32px]">Create Account</h1>
        <h3 className="text-grey-100 mb-8">
          Bringing your event from planning to completion!
        </h3>
        <form onSubmit={onSubmit}>
          <div className="grid grid-cols-2 gap-4 mb-5">
            <TextInput
              label="First name"
              id="firstname"
              placeholder="John"
              {...register('firstname', { required: 'First name is required' })}
              error={
                touchedFields.firstname ? errors.firstname?.message : undefined
              }
            />
            <TextInput
              label="Last name"
              id="lastname"
              placeholder="Dalton"
              {...register('lastname', { required: 'Last name is required' })}
              error={
                touchedFields.lastname ? errors.lastname?.message : undefined
              }
            />
          </div>

          <TextInput
            label="Email"
            id="email"
            placeholder="<EMAIL>"
            {...register('email', {
              required: 'Email is required',
              pattern: {
                value: /^\S+@\S+$/i,
                message: 'Invalid email address',
              },
            })}
            error={touchedFields.email ? errors.email?.message : undefined}
          />
          <Button
            type="submit"
            variant="primary"
            className="mt-7"
            isLoading={mutation.isPending}
            disabled={!isValid || !isDirty || mutation.isPending}>
            Sign up
          </Button>
        </form>
        <div className="relative w-full py-7">
          <hr className="border border-grey-400" />
          <p className="font-medium text-sm tracking-[0.12em] text-grey-100 absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-white">
            OR
          </p>
        </div>
        <Button
          type="button"
          variant="secondary"
          className="text-grey-500 gap-3 mb-4"
          onClick={(e) => {
            e.preventDefault();
            googleOAuthMutation();
          }}
          // isLoading={!googleMutation.isPending}
          disabled={googleMutation.isPending}>
          <Icon name="google" />
          <span>
            {googleMutation.isPending
              ? 'Inititatiing...'
              : 'Sign up with Google'}
          </span>
        </Button>
        {/* <Button variant="secondary" className="text-grey-500 gap-3">
          <Icon name="facebook" />
          <span>Sign up with facebook</span>
        </Button> */}
      </div>
      <div className="flex items-center gap-2">
        <p className="text-grey-500 text-lg">Got an account? </p>
        <Link
          to={'/'}
          className="text-cus-orange font-extrabold text-lg flex items-center ">
          <em>Sign In</em>
          <ArrowRight3 size="24" color="#FF5519" variant="Bulk" />
        </Link>
      </div>
    </div>
  );
}
