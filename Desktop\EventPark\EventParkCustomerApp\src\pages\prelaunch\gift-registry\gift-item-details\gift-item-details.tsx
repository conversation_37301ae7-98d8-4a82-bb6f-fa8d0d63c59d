import { use<PERSON>ara<PERSON>, useNavigate, useLocation } from "react-router-dom";
import { PageTitle } from "../../../../components/helmet/helmet";
import { useEffect } from "react";
import { Tag2, <PERSON>Lef<PERSON>, Box } from "iconsax-react";
import { Footer } from "../../footer";
import { GiftRegistryServices } from "../../../../lib/services/gift-registry";
import { useQuery } from "@tanstack/react-query";
import defaultGiftImg from "../../../../assets/images/gift-img.png";

interface Contributor {
  id: number;
  gifter_first_name: string;
  gifter_last_name: string;
  gifter_email: string;
  status: "pending" | "completed";
}

interface GiftItem {
  id: string;
  event_id: string;
  image_preview_key: string;
  image_preview_url: string;
  name: string;
  description: string;
  quantity: number;
  price: string;
  item_link: string;
  currency_code: string;
  status: string;
  created_at: string;
  updated_at: string;
  total_reservations: number;
  contributors?: Contributor[];
  amountReceived?: number;
}

export const GiftItemDetails = () => {
  const { giftId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { reservationCounts } = location.state || {
    reservationCounts: { pending: 0, completed: 0 },
  };
  const { data, isLoading } = useQuery({
    queryKey: ["singleGiftItem", giftId],
    queryFn: () =>
      giftId
        ? GiftRegistryServices.getSingleGiftItem(giftId)
        : Promise.resolve(null),
    enabled: !!giftId,
  });

  const { data: contributors, isLoading: contributorsLoading } = useQuery({
    queryKey: ["contributorsPerItem", giftId],
    queryFn: () =>
      giftId
        ? GiftRegistryServices.getContributors(giftId)
        : Promise.resolve(null),
    enabled: !!giftId,
  });

  const giftItem: GiftItem | null = data?.data || null;
  const contributorsList = contributors?.data?.reservations || [];

  // console.log('data for item', data?.data);
  // console.log('data for contributorsList', contributorsList);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  if (isLoading || contributorsLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }

  if (!giftItem) {
    return (
      <div className="flex justify-center items-center h-screen">
        Gift item not found
      </div>
    );
  }

  return (
    <div className="bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)]">
      <div className="max-w-[564px] mx-auto px-4 md:px-0 pb-20">
        <PageTitle
          title="Gift Item Details"
          description="View gift item details"
        />
        <div className="pt-8 mb-6">
          <button
            onClick={() => navigate(-1)}
            className="p-2.5 bg-white rounded-full cursor-pointer"
          >
            <div className="bg-[#F5F6FE] w-fit p-0.5">
              <div className="bg-primary p-0.5 rounded-full">
                <ArrowLeft size="16" color="#fff" />
              </div>
            </div>
          </button>
        </div>

        <div className="">
          <div className="flex pl-5 py-5 flex-wrap md:flex-nowrap gap-4 mb-6 shadow-[0px_12px_120px_0px_#5F5F5F0F] bg-white rounded-2xl">
            <div className="w-[155px] h-[184px] bg-[#C4B5E0] rounded-2xl overflow-hidden flex-shrink-0">
              <img
                src={giftItem.image_preview_url || defaultGiftImg}
                alt={giftItem.name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  // Fallback to default image if the image fails to load
                  const target = e.target as HTMLImageElement;
                  target.src = defaultGiftImg;
                }}
              />
            </div>
            <div className="flex-1 flex justify-between flex-col">
              <div>
                <h3 className="text-[22px] font-medium text-grey-750 mb-2">
                  {giftItem.name}
                </h3>
                <p className="text-base text-[#535862] mb-4">
                  {giftItem.description}
                </p>
              </div>
              <div className="flex items-center gap-2 mb-4">
                <div className="flex items-center gap-2 bg-light-blue-150 text-[#B54708] px-2.5 py-1.5 rounded-full">
                  <Tag2 size={12} variant="Bulk" color="#5925DC" />
                  <span className="text-primary text-sm font-semibold">
                    ₦{parseFloat(giftItem.price).toLocaleString()}
                  </span>
                </div>
                {reservationCounts.pending > 0 && (
                  <div className="flex items-center gap-1 font-bold italic bg-orange-100 text-[#B54708] text-sm px-2.5 py-1.5 rounded-full">
                    <Box size={12} variant="Bulk" color="#C4320A" />
                    <span> Reserved • {reservationCounts.pending}</span>
                  </div>
                )}
                {reservationCounts.completed > 0 && (
                  <div className="flex items-center gap-1 font-bold italic bg-green-100 text-green-700 text-sm px-2.5 py-1.5 rounded-full">
                    <Box size={12} variant="Bulk" color="#3CC35C" />
                    <span> Purchased • {reservationCounts.completed}</span>
                  </div>
                )}
              </div>
              {/* <div className="flex items-center gap-2 mb-4">
                <div className="text-sm text-gray-600">
                  Quantity: {giftItem.quantity}
                </div>
                {giftItem.item_link && (
                  <a
                    href={giftItem.item_link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-primary underline">
                    View Item Link
                  </a>
                )}
              </div> */}
              {/* <div className="text-sm text-green-600 mb-2">
                ✓ Crowd gifting enabled
              </div> */}
            </div>
          </div>

          {/* Amount Received */}
          <div className="mb-6 bg-white px-4 py-4 rounded-[16px] shadow-[0px_12px_120px_0px_#5F5F5F0F]">
            <div className="text-[32px] font-bold text-black mb-1 italic">
              ₦{parseFloat(giftItem.price).toLocaleString()}
            </div>
            <div className="text-xs font-medium text-grey-250 uppercase tracking-[0.10em]">
              AMOUNT RECEIVED
            </div>
          </div>

          <div>
            <h4 className="text-lg font-semibold mb-4">Contributors</h4>
            <div className="space-y-3">
              {contributorsList && contributorsList.length > 0 ? (
                contributorsList.map((contributor: Contributor) => (
                  <div
                    key={contributor.id}
                    className="flex items-center justify-between p-3 bg-white rounded-lg shadow-[0px_12px_120px_0px_#5F5F5F0F]"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-[#F5F6FE] rounded-full flex items-center justify-center">
                        <span className="text-primary text-base font-semibold">
                          {contributor.gifter_first_name ||
                          contributor.gifter_last_name
                            ? `${
                                contributor.gifter_first_name?.charAt(0) || ""
                              }${
                                contributor.gifter_last_name?.charAt(0) || ""
                              }`.toUpperCase()
                            : "U"}
                        </span>
                      </div>
                      <div>
                        <div className="text-[#00008C] font-semibold text-sm">
                          {contributor.gifter_first_name &&
                          contributor.gifter_last_name
                            ? `${contributor.gifter_first_name} ${contributor.gifter_last_name}`
                            : "-"}
                        </div>
                        <div className="text-[12px] text-[#535862]">
                          {contributor.gifter_email || "-"}
                        </div>
                      </div>
                    </div>
                    <div
                      className={`text-sm font-medium italic ${
                        contributor?.status === "pending"
                          ? "text-orange-600"
                          : "text-green-600"
                      }`}
                    >
                      {contributor.status || "-"}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500 bg-white rounded-lg shadow-[0px_12px_120px_0px_#5F5F5F0F]">
                  No contributors yet
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};
