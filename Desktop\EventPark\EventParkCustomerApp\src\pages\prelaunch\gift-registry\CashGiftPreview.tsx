import { useState } from "react";
import { ArrowCircleRight2, CloseCircle } from "iconsax-react";
// import phone from "../../../assets/images/phone.png";
import stackMoney from "../../../assets/images/stack-money2.svg";
import { Success } from "./success";
import {
  GiftRegistryServices,
  CreateCashGiftPayload,
} from "../../../lib/services/gift-registry";
import { useEventStore } from "../../../lib/store/event";
import { useCashGift, CashGift } from "../../../lib/contexts/CashGiftContext";

// import { Icon } from "../../../components/Icon";

// interface AccountDetails {
//   accountNumber: string;
//   bank: string;
//   accountName: string;
//   location: string;
// }

interface RegistryData {
  registryTitle?: string;
  giftTypes?: string[];

  cashGifts?: CashGift[];
}

interface CashGiftPreviewProps {
  initialData?: RegistryData;
  onClose?: () => void;
  onBack?: () => void;
  eventId?: string;
}

export const CashGiftPreview = ({
  initialData = {},
  onClose = () => {},
  eventId,
}: CashGiftPreviewProps) => {
  const { cashGifts, removeCashGift } = useCashGift();
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { selectedEvent } = useEventStore();

  // const accountDetails: AccountDetails = {
  //   accountNumber: "**********",
  //   bank: "Guaranty Trust Bank",
  //   accountName: "Oladele Olanrewaju",
  //   location: "Lagos, Nigeria",
  // };

  const handleRemoveCashGift = (id: number) => {
    removeCashGift(id);
  };

  const handleCreateGiftRegistry = async () => {
    const currentEventId = eventId || selectedEvent?.id;

    if (!currentEventId) {
      setError("No event selected. Please select an event first.");
      return;
    }

    if (!cashGifts || cashGifts.length === 0) {
      setError("No cash gifts to create. Please add some cash gifts first.");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const payload: CreateCashGiftPayload = {
        gifts: cashGifts.map((gift) => ({
          amount: gift.amount.replace(/,/g, ""),
          description: gift.description,
          is_crowd_gift: gift.is_crowd_gift,
        })),
        activate_gift_registry: true,
      };

      await GiftRegistryServices.createCashGift(currentEventId, payload);
      setOpen(true);
    } catch (err) {
      console.error("Error creating cash gift registry:", err);
      setError("Failed to create gift registry. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div className="bg-white min-h-screen mb-40 px-4 lg:px-0 font-rethink">
        <div className="max-w-[560px] mx-auto relative">
          <h1 className="text-[28px] font-semibold italic mb-4 mt-5">
            {initialData.registryTitle || "Oladele's birthday gifts"}
          </h1>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
              {error}
            </div>
          )}

          <button
            type="button"
            onClick={handleCreateGiftRegistry}
            disabled={isLoading}
            className="bg-primary text-[12px] md:text-base font-semibold absolute top-[0] md:top-0 right-[0] md:right-[-215px] cursor-pointer text-white rounded-full py-2.5 px-2 md:px-4 flex items-center gap-2 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? "Creating..." : "Create Gift Registry"}
            <ArrowCircleRight2 variant="Bulk" color="#fff" size={20} />
          </button>

          {cashGifts.length > 0 && (
            <div className="flex gap-2 mb-4">
              <span className="text-primary text-sm italic font-bold bg-primary-250 px-3 py-1 rounded-full">
                Cash gift
              </span>
              <span className="text-primary text-sm  italic font-bold bg-primary-250 px-3 py-1 rounded-full">
                {cashGifts.length} {cashGifts.length === 1 ? "Item" : "Items"}
              </span>
            </div>
          )}

          {cashGifts.length > 0 ? (
            <div className="grid grid-cols-2 gap-4">
              {cashGifts.map((cashGift) => (
                <div
                  key={cashGift.id}
                  className="bg-[#F5F9FF] h-[259px] rounded-xl p-4 relative"
                >
                  <button
                    className="absolute top-2 right-2 "
                    onClick={() => handleRemoveCashGift(cashGift.id)}
                  >
                    <CloseCircle color="#365B96" variant="Bulk" size={28} />
                  </button>
                  <img
                    src={stackMoney}
                    alt="Cash gift"
                    className="w-[104px] h-[88px] mt-4 mb-2"
                  />
                  <p className="text-[32px] font-bold">
                    ₦{Number(cashGift.amount).toLocaleString("en-NG")}
                  </p>
                  <p className="text-dark-blue-300 text-base italic mt-3">
                    {cashGift.description}
                  </p>
                  {/* {cashGift.is_crowd_gift && (
                    <div className="flex items-center gap-1 mt-2">
                      <TickCircle size="16" variant="Bulk" color="#3CC35C" />
                      <span className="text-xs font-medium text-[#007AFF]">
                        Crowd gifting enabled
                      </span>
                    </div>
                  )} */}
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 px-4">
              <img
                src={stackMoney}
                alt="No cash gifts"
                className="w-24 h-24 mb-4 opacity-50"
              />
              <h3 className="text-xl font-medium text-gray-600 mb-2">
                No Cash Gifts Added
              </h3>
              <p className="text-gray-500 text-center">
                You haven't added any cash gifts yet. Go back to add some cash
                gifts to your registry.
              </p>
            </div>
          )}
        </div>
      </div>
      {open && <Success onClose={onClose} cashCount={cashGifts?.length} />}
    </>
  );
};
