/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useState } from "react";
import { Navigate, useSearchParams } from "react-router-dom";
import { useMutation } from "@tanstack/react-query";
import { AuthServices } from "../../lib/services/auth";
import { toast } from "react-toastify";
import { useUserAuthStore } from "../../lib/store/auth";
import { useEventStore } from "../../lib/store/event";
import { events } from "../../lib/services/events";
import {
  base64ToBuffer,
  encodeECDSASignatureDER,
} from "../../lib/utils/crypto";

export function OAuthCallback() {
  const [searchParams] = useSearchParams();
  const challenge = searchParams.get("challenge");
  const [redirectPath, setRedirectPath] = useState<string | null>(null);
  const [redirectState, setRedirectState] = useState<any>(null);
  const setUserEvents = useEventStore((state) => state.setUserEvents);
  const { setAuthData } = useUserAuthStore();
  const mutation = useMutation({
    mutationFn: async () => {
      const devicePublicKey = localStorage.getItem("device_public_key");
      const devicePrivateKey = localStorage.getItem("device_private_key");

      if (!devicePublicKey || !devicePrivateKey || !challenge) {
        console.log("Missing parameters:", {
          devicePublicKey,
          devicePrivateKey: devicePrivateKey ? "exists" : "missing",
          challenge,
        });
        throw new Error("Missing required OAuth parameters");
      }

      const privateKey = await window.crypto.subtle.importKey(
        "pkcs8",
        base64ToBuffer(devicePrivateKey),
        { name: "ECDSA", namedCurve: "P-256" },
        true,
        ["sign"]
      );

      const challengeBuffer = base64ToBuffer(challenge);
      const signatureRaw = await window.crypto.subtle.sign(
        { name: "ECDSA", hash: "SHA-256" },
        privateKey,
        challengeBuffer
      );

      const signatureDER = encodeECDSASignatureDER(signatureRaw);
      const signatureBase64 = btoa(String.fromCharCode(...signatureDER));

      return await AuthServices.completeGoogleOAuth({
        device_public_key: devicePublicKey,
        challenge: challenge,
        signature: signatureBase64,
      });
    },
    onSuccess: async (data) => {
      const {
        access_token,
        refresh_token,
        access_token_expires_at,
        refresh_token_expires_at,
        user_data,
      } = data.data;

      setAuthData(
        access_token,
        {
          email: user_data.email,
          first_name: user_data.first_name,
          last_name: user_data.last_name,
          id: user_data.id,
          password_set: user_data.password_set,
          profile_picture: user_data.profile_picture,
          transaction_pin_set: user_data.transaction_pin_set,
        },
        refresh_token,
        access_token_expires_at,
        refresh_token_expires_at
      );

      try {
        const eventsResponse = await events.getEventForAuthUsers();
        const userEvents = eventsResponse?.data?.events || [];
        const eventsMeta = eventsResponse?.data?.meta || null;
        setUserEvents(userEvents, eventsMeta);

        if (userEvents.length > 0) {
          setRedirectPath("/select-event");
          setRedirectState(undefined);
        } else {
          // First-timer: redirect to prelaunch introduction
          setRedirectPath("/prelaunch");
          setRedirectState({ first_name: user_data?.first_name });
        }
      } catch (error) {
        console.error("Failed to fetch user events:", error);
        // On error, also redirect to prelaunch for first-timers
        setRedirectPath("/prelaunch");
        setRedirectState({ first_name: user_data?.first_name });
      }
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || error?.message);
    },
  });

  useEffect(() => {
    if (!challenge) {
      console.error("No challenge parameter found in URL");
      return;
    }
    mutation.mutate();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [challenge]);

  if (mutation.isError) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2 text-red-500">
            SORRY, AN ERROR OCURRED
          </h2>
        </div>
      </div>
    );
  }

  if (mutation.isSuccess) {
    return <Navigate to={redirectPath!} state={redirectState!} />;
  }

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <h2 className="text-xl font-semibold mb-2">
          Completing authentication...
        </h2>
        <p className="text-grey-100">
          Please wait while we complete your sign in.
        </p>
      </div>
    </div>
  );
}
