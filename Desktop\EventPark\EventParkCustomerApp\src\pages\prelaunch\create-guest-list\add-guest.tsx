import { useState, useEffect } from 'react';
import ex1 from '../../../assets/images/ex1.png';
import { SendEmailInvite } from './send-email-invite';
import { AddGuestManually } from './add-guest-manually';
import { UploadGuestList } from './upload-guest-list';
import { AddGuestsViaLink } from './add-guests-via-link';
import { useEventStore } from '../../../lib/store/event';
import { useGuestList } from '../../../lib/contexts/GuestListContext';

interface Guest {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

interface Template {
  id?: string;
  name: string;
  preview_url?: string;
  image?: string;
}

interface AddGuestProps {
  onNextStep?: () => void;
  selectedTemplateImage?: string;
  selectedTemplate?: Template | null;
  onGuestsChange?: (
    guests: Guest[],
    source?: 'manual' | 'upload' | 'email' | 'link'
  ) => void;
}

export const AddGuest = ({
  onNextStep,
  selectedTemplateImage = ex1,
  selectedTemplate,
  onGuestsChange,
}: AddGuestProps) => {
  const { selectedEvent } = useEventStore();
  const { setGuests, guestSource } = useGuestList();

  // Set active tab based on the current guest source from context
  // This ensures users return to the tab they were working on
  const [activeTab, setActiveTab] = useState(guestSource || 'manual');
  const [isFormActive, setIsFormActive] = useState(false);

  // Update active tab when guest source changes
  useEffect(() => {
    if (guestSource) {
      setActiveTab(guestSource);
    }
  }, [guestSource]);

  const templateName = selectedTemplate?.name || 'Invitation Template';

  const invitationImageUrl =
    selectedEvent?.iv_preview_url || selectedTemplateImage;

  const handleTabClick = (tab: 'link' | 'manual' | 'upload' | 'email') => {
    if (!isFormActive) {
      setActiveTab(tab);
    }
  };

  const handleManualGuestsChange = (newGuests: Guest[]) => {
    setGuests(newGuests, 'manual');
    onGuestsChange?.(newGuests, 'manual');
  };

  const handleUploadGuestsChange = (newGuests: Guest[]) => {
    setGuests(newGuests, 'upload');
    onGuestsChange?.(newGuests, 'upload');
  };

  const handleEmailGuestsChange = (newGuests: Guest[]) => {
    setGuests(newGuests, 'email');
    onGuestsChange?.(newGuests, 'email');
  };

  // const handleEmailGuestsChange = (guests: Guest[]) => {
  //   onGuestsChange?.(guests, 'email');
  // };

  // const handleLinkGuestsChange = (guests: Guest[]) => {
  //   onGuestsChange?.(guests, 'link');
  // };

  return (
    <div className="max-w-[740px] w-full mx-auto pt-5 md:pt-12 px-4 md:px-0 pb-62 font-rethink relative">
      <div className="md:ml-27">
        <div className="absolute right-0 md:left-10 top-0 md:top-41 -z-10">
          <div className="w-32 md:w-48 aspect-square rounded-xl overflow-hidden border-6 border-white md:-rotate-3 opacity-50 backdrop-blur-[24px] shadow-[0px_33.75px_33.75px_0px_#A6A6A60A,0px_67.49px_84.37px_0px_#A6A6A61A,0px_39.68px_198.42px_0px_#0000000F]">
            <img
              src={invitationImageUrl}
              alt={templateName}
              className="w-[170px] h-[202px] object-contain"
            />
          </div>
        </div>

        <h2 className="md:text-[40px] text-xl font-medium leading-[114.99999999999999%] mb-7 ">
          Create an Invitation
          <br />
          Card for your Guests
        </h2>

        <div className="bg-white rounded-4xl shadow-[0px_12px_120px_0px_#5F5F5F0F]">
          <div className="flex flex-col md:flex-row  gap-2 md:gap-8 px-1 md:px-5">
            <div className="flex flex-col text-center md:text-start  pt-8 gap-3 text-gray-600  md:max-w-[170px] w-full">
              <div
                className={`text-[10px] md:text-sm ${
                  activeTab === 'manual'
                    ? 'font-bold text-dark-200 bg-primary-150 rounded-full'
                    : 'text-grey-950'
                } py-2.5 pl-4 cursor-pointer transition-all duration-200 ${
                  isFormActive && activeTab !== 'manual'
                    ? 'opacity-50 blur-[1px] pointer-events-none'
                    : ''
                }`}
                onClick={() => handleTabClick('manual')}>
                Add Guest Manually
              </div>
              <div
                className={`text-[10px] md:text-sm ${
                  activeTab === 'email'
                    ? 'font-bold text-dark-200 bg-primary-150 rounded-full'
                    : 'text-grey-950'
                } py-2.5 pl-4 cursor-pointer transition-all duration-200 ${
                  isFormActive && activeTab !== 'email'
                    ? 'opacity-50 blur-[1px] pointer-events-none'
                    : ''
                }`}
                onClick={() => handleTabClick('email')}>
                Add with email
              </div>
              <div
                className={`text-[10px] md:text-sm ${
                  activeTab === 'upload'
                    ? 'font-bold text-dark-200 bg-primary-150 rounded-full'
                    : 'text-grey-950'
                } py-2.5 pl-4 cursor-pointer transition-all duration-200 ${
                  isFormActive && activeTab !== 'upload'
                    ? 'opacity-50 blur-[1px] pointer-events-none'
                    : ''
                }`}
                onClick={() => handleTabClick('upload')}>
                Upload Guest List
              </div>
              {/* <div
                className={`text-[10px] md:text-sm ${
                  activeTab === 'link'
                    ? 'font-bold text-dark-200 bg-primary-150 rounded-full'
                    : 'text-grey-950'
                } py-2.5 pl-4 cursor-pointer transition-all duration-200 ${
                  isFormActive && activeTab !== 'link'
                    ? 'opacity-50 blur-[1px] pointer-events-none'
                    : ''
                }`}
                onClick={() => handleTabClick('link')}>
                Add Guests via Link
              </div> */}
            </div>
            {activeTab === 'manual' && (
              <AddGuestManually
                onNextStep={onNextStep}
                onFormActiveChange={setIsFormActive}
                onGuestsChange={handleManualGuestsChange}
              />
            )}
            {activeTab === 'email' && (
              <SendEmailInvite
                onNextStep={onNextStep}
                onFormActiveChange={setIsFormActive}
                onGuestsChange={handleEmailGuestsChange}
              />
            )}
            {activeTab === 'upload' && (
              <UploadGuestList
                onNextStep={onNextStep}
                onFormActiveChange={setIsFormActive}
                onGuestsChange={handleUploadGuestsChange}
              />
            )}
            {activeTab === 'link' && (
              <AddGuestsViaLink onFormActiveChange={setIsFormActive} />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
