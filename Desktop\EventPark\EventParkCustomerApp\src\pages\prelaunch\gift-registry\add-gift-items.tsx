import {
  Add,
  AddCircle,
  ArrowCircleLeft2,
  ArrowCircleRight2,
  ArrowRight,
  <PERSON>us,
} from "iconsax-react";
import { useState, useRef, useEffect } from "react";
import img from "../../../assets/images/gift-img.png";
import { TextInput } from "../../../components/inputs/text-input/text-input";
import { Button } from "../../../components/button/onboardingButton";
import { PreviewGiftItems } from "./preview-gift-items";
import { ItemAddedModal, SuggestionModal } from "./item-added-modal";
import { SingleItemWarningModal } from "../../../components/modals/SingleItemWarningModal";
import { FollowupModal } from "../../../components/modals/followupModal";
import {
  useGiftItems,
  GiftItem as ContextGiftItem,
} from "../../../lib/contexts/GiftItemsContext";
import {
  GiftRegistryServices,
  ExternalLinkParseResponse,
} from "../../../lib/services/gift-registry";

interface GiftItemData {
  giftName?: string;
  price?: string;
  quantity?: number;
  description?: string;
  item_link?: string;
  addToQueue?: boolean;
  giftItems?: ContextGiftItem[];
  giftTypes?: string[];
}

interface AddGiftItemsProps {
  onNextStep: (data: GiftItemData) => void;
  initialData?: GiftItemData;
}

interface GiftItem {
  id: number;
  image: string;
  name: string;
  price?: string;
  quantity?: number;
  description?: string;
  item_link?: string;
}

export const AddGiftItems = ({
  initialData = {},
  onNextStep,
}: AddGiftItemsProps) => {
  const {
    giftItems,
    addGiftItem,
    removeGiftItem,
    // setMostWanted: setItemMostWanted,
    hasMostWantedItem,
    // getMostWantedItem
  } = useGiftItems();
  const [giftName, setGiftName] = useState(initialData.giftName || "");
  const [price, setPrice] = useState(initialData.price || "");
  const [quantity, setQuantity] = useState(initialData.quantity || 1);
  const [description, setDescription] = useState(initialData.description || "");
  const [link, setLink] = useState(initialData.item_link || "");

  // Validation states
  const [errors, setErrors] = useState({
    giftName: "",
    price: "",
    quantity: "",
    link: "",
    image: "",
  });
  const [touched, setTouched] = useState({
    giftName: false,
    price: false,
    quantity: false,
    link: false,
    image: false,
  });

  const [currentPage, setCurrentPage] = useState(0);
  const [selectedItem, setSelectedItem] = useState<GiftItem | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isBlurring, setIsBlurring] = useState(false);
  const [localMostWanted, setLocalMostWanted] = useState(false);
  const [preview, setPreview] = useState(false);
  const [itemsPerPage, setItemsPerPage] = useState(
    window.innerWidth < 640 ? 1 : 5
  );
  const [showItemAddedModal, setShowItemAddedModal] = useState(false);
  const [showSuggestionModal, setShowSuggestionModal] = useState(false);
  const [lastAddedItem, setLastAddedItem] = useState<GiftItem | null>(null);
  const [showSingleItemWarningModal, setShowSingleItemWarningModal] =
    useState(false);
  const [showFollowupModal, setShowFollowupModal] = useState(false);

  // Link parsing states
  const [isParsingLink, setIsParsingLink] = useState(false);
  const [linkParseData, setLinkParseData] =
    useState<ExternalLinkParseResponse | null>(null);
  const [linkParseError, setLinkParseError] = useState<string>("");

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  useEffect(() => {
    const handleResize = () => {
      setItemsPerPage(window.innerWidth < 640 ? 1 : 5);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const validateField = (fieldName: string, value: string | number) => {
    let error = "";

    switch (fieldName) {
      case "giftName":
        if (!value || (typeof value === "string" && value.trim() === "")) {
          error = "Gift name is required";
        }
        break;
      case "price":
        if (!value || (typeof value === "string" && value.trim() === "")) {
          error = "Price is required";
        } else if (typeof value === "string") {
          const numericPrice = parseFloat(value.replace(/,/g, ""));
          if (isNaN(numericPrice) || numericPrice <= 0) {
            error = "Please enter a valid price greater than 0";
          }
        }
        break;
      case "quantity":
        if (typeof value === "number" && value <= 0) {
          error = "Quantity must be greater than 0";
        }
        break;
      case "link":
        if (!value || (typeof value === "string" && value.trim() === "")) {
          error = "Link to item is required";
        }
        break;
      case "image":
        // Image is now optional, so no validation needed
        break;
    }

    return error;
  };

  const validateAllFields = () => {
    const newErrors = {
      giftName: validateField("giftName", giftName),
      price: validateField("price", price),
      quantity: validateField("quantity", quantity),
      link: validateField("link", link),
      image: "", // Image is optional, so no validation needed
    };

    setErrors(newErrors);
    return !Object.values(newErrors).some((error) => error !== "");
  };

  const isFormValid = () => {
    return (
      giftName.trim() !== "" &&
      price.trim() !== "" &&
      !isNaN(parseFloat(price.replace(/,/g, ""))) &&
      parseFloat(price.replace(/,/g, "")) > 0 &&
      quantity > 0 &&
      link.trim() !== ""
    );
  };

  const handleContinue = () => {
    setIsBlurring(true);
    setPreview(true);
  };

  const handleBack = () => {
    setIsBlurring(false);
    setPreview(false);
  };

  const handleNextStep = () => {
    const hasFormData = isFormValid();
    const hasQueuedItems = giftItems.length > 0;

    if ((hasFormData && !hasQueuedItems) || giftItems.length === 1) {
      setShowSingleItemWarningModal(true);
      return;
    }

    if (onNextStep) {
      onNextStep({
        giftItems: giftItems,
      });
    }
  };
  const handleItemSelect = (item: GiftItem) => {
    setSelectedItem(item);
    setGiftName(item.name || "");
    setPrice(item.price || "");
    setQuantity(item.quantity || 1);
    setDescription(item.description || "");
    setLink(item.item_link || "");
  };

  const handleAddToQueue = () => {
    if (!validateAllFields()) {
      setTouched({
        giftName: true,
        price: true,
        quantity: true,
        link: true,
        image: true,
      });
      return;
    }

    const newItem: ContextGiftItem = {
      id: Date.now(),
      name: giftName.trim(),
      price: price.trim(),
      quantity,
      description: description.trim(),
      item_link: link.trim(),
      image: selectedItem?.image || img, // Use default image if no image selected
      mostWanted: localMostWanted,
    };
    addGiftItem(newItem);
    setLastAddedItem(newItem as GiftItem);
    setShowSingleItemWarningModal(false);
    setShowFollowupModal(false);
    setGiftName("");
    setPrice("");
    setQuantity(1);
    setDescription("");
    setLink("");
    setSelectedItem(null);
    setLocalMostWanted(false);
    setLocalMostWanted(false);
    // Clear link parse data and errors
    setLinkParseData(null);
    setLinkParseError("");
    setErrors({
      giftName: "",
      price: "",
      quantity: "",
      link: "",
      image: "",
    });
    setTouched({
      giftName: false,
      price: false,
      quantity: false,
      link: false,
      image: false,
    });
  };
  const handleProceedToCashGift = () => {
    setShowItemAddedModal(false);
    if (onNextStep) {
      onNextStep({
        giftItems: giftItems,
        addToQueue: true,
      });
    }
  };

  const handleContinueAddingItems = () => {
    setShowItemAddedModal(false);
    setShowSuggestionModal(false);
  };

  const handleSingleItemWarningClose = () => {
    setShowSingleItemWarningModal(false);
  };

  const handleSingleItemWarningAddMore = () => {
    if (isFormValid()) {
      handleAddToQueue();
    }
    setShowSingleItemWarningModal(false);
  };

  const handleSingleItemWarningContinue = () => {
    setShowSingleItemWarningModal(false);

    if (isFormValid() && giftItems.length === 0) {
      const newItem: ContextGiftItem = {
        id: Date.now(),
        name: giftName.trim(),
        price: price.trim(),
        quantity,
        description: description.trim(),
        item_link: link.trim(),
        image: selectedItem?.image || img, // Use default image if no image selected
        mostWanted: localMostWanted,
      };
      addGiftItem(newItem);

      const hasBothGiftAndCash =
        initialData.giftTypes?.includes("items") &&
        initialData.giftTypes?.includes("cash");

      if (hasBothGiftAndCash) {
        setShowFollowupModal(true);
      } else if (onNextStep) {
        onNextStep({
          giftItems: [newItem],
        });
      }
    } else {
      const hasBothGiftAndCash =
        initialData.giftTypes?.includes("items") &&
        initialData.giftTypes?.includes("cash");

      if (hasBothGiftAndCash) {
        setShowFollowupModal(true);
      } else if (onNextStep) {
        onNextStep({
          giftItems: giftItems,
        });
      }
    }
  };

  const handleFollowupClose = () => {
    setShowFollowupModal(false);
  };

  const handleFollowupProceedToCashGift = () => {
    setShowFollowupModal(false);
    if (onNextStep) {
      onNextStep({
        giftItems: giftItems,
        addToQueue: true,
      });
    }
  };

  const handleFollowupContinueAddingItems = () => {
    setShowFollowupModal(false);
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const imageUrl = URL.createObjectURL(file);
      const newItem: GiftItem = {
        id: Date.now(),
        image: imageUrl,
        name: "",
      };
      setSelectedItem(newItem);
      if (touched.image) {
        setErrors((prev) => ({
          ...prev,
          image: "",
        }));
      }
    }
  };

  const handleImageClick = () => {
    setTouched((prev) => ({ ...prev, image: true }));
    fileInputRef.current?.click();
  };

  const handleNextPage = () => {
    if ((currentPage + 1) * itemsPerPage < giftItems.length) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  const handlePrevPage = () => {
    if (currentPage > 0) {
      setCurrentPage((prev) => prev - 1);
    }
  };

  const incrementQuantity = () => {
    const newQuantity = quantity + 1;
    setQuantity(newQuantity);
    if (touched.quantity) {
      setErrors((prev) => ({
        ...prev,
        quantity: validateField("quantity", newQuantity),
      }));
    }
  };

  const decrementQuantity = () => {
    const newQuantity = Math.max(0, quantity - 1);
    setQuantity(newQuantity);
    if (touched.quantity) {
      setErrors((prev) => ({
        ...prev,
        quantity: validateField("quantity", newQuantity),
      }));
    }
  };

  const handleFieldBlur = (fieldName: string, value: string | number) => {
    setTouched((prev) => ({ ...prev, [fieldName]: true }));
    setErrors((prev) => ({
      ...prev,
      [fieldName]: validateField(fieldName, value),
    }));
  };

  const handleGiftNameChange = (value: string) => {
    setGiftName(value);
    // Clear link parse data if user manually changes the gift name
    if (linkParseData && value !== linkParseData.og_title) {
      setLinkParseData(null);
    }
    if (touched.giftName) {
      setErrors((prev) => ({
        ...prev,
        giftName: validateField("giftName", value),
      }));
    }
  };

  const formatNumberWithCommas = (value: string): string => {
    const numericValue = value.replace(/\D/g, "");
    return numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  const handlePriceChange = (value: string) => {
    const formattedValue = formatNumberWithCommas(value);
    setPrice(formattedValue);

    if (touched.price) {
      setErrors((prev) => ({
        ...prev,
        price: validateField("price", formattedValue),
      }));
    }
  };

  const handleLinkChange = (value: string) => {
    setLink(value);
    // Clear parse data and errors when link changes
    setLinkParseData(null);
    setLinkParseError("");
    if (touched.link) {
      setErrors((prev) => ({
        ...prev,
        link: validateField("link", value),
      }));
    }
  };

  const isValidUrl = (string: string): boolean => {
    try {
      new URL(string);
      return true;
    } catch {
      return false;
    }
  };

  const parseExternalLink = async (url: string) => {
    if (!isValidUrl(url)) {
      return;
    }

    setIsParsingLink(true);
    setLinkParseError("");
    try {
      const response = await GiftRegistryServices.parseExternalLink({
        link: url,
      });

      setLinkParseData(response);

      // Only auto-fill the gift name, not the image
      if (response.og_title) {
        setGiftName(response.og_title);
      }
    } catch (error) {
      console.error("Failed to parse external link:", error);
      setLinkParseError(
        "Failed to parse link. Please try again or fill in the details manually."
      );
      setLinkParseData(null);
    } finally {
      setIsParsingLink(false);
    }
  };
  return (
    <div className="px-4 md:px-0 md:pl-3.5 overflow-hidden pb-14 relative">
      {isParsingLink && (
        <div className="fixed inset-0  backdrop-blur-[2px] z-20 flex items-center justify-center">
          <div className="bg-white rounded-2xl px-5 py-4 shadow-2xl  w-fit mx-4">
            <div className="flex flex-col items-center gap-4">
              <div className="animate-spin rounded-full h-17 w-17 border-4 border-gray-200 border-t-primary-650/60"></div>
            </div>
          </div>
        </div>
      )}
      <div
        className={`flex  flex-col md:flex-row items-center md:items-start gap-5 mb-9 mt-11 transition-all duration-500 ${
          isBlurring ? " justify-between w-full" : "justify-center "
        }`}
      >
        <div className={`${isBlurring ? "blur -translate-x-28" : ""}`}>
          <div
            className={`relative cursor-pointer ${
              isBlurring ? "blur -translate-x-28 " : ""
            }`}
            onClick={handleImageClick}
          >
            <img
              src={selectedItem?.image || img}
              alt="gift-img"
              className={`h-[399px] w-[281px] object-cover rounded-lg  ${
                isBlurring ? "opacity-30 hidden md:block" : ""
              }`}
            />
            {/* Overlay for nudging users to upload image */}
            {(!selectedItem?.image || selectedItem.image === img) && (
              <div className="absolute inset-0 bg-black/30 rounded-lg flex flex-col items-center justify-center text-white">
                <div className="text-center">
                  <svg
                    className="w-12 h-12 mx-auto mb-3 text-white/80"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                  <p className="text-sm font-medium mb-1">Upload Image</p>
                  <p className="text-xs text-white/70">
                    Click to add gift image
                  </p>
                </div>
              </div>
            )}
          </div>
          {touched.image && errors.image && (
            <p className="text-red-500 text-xs mt-2">{errors.image}</p>
          )}
        </div>
        {preview && (
          <PreviewGiftItems
            onBack={handleBack}
            items={giftItems}
            onDeleteItem={removeGiftItem}
          />
        )}
        <div
          className={`max-w-[328px] w-full ${
            isBlurring ? "blur translate-x-28" : ""
          }`}
        >
          <div className="mb-4">
            <label className="block text-grey-500 font-medium text-sm mb-2">
              Link to Item
            </label>
            <div className="relative">
              <input
                type="text"
                value={link}
                onChange={(e) => handleLinkChange(e.target.value)}
                onBlur={() => handleFieldBlur("link", link)}
                onPaste={async (e) => {
                  // Handle paste event to automatically parse the link
                  const pastedText = e.clipboardData.getData("text");
                  if (pastedText && isValidUrl(pastedText)) {
                    // Small delay to ensure the input value is updated
                    setTimeout(() => {
                      parseExternalLink(pastedText);
                    }, 100);
                  }
                }}
                placeholder="Paste link to item"
                className={`w-full h-11 p-3.5 border rounded-full text-base font-normal text-grey-50 placeholder:font-normal placeholder:text-grey-700 italic outline-0 ${
                  touched.link && errors.link
                    ? "border-red-500"
                    : "border-gray-300"
                } ${isParsingLink ? "opacity-50" : ""}`}
                disabled={isParsingLink}
              />
            </div>
            {touched.link && errors.link && (
              <p className="text-red-500 text-xs mt-1">{errors.link}</p>
            )}

            {linkParseError && (
              <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-700 text-xs">{linkParseError}</p>
              </div>
            )}
            {link &&
              isValidUrl(link) &&
              !linkParseData &&
              !isParsingLink &&
              !linkParseError && (
                <button
                  type="button"
                  onClick={() => parseExternalLink(link)}
                  className="mt-2 text-xs text-primary-650 hover:text-primary-700 underline"
                >
                  Parse this link to auto-fill details
                </button>
              )}
          </div>
          <TextInput
            id="giftName"
            label="Gift Name"
            value={giftName}
            onChange={(e) => handleGiftNameChange(e.target.value)}
            onBlur={() => handleFieldBlur("giftName", giftName)}
            placeholder="E.g iPhone 15"
            className={`text-grey-50 font-bold italic placeholder:font-normal placeholder:text-grey-700 ${
              touched.giftName && errors.giftName ? "border-red-500" : ""
            }`}
            error={touched.giftName ? errors.giftName : ""}
          />
          <div className="my-4">
            <div className="flex items-center justify-end">
              <div className="flex  items-center border border-gray-300 rounded-full">
                <button
                  onClick={decrementQuantity}
                  className="border-r px-3 py-1 rounded-l-lg transition-colors"
                  disabled={quantity === 1}
                >
                  <Minus color={quantity <= 1 ? "#E6E6E6" : "#000"} size={20} />
                </button>
                <span className="py-1  text-sm font-medium text-gray-900 px-3.5 text-center">
                  {quantity}
                </span>
                <button
                  onClick={incrementQuantity}
                  className="border-l rounded-r-lg transition-colors px-3 py-1"
                >
                  <Add color="#000" size={20} />
                </button>
              </div>
            </div>
          </div>
          <div className="flex gap-4 my-4">
            <div className="flex-1">
              <label className="block text-grey-500 font-medium text-sm mb-2">
                Price
              </label>
              <div className="flex">
                <div className="relative flex-1">
                  <input
                    type="text"
                    inputMode="numeric"
                    pattern="[0-9,]*"
                    value={price}
                    onChange={(e) => {
                      const value = e.target.value;
                      if (/^[\d,]*$/.test(value)) {
                        handlePriceChange(value);
                      }
                    }}
                    onBlur={() => handleFieldBlur("price", price)}
                    placeholder="₦ Enter Amount"
                    className={`w-full h-11 px-3.5 border border-r-0 rounded-l-full text-base font-normal text-grey-50 placeholder:font-normal placeholder:text-grey-700 italic outline-0 ${
                      touched.price && errors.price
                        ? "border-red-500"
                        : "border-gray-300"
                    }`}
                  />
                </div>
                <div
                  className={`border border-l-0 rounded-r-full px-3.5 flex items-center bg-white ${
                    touched.price && errors.price
                      ? "border-red-500"
                      : "border-gray-300"
                  }`}
                >
                  <span className="text-grey-500 italic mr-1">NGN</span>
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M19 9l-7 7-7-7"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
              </div>
              {touched.price && errors.price && (
                <p className="text-red-500 text-xs mt-1">{errors.price}</p>
              )}
            </div>
          </div>
          <div className="mb-4">
            <label className="block text-grey-500 font-medium text-sm mb-2">
              Description (Optional)
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe your gift item"
              className="w-full p-3.5 border border-gray-300 rounded-2xl text-base font-normal text-grey-50 placeholder:font-normal placeholder:text-grey-700 italic outline-0 [&::-webkit-scrollbar]:hidden h-15 resize-none"
            ></textarea>
          </div>

          <div className="flex items-center gap-3 mb-4">
            <button
              onClick={() => {
                if (!localMostWanted && hasMostWantedItem()) {
                  return;
                }
                setLocalMostWanted(!localMostWanted);
              }}
              disabled={!localMostWanted && hasMostWantedItem()}
              className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                localMostWanted ? "bg-primary-650" : "bg-grey-850"
              } ${
                !localMostWanted && hasMostWantedItem()
                  ? "opacity-50 cursor-not-allowed"
                  : ""
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-grey-350 shadow-[0px_1px_2px_0px_#0A0D120F,_0px_1px_3px_0px_#0A0D121A] transition-transform ${
                  localMostWanted ? "translate-x-4" : "translate-x-1"
                }`}
              />
            </button>
            <span
              className={`text-sm font-medium ${
                !localMostWanted && hasMostWantedItem()
                  ? "text-grey-500/50"
                  : "text-grey-500"
              }`}
            >
              Set as Most wanted Item
              {!localMostWanted && hasMostWantedItem() && (
                <span className="block text-xs text-grey-500/50 italic">
                  Another item is already marked as most wanted
                </span>
              )}
            </span>
          </div>
          <Button
            variant="primary"
            size="md"
            className={`h-7 bg-primary-250 text-primary ${
              !isFormValid() && "!cursor-default"
            }`}
            iconLeft={<AddCircle size="18" color="#4D55F2" variant="Bulk" />}
            onClick={handleAddToQueue}
            disabled={!isFormValid()}
          >
            Add to Queue
          </Button>

          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept="image/*"
            onChange={handleImageUpload}
          />
        </div>

        <button
          onClick={handleNextStep}
          disabled={giftItems.length === 0 && !isFormValid()}
          className={`${
            isBlurring ? "hidden" : ""
          } py-2.5 px-4 rounded-full flex text-white items-center gap-2 transition-colors ${
            giftItems.length === 0 && !isFormValid()
              ? "bg-primary-650/50 cursor-default"
              : "bg-primary-650  cursor-pointer hover:bg-primary-650/80"
          }`}
        >
          Continue
          <div className={`rounded-full p-0.5 bg-white/30`}>
            <ArrowRight size="12" color="#fff" />
          </div>
        </button>
      </div>
      {giftItems.length > 0 && (
        <div
          className={`flex-col  gap-2 md:gap-9  transition-all duration-500 ${
            isBlurring ? "hidden" : "flex"
          }`}
        >
          <div
            className={` items-center justify-center gap-2 md:gap-9  transition-all duration-500 ${
              isBlurring ? "hidden" : "flex"
            }`}
          >
            <div
              className="bg-primary-250 p-2 rounded-full cursor-pointer"
              onClick={handlePrevPage}
            >
              <ArrowCircleLeft2 color="#B8BBFA" size={20} variant="Bulk" />
            </div>
            <div className={`flex justify-center items-center gap-4 `}>
              {giftItems
                .slice(
                  currentPage * itemsPerPage,
                  (currentPage + 1) * itemsPerPage
                )
                .map((item) => (
                  <div
                    key={item.id}
                    className="flex-shrink-0 bg-[#f8f8f8] w-25 h-25 rounded-xl overflow-hidden border border-gray-200 cursor-pointer"
                    onClick={() => handleItemSelect(item as GiftItem)}
                  >
                    <img
                      src={item.image}
                      alt={item.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ))}
            </div>
            <div
              className="bg-primary-750 p-2 rounded-full cursor-pointer"
              onClick={handleNextPage}
            >
              <ArrowCircleRight2 color="#fff" size={20} variant="Bulk" />
            </div>
          </div>
          <button
            type="button"
            onClick={handleContinue}
            disabled={giftItems.length === 0}
            className={`mb-20 lg:ml-70 w-fit text-sm font-semibold border py-2 px-3.5 rounded-full mt-5 transition-colors ${
              giftItems.length === 0
                ? "text-gray-400 bg-gray-100 border-gray-300 cursor-not-allowed"
                : "text-primary bg-white border-primary-950"
            }`}
          >
            View All
          </button>
        </div>
      )}

      {showItemAddedModal && lastAddedItem && (
        <ItemAddedModal
          itemName={lastAddedItem.name}
          onProceedToCashGift={handleProceedToCashGift}
          onContinueAddingItems={handleContinueAddingItems}
        />
      )}

      {/* Suggestion Modal */}
      {showSuggestionModal && lastAddedItem && (
        <SuggestionModal
          itemName={lastAddedItem.name}
          suggestedItem={
            lastAddedItem.name === "iPhone 15 Pro" ? "Earbuds" : "Phone Case"
          }
          onProceedToAdd={handleContinueAddingItems}
          onAddOtherItems={handleContinueAddingItems}
          onClose={() => setShowSuggestionModal(false)}
        />
      )}

      {/* Single Item Warning Modal */}
      <SingleItemWarningModal
        isOpen={showSingleItemWarningModal}
        onClose={handleSingleItemWarningClose}
        onAddMore={handleSingleItemWarningAddMore}
        onContinueAnyway={handleSingleItemWarningContinue}
      />

      {/* Followup Modal */}
      <FollowupModal
        isOpen={showFollowupModal}
        onClose={handleFollowupClose}
        onProceedToCashGift={handleFollowupProceedToCashGift}
        onContinueAddingItems={handleFollowupContinueAddingItems}
      />
    </div>
  );
};
