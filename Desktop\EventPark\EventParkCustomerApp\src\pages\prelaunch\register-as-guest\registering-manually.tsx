
import { Button } from '../../../components/button/button';

export const RegisterManually = () => {
  return (
    <div className=" flex-1 flex flex-col justify-between  bg-white rounded-2xl px-5 pt-7 pb-6">
      <div>
        <h2 className="text-2xl font-semibold mb-5.5 ">Preview your Details</h2>
        <div className="border border-grey-900 pt-1.5  rounded-xl bg-[linear-gradient(180deg,_#FAFAFA_6.93%,_#FFFFFF_100%)]">
          <div className="flex justify-between items-center text-sm md:text-base py-5 border-b border-grey-900 px-3.5">
            <span className="text-grey-250 text">Fullname</span>
            <span className="font-bold">Ad<PERSON><PERSON></span>
          </div>

          <div className="flex justify-between items-center text-sm md:text-base py-5 mt-1.5 border-b border-grey-900  px-3.5">
            <span className="text-grey-250">Email</span>
            <span className="font-bold break-all ml-7 sm:ml-0">
              <EMAIL>
            </span>
          </div>

          <div className="flex justify-between items-center text-sm md:text-base py-5 mt-1.5  px-3.5">
            <span className="text-grey-250">Mobile Number</span>
            <span className="font-bold">+234 7015263711</span>
          </div>
        </div>
      </div>
      <div className="mt-20 lg:mt-0">
        <Button type="submit" variant="primary">
          Accept Invitation
        </Button>{' '}
        <Button
          type="submit"
          variant="primary"
          className="bg-cus-pink-700 mt-4 text-cus-red font-semibold hover:bg-cus-pink-700/90">
          Reject Invitation{' '}
        </Button>{' '}
      </div>
    </div>
  );
};
