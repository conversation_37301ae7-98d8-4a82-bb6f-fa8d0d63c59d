import moment from "moment";
import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";

export type ToolStatus = {
  has_bank_account: boolean;
  has_gift_registry: boolean;
  has_guestlist: boolean;
  has_wallet: boolean;
};

export interface AuthState {
  userAppToken: string | null;
  refreshToken: string | null;
  accessTokenExpiresAt: string | null;
  refreshTokenExpiresAt: string | null;
  userData: {
    email: string;
    first_name: string;
    last_name: string;
    id: string;
    password_set: boolean;
    profile_picture: string;
    transaction_pin_set: boolean;
  } | null;
  toolStatus: ToolStatus | null;
  setAuthData: (
    token: string,
    userData: {
      email: string;
      first_name: string;
      last_name: string;
      id: string;
      password_set: boolean;
      profile_picture: string;
      transaction_pin_set: boolean;
    },
    refreshToken?: string,
    accessTokenExpiresAt?: string,
    refreshTokenExpiresAt?: string
  ) => void;
  setTokens: (
    accessToken: string,
    refreshToken: string,
    accessTokenExpiresAt: string,
    refreshTokenExpiresAt: string
  ) => void;
  setToolStatus: (toolStatus: ToolStatus) => void;
  clearAuthData: () => void;
  isAccessTokenExpired: () => boolean;
  isRefreshTokenExpired: () => boolean;
  shouldRefreshToken: () => boolean;
}

const isTokenExpired = (expiresAt: string | null): boolean => {
  console.log(expiresAt);
  if (expiresAt === null) return true;
  const tokenExpiry = moment.utc(expiresAt);
  const currentTime = moment.utc();
  const isExpired = tokenExpiry.isBefore(currentTime);

  console.log(`Token expiry: ${tokenExpiry.format()} UTC`);
  console.log(`Current time: ${currentTime.format()} UTC`);
  console.log(`Is expired: ${isExpired}`);

  return isExpired;
};

const shouldRefreshToken = (expiresAt: string | null): boolean => {
  if (!expiresAt) return true;
  // Use moment.utc for consistent UTC handling
  const expiryTime = moment.utc(expiresAt);

  return expiryTime.isSameOrBefore(moment.utc());
};

export const useUserAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      userAppToken: null,
      refreshToken: null,
      accessTokenExpiresAt: null,
      refreshTokenExpiresAt: null,
      userData: null,
      toolStatus: null,
      setAuthData: (
        token,
        userData,
        refreshToken,
        accessTokenExpiresAt,
        refreshTokenExpiresAt
      ) =>
        set({
          userAppToken: token,
          userData,
          ...(refreshToken && { refreshToken }),
          ...(accessTokenExpiresAt && { accessTokenExpiresAt }),
          ...(refreshTokenExpiresAt && { refreshTokenExpiresAt }),
        }),
      setTokens: (
        accessToken,
        refreshToken,
        accessTokenExpiresAt,
        refreshTokenExpiresAt
      ) => {
        // Use explicit set to ensure immediate update
        set(
          {
            userAppToken: accessToken,
            refreshToken,
            accessTokenExpiresAt,
            refreshTokenExpiresAt,
          },
          false // Don't replace entire state, just merge
        );
      },
      setToolStatus: (toolStatus) => set({ toolStatus }),
      clearAuthData: () =>
        set({
          userAppToken: null,
          refreshToken: null,
          accessTokenExpiresAt: null,
          refreshTokenExpiresAt: null,
          userData: null,
          toolStatus: null,
        }),
      isAccessTokenExpired: () => {
        const { accessTokenExpiresAt } = get();
        return isTokenExpired(accessTokenExpiresAt);
      },
      isRefreshTokenExpired: () => {
        const { refreshTokenExpiresAt } = get();
        return isTokenExpired(refreshTokenExpiresAt);
      },
      shouldRefreshToken: () => {
        const { accessTokenExpiresAt } = get();
        return shouldRefreshToken(accessTokenExpiresAt);
      },
    }),
    {
      name: "user-auth-store",
      storage: createJSONStorage(() => localStorage),
    }
  )
);
