0000000000000000000000000000000000000000 6a3114713d2654bf4ef65e11084ea7b9ff649e23 Bhiekeys <<EMAIL>> 1752151978 +0100	clone: from https://github.com/EventPark/EventParkCustomerApp.git
6a3114713d2654bf4ef65e11084ea7b9ff649e23 6a3114713d2654bf4ef65e11084ea7b9ff649e23 Bhiekeys <<EMAIL>> 1752170552 +0100	checkout: moving from main to contributors
6a3114713d2654bf4ef65e11084ea7b9ff649e23 a72288b6dd6ed19070fe476531f5298cfac6c49d Bhiekeys <<EMAIL>> 1752170575 +0100	commit: integrate contributors endpoint
a72288b6dd6ed19070fe476531f5298cfac6c49d 809107b8f45aad60d36a10577ad2b481d90e88bf Bhiekeys <<EMAIL>> 1752170925 +0100	commit: fix lint err
809107b8f45aad60d36a10577ad2b481d90e88bf 809107b8f45aad60d36a10577ad2b481d90e88bf Bhiekeys <<EMAIL>> 1752170956 +0100	checkout: moving from contributors to contributor
809107b8f45aad60d36a10577ad2b481d90e88bf 6a3114713d2654bf4ef65e11084ea7b9ff649e23 Bhiekeys <<EMAIL>> 1752235728 +0100	checkout: moving from contributor to main
6a3114713d2654bf4ef65e11084ea7b9ff649e23 5901fa815de34bd7373c5ff3a827f34885b6a283 Bhiekeys <<EMAIL>> 1752235761 +0100	pull origin main: Fast-forward
5901fa815de34bd7373c5ff3a827f34885b6a283 5901fa815de34bd7373c5ff3a827f34885b6a283 Bhiekeys <<EMAIL>> 1752243076 +0100	checkout: moving from main to nudge-fix
5901fa815de34bd7373c5ff3a827f34885b6a283 fb28a7ff63ffe9ebda00a6ccce64718b594ab585 Bhiekeys <<EMAIL>> 1752243093 +0100	commit: fix ui and nudge fix
fb28a7ff63ffe9ebda00a6ccce64718b594ab585 5901fa815de34bd7373c5ff3a827f34885b6a283 Bhiekeys <<EMAIL>> 1752245124 +0100	checkout: moving from nudge-fix to main
5901fa815de34bd7373c5ff3a827f34885b6a283 41b84ac99bea795e05c5692c76af3c4f5bdcd6cf Bhiekeys <<EMAIL>> 1752245140 +0100	pull origin main: Fast-forward
41b84ac99bea795e05c5692c76af3c4f5bdcd6cf 41b84ac99bea795e05c5692c76af3c4f5bdcd6cf Bhiekeys <<EMAIL>> 1752247128 +0100	checkout: moving from main to fix-activation
41b84ac99bea795e05c5692c76af3c4f5bdcd6cf 66926056c361f760b3ec72b7c6a48ca024e413ed Bhiekeys <<EMAIL>> 1752247148 +0100	commit: fix activation on gift/cash items
66926056c361f760b3ec72b7c6a48ca024e413ed 41b84ac99bea795e05c5692c76af3c4f5bdcd6cf Bhiekeys <<EMAIL>> 1752252749 +0100	checkout: moving from fix-activation to main
41b84ac99bea795e05c5692c76af3c4f5bdcd6cf 3834f0a881795f126e47dc6c19055989a2ca5083 Bhiekeys <<EMAIL>> 1752252766 +0100	pull origin main: Fast-forward
3834f0a881795f126e47dc6c19055989a2ca5083 3834f0a881795f126e47dc6c19055989a2ca5083 Bhiekeys <<EMAIL>> 1752520498 +0100	checkout: moving from main to edit-event
3834f0a881795f126e47dc6c19055989a2ca5083 159a68019d0a5e6d04dc95520ff530a5daaece2d Bhiekeys <<EMAIL>> 1752520562 +0100	commit: add edit event
159a68019d0a5e6d04dc95520ff530a5daaece2d 3834f0a881795f126e47dc6c19055989a2ca5083 Bhiekeys <<EMAIL>> 1752635656 +0100	checkout: moving from edit-event to main
3834f0a881795f126e47dc6c19055989a2ca5083 e7f11fdb3d9eb8513f51e2a3b5cdc76ab711138d Bhiekeys <<EMAIL>> 1752635673 +0100	pull origin main: Fast-forward
e7f11fdb3d9eb8513f51e2a3b5cdc76ab711138d e7f11fdb3d9eb8513f51e2a3b5cdc76ab711138d Bhiekeys <<EMAIL>> 1752652875 +0100	reset: moving to HEAD
e7f11fdb3d9eb8513f51e2a3b5cdc76ab711138d e7f11fdb3d9eb8513f51e2a3b5cdc76ab711138d Bhiekeys <<EMAIL>> 1752653014 +0100	reset: moving to HEAD
e7f11fdb3d9eb8513f51e2a3b5cdc76ab711138d e7f11fdb3d9eb8513f51e2a3b5cdc76ab711138d Bhiekeys <<EMAIL>> 1752654044 +0100	reset: moving to HEAD
e7f11fdb3d9eb8513f51e2a3b5cdc76ab711138d df8fa18dcd5b94941b44ff3f88f7e047469e2e3c Bhiekeys <<EMAIL>> 1752654137 +0100	pull origin main: Fast-forward
df8fa18dcd5b94941b44ff3f88f7e047469e2e3c df8fa18dcd5b94941b44ff3f88f7e047469e2e3c Bhiekeys <<EMAIL>> 1752658379 +0100	checkout: moving from main to tools-check-update
df8fa18dcd5b94941b44ff3f88f7e047469e2e3c a8194939733b8d46ca315ef47ddb609639755c9f Bhiekeys <<EMAIL>> 1752658401 +0100	commit: update registry tool chack
a8194939733b8d46ca315ef47ddb609639755c9f df8fa18dcd5b94941b44ff3f88f7e047469e2e3c Bhiekeys <<EMAIL>> 1752662754 +0100	checkout: moving from tools-check-update to main
df8fa18dcd5b94941b44ff3f88f7e047469e2e3c 797da8177ae65fe4d616390d47981c12868aed7c Bhiekeys <<EMAIL>> 1752662783 +0100	pull origin main: Fast-forward
797da8177ae65fe4d616390d47981c12868aed7c 797da8177ae65fe4d616390d47981c12868aed7c Bhiekeys <<EMAIL>> 1752685576 +0100	checkout: moving from main to iv-template
797da8177ae65fe4d616390d47981c12868aed7c c144b470f20c0b7617a84f934899f063ca9cbf2a Bhiekeys <<EMAIL>> 1752685590 +0100	commit: integrate iv design
c144b470f20c0b7617a84f934899f063ca9cbf2a 797da8177ae65fe4d616390d47981c12868aed7c Bhiekeys <<EMAIL>> 1752738384 +0100	checkout: moving from iv-template to main
797da8177ae65fe4d616390d47981c12868aed7c 0a48ababd23c4b50a7ff53d94dcda2138cd91976 Bhiekeys <<EMAIL>> 1752738404 +0100	pull origin main: Fast-forward
0a48ababd23c4b50a7ff53d94dcda2138cd91976 0a48ababd23c4b50a7ff53d94dcda2138cd91976 Bhiekeys <<EMAIL>> 1752758304 +0100	checkout: moving from main to iv-upload
0a48ababd23c4b50a7ff53d94dcda2138cd91976 ccb7af548bf4436e85fb56702a0f76055a137ee1 Bhiekeys <<EMAIL>> 1752758320 +0100	commit: add iv upload
ccb7af548bf4436e85fb56702a0f76055a137ee1 0a48ababd23c4b50a7ff53d94dcda2138cd91976 Bhiekeys <<EMAIL>> 1752770633 +0100	checkout: moving from iv-upload to main
0a48ababd23c4b50a7ff53d94dcda2138cd91976 ddbf6e9bc6c92fde32ab8253ad569e72ec21a147 Bhiekeys <<EMAIL>> 1752770674 +0100	pull origin main: Fast-forward
ddbf6e9bc6c92fde32ab8253ad569e72ec21a147 ddbf6e9bc6c92fde32ab8253ad569e72ec21a147 Bhiekeys <<EMAIL>> 1752846818 +0100	checkout: moving from main to tool-update
ddbf6e9bc6c92fde32ab8253ad569e72ec21a147 76dadc98d3657d50804f492d409b997fde66e0a4 Bhiekeys <<EMAIL>> 1752846837 +0100	commit: update tool check
76dadc98d3657d50804f492d409b997fde66e0a4 ddbf6e9bc6c92fde32ab8253ad569e72ec21a147 Bhiekeys <<EMAIL>> 1753079221 +0100	checkout: moving from tool-update to main
ddbf6e9bc6c92fde32ab8253ad569e72ec21a147 540ebbad8d71f215e48e5a2c0bf9565ab994f3a3 Bhiekeys <<EMAIL>> 1753079238 +0100	pull origin main: Fast-forward
540ebbad8d71f215e48e5a2c0bf9565ab994f3a3 540ebbad8d71f215e48e5a2c0bf9565ab994f3a3 Bhiekeys <<EMAIL>> 1753087561 +0100	checkout: moving from main to upload-fix
540ebbad8d71f215e48e5a2c0bf9565ab994f3a3 5b666a711dcc1a0d2bdc2e41be1359abf53ff2f7 Bhiekeys <<EMAIL>> 1753087596 +0100	commit: add upload skeleton
5b666a711dcc1a0d2bdc2e41be1359abf53ff2f7 67559364f8c94b86a0393b9e9e214caef14a36f8 Bhiekeys <<EMAIL>> 1753088543 +0100	commit: adjust ui for create event
67559364f8c94b86a0393b9e9e214caef14a36f8 540ebbad8d71f215e48e5a2c0bf9565ab994f3a3 Bhiekeys <<EMAIL>> 1753089556 +0100	checkout: moving from upload-fix to main
540ebbad8d71f215e48e5a2c0bf9565ab994f3a3 d0d04e70431c06df71082dba3ffcd6997770c0ac Bhiekeys <<EMAIL>> 1753089569 +0100	pull origin main: Fast-forward
d0d04e70431c06df71082dba3ffcd6997770c0ac d0d04e70431c06df71082dba3ffcd6997770c0ac Bhiekeys <<EMAIL>> 1753108353 +0100	checkout: moving from main to guest-count
d0d04e70431c06df71082dba3ffcd6997770c0ac ecea259f4851f3cec547662f6c80e21a717ce1a8 Bhiekeys <<EMAIL>> 1753108381 +0100	commit: update guest-count
ecea259f4851f3cec547662f6c80e21a717ce1a8 ecea259f4851f3cec547662f6c80e21a717ce1a8 Bhiekeys <<EMAIL>> 1753112486 +0100	checkout: moving from guest-count to update-transaction-pin
ecea259f4851f3cec547662f6c80e21a717ce1a8 805e93991076f1774c28c96c0a8f01402b5a9df4 Bhiekeys <<EMAIL>> 1753112505 +0100	commit: update transaction pin
805e93991076f1774c28c96c0a8f01402b5a9df4 d0d04e70431c06df71082dba3ffcd6997770c0ac Bhiekeys <<EMAIL>> 1753185619 +0100	checkout: moving from update-transaction-pin to main
d0d04e70431c06df71082dba3ffcd6997770c0ac 875480258f7a8b24e2d4b83cfe73cafb587a4c5d Bhiekeys <<EMAIL>> 1753185641 +0100	pull origin main: Fast-forward
875480258f7a8b24e2d4b83cfe73cafb587a4c5d 875480258f7a8b24e2d4b83cfe73cafb587a4c5d Bhiekeys <<EMAIL>> 1753194709 +0100	checkout: moving from main to settings
875480258f7a8b24e2d4b83cfe73cafb587a4c5d 0d22ecb51fb4b04babefe9b420f07e209ec5f777 Bhiekeys <<EMAIL>> 1753194724 +0100	commit: update settings
0d22ecb51fb4b04babefe9b420f07e209ec5f777 7c6efbacacd730f0dafad5ac1cd4f5beaf9c9ac8 Bhiekeys <<EMAIL>> 1753195057 +0100	pull origin main: Merge made by the 'ort' strategy.
7c6efbacacd730f0dafad5ac1cd4f5beaf9c9ac8 7c6efbacacd730f0dafad5ac1cd4f5beaf9c9ac8 Bhiekeys <<EMAIL>> 1753274385 +0100	checkout: moving from settings to fixes
7c6efbacacd730f0dafad5ac1cd4f5beaf9c9ac8 1a0fb2e70ea94e40a69b338a581efbec86530924 Bhiekeys <<EMAIL>> 1753274403 +0100	commit: fix guestlist count
1a0fb2e70ea94e40a69b338a581efbec86530924 875480258f7a8b24e2d4b83cfe73cafb587a4c5d Bhiekeys <<EMAIL>> 1753274614 +0100	checkout: moving from fixes to main
875480258f7a8b24e2d4b83cfe73cafb587a4c5d 957fd14d63d4e819195c86105081ec163fd208aa Bhiekeys <<EMAIL>> 1753274630 +0100	pull origin main: Fast-forward
957fd14d63d4e819195c86105081ec163fd208aa 957fd14d63d4e819195c86105081ec163fd208aa Bhiekeys <<EMAIL>> 1753354852 +0100	checkout: moving from main to item-parse
957fd14d63d4e819195c86105081ec163fd208aa 7b992e4a86473e4305b895adb9cf19678736b5f6 Bhiekeys <<EMAIL>> 1753354884 +0100	commit: add item parsing while creating gift item
7b992e4a86473e4305b895adb9cf19678736b5f6 7b992e4a86473e4305b895adb9cf19678736b5f6 Bhiekeys <<EMAIL>> 1753522320 +0100	reset: moving to HEAD
7b992e4a86473e4305b895adb9cf19678736b5f6 7b992e4a86473e4305b895adb9cf19678736b5f6 Bhiekeys <<EMAIL>> 1753522347 +0100	checkout: moving from item-parse to keeping
7b992e4a86473e4305b895adb9cf19678736b5f6 39a3230d41218fc68a6cd5d59a95e6a290a33942 Bhiekeys <<EMAIL>> 1753522358 +0100	commit: keeping
39a3230d41218fc68a6cd5d59a95e6a290a33942 39a3230d41218fc68a6cd5d59a95e6a290a33942 Bhiekeys <<EMAIL>> 1753522652 +0100	checkout: moving from keeping to welcome-page
39a3230d41218fc68a6cd5d59a95e6a290a33942 957fd14d63d4e819195c86105081ec163fd208aa Bhiekeys <<EMAIL>> 1753525010 +0100	checkout: moving from welcome-page to main
957fd14d63d4e819195c86105081ec163fd208aa 31470bc8a1876bc854b38d4af0821f4cf827c0a7 Bhiekeys <<EMAIL>> 1753525039 +0100	pull origin main: Fast-forward
31470bc8a1876bc854b38d4af0821f4cf827c0a7 31470bc8a1876bc854b38d4af0821f4cf827c0a7 Bhiekeys <<EMAIL>> 1753538691 +0100	checkout: moving from main to delivery-address
31470bc8a1876bc854b38d4af0821f4cf827c0a7 59c5ff04c411ea2fb7887de5f5a8fb5dfd89deb6 Bhiekeys <<EMAIL>> 1753538713 +0100	commit: update delivery address
59c5ff04c411ea2fb7887de5f5a8fb5dfd89deb6 dd9ff8af353119f3d778df6a29fc2d03de8d76a3 Bhiekeys <<EMAIL>> 1753540933 +0100	commit: add check
dd9ff8af353119f3d778df6a29fc2d03de8d76a3 31470bc8a1876bc854b38d4af0821f4cf827c0a7 Bhiekeys <<EMAIL>> 1753548433 +0100	checkout: moving from delivery-address to main
31470bc8a1876bc854b38d4af0821f4cf827c0a7 bd5431dbd0fd1b2332309b95d9190fa43b14c8f5 Bhiekeys <<EMAIL>> 1753548460 +0100	pull origin main: Fast-forward
bd5431dbd0fd1b2332309b95d9190fa43b14c8f5 139d24303170bcfeba66c0fa4d2c2532138a9a45 Bhiekeys <<EMAIL>> 1753549050 +0100	commit: add password validate
139d24303170bcfeba66c0fa4d2c2532138a9a45 139d24303170bcfeba66c0fa4d2c2532138a9a45 Bhiekeys <<EMAIL>> 1753549065 +0100	checkout: moving from main to password-validate
139d24303170bcfeba66c0fa4d2c2532138a9a45 139d24303170bcfeba66c0fa4d2c2532138a9a45 Bhiekeys <<EMAIL>> 1753549872 +0100	checkout: moving from password-validate to quick-act
139d24303170bcfeba66c0fa4d2c2532138a9a45 139d24303170bcfeba66c0fa4d2c2532138a9a45 Bhiekeys <<EMAIL>> 1754045905 +0100	checkout: moving from quick-act to main
139d24303170bcfeba66c0fa4d2c2532138a9a45 139d24303170bcfeba66c0fa4d2c2532138a9a45 Bhiekeys <<EMAIL>> 1754045944 +0100	pull origin main: updating HEAD
139d24303170bcfeba66c0fa4d2c2532138a9a45 139d24303170bcfeba66c0fa4d2c2532138a9a45 Bhiekeys <<EMAIL>> 1754045996 +0100	pull origin main: updating HEAD
139d24303170bcfeba66c0fa4d2c2532138a9a45 8d0c53cea43f168d63bc32a67b2c66e7de42e8c6 Bhiekeys <<EMAIL>> 1754046060 +0100	pull origin main: Merge made by the 'ort' strategy.
8d0c53cea43f168d63bc32a67b2c66e7de42e8c6 8d0c53cea43f168d63bc32a67b2c66e7de42e8c6 Bhiekeys <<EMAIL>> 1754052340 +0100	checkout: moving from main to confirm-modal
8d0c53cea43f168d63bc32a67b2c66e7de42e8c6 1dd0b64914ac2b803e68bb98a5190f43f2a8d428 Bhiekeys <<EMAIL>> 1754052369 +0100	commit: add confirmation modal for reject invite
1dd0b64914ac2b803e68bb98a5190f43f2a8d428 8d0c53cea43f168d63bc32a67b2c66e7de42e8c6 Bhiekeys <<EMAIL>> 1754074496 +0100	checkout: moving from confirm-modal to main
8d0c53cea43f168d63bc32a67b2c66e7de42e8c6 353b553c5ddc373bf93bf720d496d8bcf265ef79 Bhiekeys <<EMAIL>> 1754074514 +0100	pull origin main: Merge made by the 'ort' strategy.
