import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  ReactNode,
} from 'react';

export interface CashGift {
  id: number;
  amount: string;
  description: string;
  image?: string;
  is_crowd_gift: boolean;
}

interface CashGiftContextType {
  cashGifts: CashGift[];
  addCashGift: (gift: Omit<CashGift, 'id'>) => void;
  removeCashGift: (id: number) => void;
  updateCashGift: (id: number, updates: Partial<CashGift>) => void;
  clearCashGifts: () => void;
  setCashGifts: (gifts: CashGift[]) => void;
  getTotalAmount: () => number;
}

const CashGiftContext = createContext<CashGiftContextType | undefined>(
  undefined
);

interface CashGiftProviderProps {
  children: ReactNode;
  initialCashGifts?: CashGift[];
}

export const CashGiftProvider: React.FC<CashGiftProviderProps> = ({
  children,
  initialCashGifts = [],
}) => {
  const [cashGifts, setCashGiftsState] = useState<CashGift[]>(initialCashGifts);

  const addCashGift = useCallback((gift: Omit<CashGift, 'id'>) => {
    const newGift: CashGift = {
      ...gift,
      id: Date.now() + Math.random(), // Ensure unique ID
    };
    setCashGiftsState((prev) => [...prev, newGift]);
  }, []);

  const removeCashGift = useCallback((id: number) => {
    setCashGiftsState((prev) => prev.filter((gift) => gift.id !== id));
  }, []);

  const updateCashGift = useCallback(
    (id: number, updates: Partial<CashGift>) => {
      setCashGiftsState((prev) =>
        prev.map((gift) => (gift.id === id ? { ...gift, ...updates } : gift))
      );
    },
    []
  );

  const clearCashGifts = useCallback(() => {
    setCashGiftsState([]);
  }, []);

  const setCashGifts = useCallback((gifts: CashGift[]) => {
    setCashGiftsState(gifts);
  }, []);

  const getTotalAmount = useCallback(() => {
    return cashGifts.reduce((total, gift) => {
      const amount = parseFloat(gift.amount.replace(/,/g, '')) || 0;
      return total + amount;
    }, 0);
  }, [cashGifts]);

  const value: CashGiftContextType = {
    cashGifts,
    addCashGift,
    removeCashGift,
    updateCashGift,
    clearCashGifts,
    setCashGifts,
    getTotalAmount,
  };

  return (
    <CashGiftContext.Provider value={value}>
      {children}
    </CashGiftContext.Provider>
  );
};

export const useCashGift = (): CashGiftContextType => {
  const context = useContext(CashGiftContext);
  if (context === undefined) {
    throw new Error('useCashGift must be used within a CashGiftProvider');
  }
  return context;
};
