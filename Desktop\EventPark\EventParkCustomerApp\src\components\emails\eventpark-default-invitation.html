<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>EventPark Default Invitation</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=MonteCarlo&family=Miama&family=Anonymous+Pro:wght@400&family=Charter:wght@400&display=swap"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Charter", serif;
        background-color: #f5f5f5;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
      }

      .invitation-container {
        position: relative;
        width: 1240px;
        height: 1748px;
        background-image: url("https://customer-preprod.eventpark.africa/assets/email-templates/eventpark-default-background.png");
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        max-width: 100%;
        max-height: 100vh;
        object-fit: contain;
      }

      .text-element {
        position: absolute;
      }

      .invitation-text {
        font-family: "MonteCarlo", cursive;
        font-weight: 400;
        font-size: 220px;
        line-height: 1.049999930641868em;
        letter-spacing: 12%;
        text-align: left;
        color: #4d55f2;
        left: 136px;
        top: 470.9359636902809px;
        width: 808.7538530230522px;
        height: 629.9846253991127px;
        transform-origin: left center;
      }

      .celebration-text {
        font-family: "Miama", cursive;
        font-weight: 400;
        font-size: 56px;
        line-height: 1.0499999182564872em;
        letter-spacing: 3%;
        text-align: left;
        color: #4d55f2;
        left: 208px;
        top: 1248px;
        width: 824px;
        height: 59px;
        transform-origin: left center;
      }

      .celebrant-names {
        font-family: "Miama", cursive;
        font-weight: 400;
        font-size: 60px;
        line-height: 1.0499999364217123em;
        letter-spacing: 2.9999999999999996%;
        text-align: left;
        color: #ff5519;
        left: 271px;
        top: 1389px;
        width: 699px;
        height: 63px;
        transform-origin: left center;
      }

      .event-type {
        font-family: "Anonymous Pro", monospace;
        font-weight: 400;
        font-size: 66px;
        line-height: 1.049999930641868em;
        letter-spacing: 20%;
        text-transform: uppercase;
        text-align: left;
        color: #4d55f2;
        left: 356px;
        top: 1466px;
        width: 529px;
        height: 69px;
        transform-origin: left center;
      }

      .event-date {
        font-family: "Charter", serif;
        font-weight: 400;
        font-size: 42px;
        line-height: 1.22021484375em;
        letter-spacing: 3%;
        text-align: center;
        color: #000059;
        left: 378px;
        top: 1122px;
        width: 484px;
        height: 51px;
      }

      .venue-address {
        font-family: "Charter", serif;
        font-weight: 400;
        font-size: 38px;
        line-height: 1.22021484375em;
        text-align: center;
        color: #000059;
        left: 278px;
        top: 1575px;
        width: 685px;
        height: 46px;
      }

      .rsvp-info {
        font-family: "Charter", serif;
        font-weight: 400;
        font-size: 38px;
        line-height: 1.22021484375em;
        text-align: center;
        color: #000059;
        left: 446px;
        top: 1633px;
        width: 348px;
        height: 46px;
      }

      /* Dynamic scaling for invitation text */
      .invitation-text[data-length="8"] {
        transform: scale(0.95);
      }
      .invitation-text[data-length="9"] {
        transform: scale(0.9);
      }
      .invitation-text[data-length="10"] {
        transform: scale(0.85);
      }
      .invitation-text[data-length="11"] {
        transform: scale(0.8);
      }
      .invitation-text[data-length="12"] {
        transform: scale(0.75);
      }
      .invitation-text[data-length="13"] {
        transform: scale(0.7);
      }
      .invitation-text[data-length="14"] {
        transform: scale(0.65);
      }
      .invitation-text[data-length="15"] {
        transform: scale(0.6);
      }
      .invitation-text[data-length="16"] {
        transform: scale(0.55);
      }
      .invitation-text[data-length="17"] {
        transform: scale(0.5);
      }
      .invitation-text[data-length="18"],
      .invitation-text[data-length="19"],
      .invitation-text[data-length="20"] {
        transform: scale(0.45);
      }

      /* Dynamic scaling for celebrant names */
      .celebrant-names[data-length="8"] {
        transform: scale(0.95);
      }
      .celebrant-names[data-length="9"] {
        transform: scale(0.9);
      }
      .celebrant-names[data-length="10"] {
        transform: scale(0.85);
      }
      .celebrant-names[data-length="11"] {
        transform: scale(0.8);
      }
      .celebrant-names[data-length="12"] {
        transform: scale(0.75);
      }
      .celebrant-names[data-length="13"] {
        transform: scale(0.7);
      }
      .celebrant-names[data-length="14"] {
        transform: scale(0.65);
      }
      .celebrant-names[data-length="15"] {
        transform: scale(0.6);
      }
      .celebrant-names[data-length="16"] {
        transform: scale(0.55);
      }
      .celebrant-names[data-length="17"] {
        transform: scale(0.5);
      }
      .celebrant-names[data-length="18"],
      .celebrant-names[data-length="19"],
      .celebrant-names[data-length="20"] {
        transform: scale(0.45);
      }

      @media screen and (max-width: 1280px) {
        .invitation-container {
          transform: scale(0.8);
          transform-origin: center center;
        }
      }

      @media screen and (max-width: 1024px) {
        .invitation-container {
          transform: scale(0.6);
          transform-origin: center center;
        }
      }

      @media screen and (max-width: 768px) {
        .invitation-container {
          transform: scale(0.4);
          transform-origin: center center;
        }
      }
    </style>
  </head>
  <body>
    <div class="invitation-container">
      <div class="text-element invitation-text">You are<br />Invited!</div>

      <div class="text-element celebration-text">
        Join this happy celebration
      </div>

      <div class="text-element celebrant-names">
        {{#if celebrant_names}} {{{celebrant_names}}} {{else}} Alexandra &
        Richard {{/if}}
      </div>

      <div class="text-element event-type">
        {{#if event_type}} {{{event_type}}} {{else}} anniversary {{/if}}
      </div>

      <div class="text-element event-date">{{{event_date}}}</div>

      <div class="text-element venue-address">{{{venue_address}}}</div>

      <div class="text-element rsvp-info">{{{rsvp_info}}}</div>
    </div>

    <script>
      function calculateTextLengths() {
        const invitationText = document.querySelector(".invitation-text");
        const celebrantNames = document.querySelector(".celebrant-names");

        if (invitationText) {
          const textContent =
            invitationText.textContent || invitationText.innerText;
          const cleanText = textContent.replace(/\s+/g, " ").trim();
          const totalLength = cleanText.length;
          invitationText.setAttribute("data-length", totalLength.toString());
          console.log(`Invitation text length: ${totalLength} characters`);
        }

        if (celebrantNames) {
          const textContent =
            celebrantNames.textContent || celebrantNames.innerText;
          const cleanText = textContent.replace(/\s+/g, " ").trim();
          const totalLength = cleanText.length;
          celebrantNames.setAttribute("data-length", totalLength.toString());
          console.log(`Celebrant names length: ${totalLength} characters`);
        }
      }

      // Run the calculation when the page loads
      document.addEventListener("DOMContentLoaded", calculateTextLengths);

      // Also run it after a short delay to ensure all content is loaded
      setTimeout(calculateTextLengths, 100);
    </script>
  </body>
</html>
