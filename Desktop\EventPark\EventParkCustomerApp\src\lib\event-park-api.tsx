/* eslint-disable @typescript-eslint/no-explicit-any */
import axios, { AxiosError, InternalAxiosRequestConfig } from "axios";
import { useUserAuthStore } from "./store/auth";
import { useEventStore } from "./store/event";
import { AuthServices } from "./services/auth";
import { toast } from "react-toastify";

// Track if token refresh is in progress to avoid multiple simultaneous refresh attempts
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value: string) => void;
  reject: (error: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token!);
    }
  });

  failedQueue = [];
};

// Helper function to clear all auth data
const clearAuthData = () => {
  useUserAuthStore.getState().clearAuthData();
  useEventStore.getState().clearAllEventData();
};

export const EventParkAPI = () => {
  const baseURL = import.meta.env.VITE_API_BASE_URL;
  const publicPaths = ["/login", "/signup", "/forgot-password"];

  const axiosInstance = axios.create({
    baseURL,
    timeout: 30000,
    headers: {
      "Content-Type": "application/json",
    },
  });

  axiosInstance.interceptors.request.use(
    async (config: InternalAxiosRequestConfig) => {
      // Check if we should proactively refresh the token (before it expires)
      if (isRefreshing) {
        // If already refreshing, queue this request and wait for new token
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then((token) => {
            config.headers.Authorization = `Bearer ${token}`;
            return config;
          })
          .catch((err) => {
            return Promise.reject(err);
          });
      }

      // Get fresh auth state after potential queue wait
      const authStore = useUserAuthStore.getState();
      const { userAppToken, shouldRefreshToken, isRefreshTokenExpired } =
        authStore;

      // Add current token to request headers if available
      if (userAppToken) {
        config.headers.Authorization = `Bearer ${userAppToken}`;
      }

      // Check if we should proactively refresh the token (before it expires)
      if (userAppToken && shouldRefreshToken() && !isRefreshTokenExpired()) {
        isRefreshing = true;

        try {
          console.log("🔄 Starting proactive token refresh...");
          // Proactively refresh the token
          const response = await AuthServices.refreshToken();
          console.log("✅ Proactive refresh response received");
          const {
            access_token,
            refresh_token,
            access_token_expires_at,
            refresh_token_expires_at,
          } = response.data;

          // Update tokens in store with expiry dates
          authStore.setTokens(
            access_token,
            refresh_token,
            access_token_expires_at,
            refresh_token_expires_at
          );

          // Force a small delay to ensure localStorage is updated
          await new Promise((resolve) => setTimeout(resolve, 10));

          // Process queued requests with new token
          processQueue(null, access_token);

          // Update current request with new token
          config.headers.Authorization = `Bearer ${access_token}`;
          console.log("✅ Proactive token refresh completed successfully");
        } catch (refreshError) {
          // Refresh failed, clear auth data and redirect
          console.error("❌ Proactive token refresh failed:", refreshError);
          processQueue(refreshError, null);
          clearAuthData();
          toast.error("Session expired. Please login again.");
          console.error("Proactive token refresh failed, redirecting to login");
          setTimeout(() => (window.location.href = "/login"), 1000);
          return Promise.reject(refreshError);
        } finally {
          isRefreshing = false;
        }
      }

      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  axiosInstance.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
      const originalRequest = error.config as InternalAxiosRequestConfig & {
        _retry?: boolean;
      };
      const currentPath = window.location.pathname;
      const isPublicPath = publicPaths.includes(currentPath);

      // Handle 401 errors (fallback for cases where proactive refresh didn't work)
      if (
        error.response?.status === 401 &&
        !isPublicPath &&
        !originalRequest._retry
      ) {
        const authStore = useUserAuthStore.getState();
        const { userAppToken, refreshToken, isRefreshTokenExpired } = authStore;

        if (userAppToken && refreshToken && !isRefreshTokenExpired()) {
          if (isRefreshing) {
            // If already refreshing, queue this request
            return new Promise((resolve, reject) => {
              failedQueue.push({ resolve, reject });
            })
              .then((token) => {
                originalRequest.headers.Authorization = `Bearer ${token}`;
                return axiosInstance(originalRequest);
              })
              .catch((err) => {
                return Promise.reject(err);
              });
          }

          originalRequest._retry = true;
          isRefreshing = true;

          try {
            console.log("🔄 Starting fallback token refresh after 401...");
            // Attempt to refresh the token
            const response = await AuthServices.refreshToken();
            console.log("✅ Fallback refresh response received");
            const {
              access_token,
              refresh_token,
              access_token_expires_at,
              refresh_token_expires_at,
            } = response.data;

            // Update tokens in store with expiry dates
            authStore.setTokens(
              access_token,
              refresh_token,
              access_token_expires_at,
              refresh_token_expires_at
            );

            // Force a small delay to ensure localStorage is updated
            await new Promise((resolve) => setTimeout(resolve, 10));

            // Process queued requests with new token
            processQueue(null, access_token);

            // Retry original request with new token
            originalRequest.headers.Authorization = `Bearer ${access_token}`;
            console.log(
              "✅ Fallback token refresh completed, retrying original request"
            );
            return axiosInstance(originalRequest);
          } catch (refreshError) {
            // Refresh failed, clear auth data and redirect
            console.error("❌ Fallback token refresh failed:", refreshError);
            processQueue(refreshError, null);
            clearAuthData();
            toast.error("Session expired. Please login again.");
            console.error("Token refresh failed, redirecting to login");
            setTimeout(() => (window.location.href = "/login"), 1000);
            return Promise.reject(refreshError);
          } finally {
            isRefreshing = false;
          }
        } else {
          // No tokens available or refresh token expired, clear auth and redirect
          clearAuthData();
          toast.error("Session expired. Please login again.");
          console.error("No valid tokens available, redirecting to login");
          setTimeout(() => (window.location.href = "/login"), 1000);
        }
      }

      return Promise.reject(error);
    }
  );

  return axiosInstance;
};
