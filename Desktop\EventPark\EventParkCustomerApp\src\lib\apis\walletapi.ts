import { EventParkAPI } from "../event-park-api";

// Wallet model based on API response
export interface Wallet {
  available_balance: string;
  created_at: string;
  currency_code: string;
  id: string;
  ledger_balance: string;
  pin_set: boolean;
  status: "active" | "inactive";
  updated_at: string;
  user_id: string;
}

// Error model
export interface ApiError {
  code: string;
  message: string;
}

// Create wallet payload
export interface CreateWalletPayload {
  currency_code: string;
}

// Top up wallet payload
export interface TopUpWalletPayload {
  amount: string;
  channel?: string;
}

// Top up wallet response
export interface TopUpWalletResponse {
  payment_url: string;
  transaction_id?: string;
  amount: string;
}

// Top up wallet response
export interface TopUpWalletResponse {
  payment_url: string;
  transaction_id?: string;
  amount: string;
}

// Set wallet pin payload
export interface SetWalletPinPayload {
  pin: string;
}

// Withdraw wallet payload
export interface WithdrawWalletPayload {
  amount: string;
  payout_account_id: string;
  transaction_pin: string;
}

// Withdrawal response
export interface WithdrawalResponse {
  payment_url?: string;
  transaction: {
    amount: string;
    channel: string;
    completed_at?: string;
    created_at: string;
    currency_code: string;
    description: string;
    fee: string;
    id: string;
    metadata: {
      card_details?: {
        card_bank: string;
        card_last_4: string;
        card_type: string;
      };
      payment_url?: string;
      transfer_details?: {
        narration: string;
        payment_reference: string;
        receiver_account_name: string;
        receiver_account_number: string;
        receiver_bank_code: string;
        sender_account_name: string;
        sender_account_number: string;
        sender_bank_code: string;
      };
    };
  };
}

// Helper type guard for Axios error
function isAxiosError(
  error: unknown
): error is { response: { data: ApiError } } {
  return (
    typeof error === "object" &&
    error !== null &&
    "response" in error &&
    typeof (error as { response?: unknown }).response === "object" &&
    (error as { response?: { data?: unknown } }).response !== undefined &&
    "data" in (error as { response: { data?: unknown } }).response!
  );
}

/**
 * Transaction and payout-related types and API functions
 */

// Transaction meta info
export interface TransactionMeta {
  from: string;
  next_page: boolean;
  page: number;
  page_count: number;
  per_page: number;
  previous_page: boolean;
  to: string;
  total: number;
}

// Card details for transaction metadata
export interface CardDetails {
  card_bank: string;
  card_last_4: string;
  card_type: string;
}

// Transfer details for transaction metadata
export interface TransferDetails {
  narration: string;
  payment_reference: string;
  receiver_account_name: string;
  receiver_account_number: string;
  receiver_bank_code: string;
  sender_account_name: string;
  sender_account_number: string;
  sender_bank_code: string;
}

// Transaction metadata
export interface TransactionMetadata {
  card_details?: CardDetails;
  cash_gift_reservation_id?: string;
  payment_url?: string;
  transfer_details?: TransferDetails;
}

// Transaction model
export interface Transaction {
  amount: string;
  channel: string;
  completed_at: string | null;
  created_at: string;
  currency_code: string;
  description: string;
  fee: string;
  id: string;
  metadata: TransactionMetadata;
  provider?: string;
  reference?: string;
  service_name?: string;
  settled_at?: string | null;
  status: string;
  type?: "credit" | "debit";
  updated_at?: string;
  user_id?: string;
  wallet_id?: string;
}

// Transaction list response
export interface TransactionListResponse {
  meta: TransactionMeta;
  transactions: Transaction[];
}

// Transaction list query params
export interface TransactionListParams {
  from?: string; // ISO date string
  to?: string; // ISO date string
  page?: number;
  per_page?: number;
}

// Transaction fee calculation request
export interface TransactionFeeRequest {
  amount: string;
  channel: string;
  purpose: string;
  wallet_id: string;
}

// Transaction fee calculation response
export interface TransactionFeeResponse {
  amount: string;
  fee: string;
}

// Payout bank model
export interface PayoutBank {
  bank_code: string;
  bank_name: string;
}

// Payout account model
export interface PayoutAccount {
  account_name: string;
  account_number: string;
  bank_code: string;
  created_at: string;
  id: string;
  updated_at: string;
  user_id: string;
}

// Create payout account payload
export interface CreatePayoutAccountPayload {
  account_number: string;
  bank_code: string;
  currency_code: string; // e.g., 'ngn'
}

export const WalletAPI = {
  /**
   * Fetch all user wallets
   */
  async getUserWallets(): Promise<Wallet[]> {
    try {
      const response = await EventParkAPI().get<Wallet[]>("/v1/user/wallets");
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response.data;
      }
      throw {
        code: "unknown_error",
        message: "Failed to fetch wallets",
      };
    }
  },

  /**
   * Create a new wallet
   */
  async createWallet(payload: CreateWalletPayload): Promise<Wallet> {
    try {
      const response = await EventParkAPI().post<Wallet>(
        "/v1/user/wallets",
        payload
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response.data;
      }
      throw {
        code: "unknown_error",
        message: "Failed to create wallet",
      };
    }
  },

  /**
   * Top up wallet
   */
  async topUpWallet(
    walletId: string,
    payload: TopUpWalletPayload
  ): Promise<TopUpWalletResponse> {
    try {
      const response = await EventParkAPI().post<TopUpWalletResponse>(
        `/v1/user/wallets/${walletId}/topup`,
        payload
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response.data;
      }
      throw {
        code: "unknown_error",
        message: "Failed to top up wallet",
      };
    }
  },

  /**
   * Set transaction pin for a wallet
   */
  async setWalletPin(
    walletId: string,
    payload: SetWalletPinPayload
  ): Promise<Wallet> {
    console.log(walletId);
    try {
      const response = await EventParkAPI().post<Wallet>(
        `/v1/user/pins/transaction`,
        payload
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response.data;
      }
      throw {
        code: "unknown_error",
        message: "Failed to set wallet pin",
      };
    }
  },

  /**
   * Fetch user transactions with optional filters
   */
  async getUserTransactions(
    params: TransactionListParams = {}
  ): Promise<TransactionListResponse> {
    try {
      const response = await EventParkAPI().get<TransactionListResponse>(
        "/v1/user/transactions",
        { params }
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response.data;
      }
      throw {
        code: "unknown_error",
        message: "Failed to fetch transactions",
      };
    }
  },

  /**
   * Fetch available payout banks for a currency
   */
  async getPayoutBanks(currency_code: string): Promise<PayoutBank[]> {
    try {
      const response = await EventParkAPI().get<PayoutBank[]>(
        `/v1/user/payout/banks/${currency_code}`
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response.data;
      }
      throw {
        code: "unknown_error",
        message: "Failed to fetch payout banks",
      };
    }
  },

  /**
   * Resolve bank account details
   */
  async resolvePayoutAccount(payload: {
    account_number: string;
    bank_code: string;
    currency_code: string;
  }): Promise<{
    account_name: string;
    account_number: string;
    bank_code: string;
  }> {
    try {
      const response = await EventParkAPI().post<{
        account_name: string;
        account_number: string;
        bank_code: string;
      }>("/v1/user/payout/accounts/resolve", payload);
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response.data;
      }
      throw {
        code: "unknown_error",
        message: "Failed to resolve account details",
      };
    }
  },

  /**
   * Create a new payout account
   */
  async createPayoutAccount(
    payload: CreatePayoutAccountPayload
  ): Promise<PayoutAccount> {
    try {
      const response = await EventParkAPI().post<PayoutAccount>(
        "/v1/user/payout/accounts",
        payload
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response.data;
      }
      throw {
        code: "unknown_error",
        message: "Failed to create payout account",
      };
    }
  },

  /**
   * Initiate wallet withdrawal
   */
  async withdrawFromWallet(
    walletId: string,
    payload: WithdrawWalletPayload
  ): Promise<WithdrawalResponse> {
    try {
      const response = await EventParkAPI().post<WithdrawalResponse>(
        `/v1/user/wallets/${walletId}/withdraw`,
        payload
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response.data;
      }
      throw {
        code: "unknown_error",
        message: "Failed to initiate withdrawal",
      };
    }
  },

  /**
   * Get user payout accounts
   */
  async getPayoutAccounts(): Promise<PayoutAccount[]> {
    try {
      const response = await EventParkAPI().get<PayoutAccount[]>(
        "/v1/user/payout/accounts"
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response.data;
      }
      throw {
        code: "unknown_error",
        message: "Failed to fetch payout accounts",
      };
    }
  },

  /**
   * Fetch authenticated user's transaction by ID
   */
  async getTransactionById(transactionId: string): Promise<Transaction> {
    try {
      const response = await EventParkAPI().get<Transaction>(
        `/v1/user/transactions/${transactionId}`
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response.data;
      }
      throw {
        code: "unknown_error",
        message: "Failed to fetch transaction details",
      };
    }
  },

  /**
   * Calculate transaction fee
   */
  async calculateTransactionFee(
    payload: TransactionFeeRequest
  ): Promise<TransactionFeeResponse> {
    try {
      const response = await EventParkAPI().post<TransactionFeeResponse>(
        "/v1/user/transactions/fee/calculate",
        payload
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response.data;
      }
      throw {
        code: "unknown_error",
        message: "Failed to calculate transaction fee",
      };
    }
  },
};
