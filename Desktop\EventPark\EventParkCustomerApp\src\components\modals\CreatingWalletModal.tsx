/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect } from 'react';
import { CloseCircle } from 'iconsax-react';
import wallet from '../../assets/images/wallet.png';

interface CreateWalletModalProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete?: () => void;
  isLoading: boolean;
  isSuccess: boolean;
  isError: boolean;
  error?: any;
}

export const CreateWalletModal = ({
  isOpen,
  onClose,
  onComplete,
  isLoading,
  isSuccess,
  isError,
  error,
}: CreateWalletModalProps) => {
  const [showSuccessState, setShowSuccessState] = useState(false);
  const getErrorMessage = (error: any): string => {
    if (error?.response?.data?.message) {
      return error.response.data.message;
    }
    if (error?.response?.data?.error) {
      return error.response.data.error;
    }
    if (error?.message) {
      return error.message;
    }
    return 'Something went wrong. Please try again.';
  };
  useEffect(() => {
    if (isOpen) {
      setShowSuccessState(false);
    }
  }, [isOpen]);

  useEffect(() => {
    if (isSuccess && !showSuccessState) {
      const timer = setTimeout(() => {
        setShowSuccessState(true);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [isSuccess, showSuccessState]);

  useEffect(() => {
    if (isError) {
      console.error('Wallet creation failed');
    }
  }, [isError]);

  const handleContinueSetup = () => {
    if (onComplete) {
      onComplete();
    }
  };

  const handleClose = () => {
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="absolute inset-0 bg-black/20 " onClick={handleClose} />

      <div className="relative bg-white rounded-[24px]  w-full max-w-[522px]  mx-4 z-10">
        <button
          onClick={handleClose}
          className={`${
            isError ? 'hidden' : 'absolute top-4 right-4 transition-colors'
          }`}>
          <CloseCircle size={40} color="#4D55F2" variant="Bulk" />
        </button>

        {isLoading || (!isSuccess && !isError) ? (
          <div className="text-center py-14 px-4">
            <div className="mb-10 mt-10">
              <div className="relative w-30 h-30 mx-auto mb-6 ">
                <div className="absolute inset-0 rounded-full border-6 border-grey-350"></div>
                <div
                  className="absolute inset-0 rounded-full border-6 border-transparent border-t-primary-400 animate-spin"
                  style={{
                    animation: 'spin 1s linear infinite',
                  }}></div>
              </div>
            </div>

            <h2 className="text-[28px] font-semibold text-dark-200 mb-2">
              Creating your Wallet
            </h2>
          </div>
        ) : isSuccess && showSuccessState ? (
          <>
            <div className="mb-6 bg-[#F5F6FE] rounded-t-2xl flex justify-center items-center">
              <img src={wallet} alt="wallet" />
            </div>

            <div className="flex flex-col items-center text-center px-4 w-full pb-10">
              <h2 className="text-2xl md:text-[28px] font-medium my-2 text-dark-200">
                Your Wallet has Successfully <br />{' '}
                <span className="text-[18px] md:text-[26px] text-grey-250">
                  been created
                </span>
              </h2>
              <p className="text-grey-250 text-base mb-5 max-w-[372px] w-full">
                You can view your created wallet from your dashboard. Please
                click the button below to continue setting up your gift registry{' '}
              </p>
              <button
                type="button"
                onClick={handleContinueSetup}
                className="bg-primary cursor-pointer text-base text-white flex items-center py-2 px-6 font-semibold rounded-full gap-2 hover:bg-[#4A48E0] transition-colors">
                <span>Continue</span>
              </button>
            </div>
          </>
        ) : isError ? (
          <div className="mb-13">
            <div className="mb-6 bg-gradient-to-t from-[#FFE8CD] from-22.63% to-[#FFFAF5] to-100% rounded-t-2xl py-10 flex justify-center items-center">
              <svg
                width="160"
                height="160"
                viewBox="0 0 160 160"
                fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  opacity="0.4"
                  d="M140.532 57.1989V102.799C140.532 110.265 136.532 117.199 130.065 120.999L90.4653 143.866C83.9987 147.599 75.9985 147.599 69.4652 143.866L29.8652 120.999C23.3985 117.266 19.3984 110.332 19.3984 102.799V57.1989C19.3984 49.7323 23.3985 42.7987 29.8652 38.9987L69.4652 16.132C75.9318 12.3987 83.932 12.3987 90.4653 16.132L130.065 38.9987C136.532 42.7987 140.532 49.6656 140.532 57.1989Z"
                  fill="url(#paint0_linear_13437_50111)"
                />
                <path
                  d="M80 91.668C77.2667 91.668 75 89.4013 75 86.668V51.668C75 48.9346 77.2667 46.668 80 46.668C82.7333 46.668 85 48.9346 85 51.668V86.668C85 89.4013 82.7333 91.668 80 91.668Z"
                  fill="#FF7747"
                />
                <path
                  d="M80.0026 115.002C79.1359 115.002 78.2692 114.802 77.4692 114.468C76.6026 114.135 75.9358 113.668 75.2691 113.068C74.6691 112.402 74.2027 111.668 73.8027 110.868C73.4693 110.068 73.3359 109.202 73.3359 108.335C73.3359 106.602 74.0025 104.868 75.2691 103.602C75.9358 103.002 76.6026 102.535 77.4692 102.202C79.9359 101.135 82.8694 101.735 84.7361 103.602C85.3361 104.268 85.8025 104.935 86.1358 105.802C86.4692 106.602 86.6693 107.468 86.6693 108.335C86.6693 109.202 86.4692 110.068 86.1358 110.868C85.8025 111.668 85.3361 112.402 84.7361 113.068C83.4694 114.335 81.8026 115.002 80.0026 115.002Z"
                  fill="#FF7747"
                />
                <defs>
                  <linearGradient
                    id="paint0_linear_13437_50111"
                    x1="79.9653"
                    y1="13.332"
                    x2="79.9653"
                    y2="146.666"
                    gradientUnits="userSpaceOnUse">
                    <stop stop-color="#FF8C04" />
                    <stop offset="1" stop-color="#FFE8CD" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
            <p className="text-2xl md:text-[28px] font-medium my-2 mx-auto w-full text-dark-200 max-w-[351px] text-center">
              There was an error creating your Wallet
            </p>
            <p className="text-grey-250 text-base my-5 text-center">
              {getErrorMessage(error)}
            </p>
            <button
              type="button"
              onClick={handleClose}
              className="bg-primary cursor-pointer text-base text-white flex items-center py-2 px-6 font-semibold rounded-full gap-2 hover:bg-[#4A48E0] transition-colors mx-auto">
              <span>Close</span>
            </button>
          </div>
        ) : null}
      </div>
    </div>
  );
};
