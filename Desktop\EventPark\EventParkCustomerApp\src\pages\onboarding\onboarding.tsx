import {
  <PERSON>d,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Profile2User,
} from 'iconsax-react';
import logo from '../../assets/icons/Ep-Logo.svg';
import { Button } from '../../components/button/onboardingButton';
import graph from '../../assets/images/graph.png';
import shop from '../../assets/images/shop.png';
import events from '../../assets/images/Vector.png';
import { Link } from 'react-router-dom';

export const Onboarding = () => {
  return (
    <div className="md:px-20 px-5 pt-14 font-rethink bg-[url('/src/assets/images/onboarding-bg.png')]">
      <div className="flex flex-col md:flex-row md:items-center gap-5 md:gap-0 items-start  justify-between ">
        <img src={logo} alt="eventpark-logo" />

        <Link
          to="/"
          className="bg-primary-250 flex items-center py-2 px-3.5 text-xs md:text-sm font-semibold rounded-full gap-2">
          <span> Skip to Dashboard </span>
          <div className="rounded-full bg-primary/40 p-0.5">
            <ArrowRight2 size="12" color="#4D55F2" />
          </div>
        </Link>
      </div>
      <div className="max-w-[640px] mx-auto mt-[28.82px]">
        <h3 className="font-medium text-base md:text-[32px]">
          🎉Hey Emmanuel,
        </h3>
        <p className="text-sm md:text-lg text-grey-100 mb-6">
          You are in!, What would you like to do today!
        </p>

        <div className="bg-primary rounded-[20px] p-8 relative overflow-hidden">
          <div className="relative z-10">
            <span className="text-primary-300 text-sm font-medium tracking-[0.12em]">
              EVENTS
            </span>
            <p className="font-semibold italic text-white text-base md:text-[28px]">
              Let's create your first event!
            </p>
            <p className="text-xs md:text-sm text-primary-400 mb-[37px]">
              Search for vendors in the marketplace
            </p>
            <Button
              variant="primary"
              size="sm"
              iconRight={
                <div className="rounded-full bg-primary p-0.5">
                  <ArrowRight size="14" color="#fff" />
                </div>
              }>
              Create an Event
            </Button>
          </div>
          <div className="absolute top-0 right-0 w-full h-full pointer-events-none ">
            <img
              src={events}
              alt="event-bg"
              className="object-cover absolute top-0 right-0 h-full"
            />
          </div>
        </div>
        <div className="flex flex-col md:flex-row items-center mt-6 mb-[47px] gap-5  justify-between">
          <div className="bg-gradient-to-t from-primary-600 to-primary-700 rounded-[20px] p-8 md:max-w-[310px] w-full h-[220px] relative overflow-hidden">
            <div className="relative z-10 flex flex-col justify-between h-full items-start">
              <div>
                <p className="font-medium text-base md:text-2xl">
                  Plan a Budget
                </p>
                <p className="text-xs md:text-sm text-grey-100 mb-[37px] max-w-[190px]">
                  Got a budget for an event?
                </p>
              </div>
              <Button
                variant="primary"
                size="sm"
                className="text-dark"
                iconRight={
                  <div className="rounded-full bg-primary-800/40 p-0.5">
                    <ArrowRight size="14" color="#000F4A" />
                  </div>
                }>
                Plan Budget
              </Button>
            </div>
            <div className="absolute bottom-0 right-0 pointer-events-none ">
              <img
                src={graph}
                alt="plan-budget"
                className="object-contain max-h-36 "
              />
            </div>
          </div>
          <div className="bg-cus-pink rounded-[20px] px-5 py-7 md:max-w-[310px] w-full h-[220px] relative overflow-hidden">
            <div className="relative z-10 flex flex-col justify-between h-full items-start">
              <div>
                <p className="font-medium text-base md:text-2xl">
                  Browse Vendors
                </p>
                <p className="text-xs md:text-sm text-cus-pink-100  max-w-[190px]">
                  Search for vendors in the marketplace{' '}
                </p>
              </div>
              <Button variant="secondary" size="sm" className="text-black">
                Expore Marketplace{' '}
              </Button>
            </div>
            <div className="absolute bottom-0 right-0 pointer-events-none ">
              <img src={shop} alt="shop" className="object-contain max-h-36 " />
            </div>
          </div>
        </div>
        <div className="py-6 border-t-[0.8px] border-grey-900">
          <p className="text-base tracking-[0.12em] font-medium text-grey-950 mb-6">
            OTHER ACTIONS YOU COULD TRY
          </p>
          <div className="flex flex-col md:flex-row md:items-center gap-3">
            <Button
              variant="primary"
              size="md"
              className="text-black"
              iconLeft={
                <div className="rounded-full bg-primary-550/40">
                  <Add size="16" color="#fff" />
                </div>
              }>
              Plan a Budget
            </Button>
            <Button
              variant="neutral"
              size="md"
              iconLeft={
                <div>
                  <Layer
                    size="20"
                    color="#343CD8"
                    variant="Bulk"
                    className="[&>path:nth-last-child(1)]:fill-black"
                  />{' '}
                </div>
              }>
              Build a Website
            </Button>
            <Button
              variant="primary"
              size="md"
              className="bg-grey-150 "
              iconLeft={
                <div className="">
                  <Profile2User size="20" color="#333333" variant="Bulk" />
                </div>
              }>
              Manage Guest
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
