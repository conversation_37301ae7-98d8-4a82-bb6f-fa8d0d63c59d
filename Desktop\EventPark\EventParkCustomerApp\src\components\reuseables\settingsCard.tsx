import { User } from 'iconsax-react';

interface SettingsCardProps {
  title: string;
  title1?: string;
  initials?: string;
  description?: string;
  buttonText?: string;
  onClick?: () => void;
  children?: React.ReactNode;
  profilePicture?: string;
}

export const SettingsCard: React.FC<SettingsCardProps> = ({
  title,
  title1,
  initials,
  description,
  buttonText,
  onClick,
  children,
  profilePicture,
}) => {
  return (
    <div className="bg-white rounded-2xl p-4 mb-5 shadow-[0px_12px_120px_0px_#5F5F5F0F] ">
      <div className="flex items-start justify-between ">
        <div className="flex-1">
          {(initials || profilePicture) && (
            <div className="flex items-center mb-4">
              <div className="w-16 h-16 bg-gradient-to-br from-[#FEF7F4] from-26.3% to-[#F5F6FE] to-75.01% rounded-full flex items-center justify-center text-dark-300 font-bold text-[28px]">
                {profilePicture ? (
                  <img
                    src={profilePicture}
                    alt="Profile"
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  // initials
                  <User color="#4D55F2" size={32} />
                )}
              </div>
            </div>
          )}
          {title1 && (
            <div className="tracking-[0.12em] text-primary-50 font-medium text-sm">
              {title1}
            </div>
          )}
          <h3 className="text-2xl font-semibold mt-3.5">{title}</h3>
          <p className="text-sm text-grey-960">{description}</p>
        </div>
      </div>

      {children}

      {buttonText && (
        <button
          onClick={onClick}
          className="mt-3.5 px-3.5 py-2 bg-white border border-primary-400 text-primary-650 rounded-full text-sm font-semibold  cursor-pointer ">
          {buttonText}
        </button>
      )}
    </div>
  );
};
