import {
  Tag2,
  <PERSON>ick<PERSON><PERSON><PERSON>,
  ArrowRight2,
  Location,
  Copy,
  CloseCircle,
  ArrowCircleRight,
  ArrowLeft,
} from "iconsax-react";
// import giftIcon from "../../../../assets/images/gift-blur.png";

import { useState, useRef, useEffect } from "react";
import iphone from "../../../../assets/images/iphone.svg";
import cash from "../../../../assets/images/cash-img.png";
import defaultGiftImg from "../../../../assets/images/gift-img.png";
import { Footer } from "../../footer";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import {
  GuestGiftsAPI,
  CashGift,
  ItemGift,
  GiftReservation,
  EventDetailsResponse,
} from "../../../../lib/apis/guestGiftsApi";
import { guestTokenManager } from "../../../../lib/utils/guestTokenManager";
import { toast } from "react-toastify";

export const ViewingGiftAsGuest = () => {
  const { eventId, giftId } = useParams<{ eventId: string; giftId?: string }>();
  console.log(giftId);
  const navigate = useNavigate();
  const location = useLocation();

  // Check if user came from a guest invite path
  const showBackButton = () => {
    // Check if location state indicates they came from guest portal
    const locationState = location.state as {
      fromGuestPortal?: boolean;
    } | null;
    if (locationState?.fromGuestPortal) {
      return true;
    }

    // Check sessionStorage for guest portal navigation flag
    const fromGuestPortal = sessionStorage.getItem("fromGuestPortal");
    if (fromGuestPortal === "true") {
      return true;
    }

    // Check if there's a history entry that suggests they came from guest portal
    // This is a fallback check
    if (window.history.length > 1) {
      // We can't directly access history entries, but we can make an educated guess
      // based on the URL structure and session data
      const guestEventId = sessionStorage.getItem("guestEventId");
      if (guestEventId && guestEventId === eventId) {
        return true;
      }
    }

    return false;
  };

  const [activeTab, setActiveTab] = useState<"items" | "cash">("items");
  const [showDeliveryModal, setShowDeliveryModal] = useState(false);
  const [showReturningGuestModal, setShowReturningGuestModal] = useState(false);
  const [showOtpModal, setShowOtpModal] = useState(false);
  const [returningGuestEmail, setReturningGuestEmail] = useState("");
  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const [emailError, setEmailError] = useState("");
  const [cancellingReservation, setCancellingReservation] = useState<
    string | null
  >(null);
  const [paymentRedirectCountdown, setPaymentRedirectCountdown] = useState<
    number | null
  >(null);

  // API state
  const [cashGifts, setCashGifts] = useState<CashGift[]>([]);
  const [itemGifts, setItemGifts] = useState<ItemGift[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Reservation state
  const [reservations, setReservations] = useState<GiftReservation[]>([]);
  const [showReservations, setShowReservations] = useState(false);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [isInitiatingAccess, setIsInitiatingAccess] = useState(false);
  const [isVerifyingOtp, setIsVerifyingOtp] = useState(false);
  console.log(accessToken);

  const otpRefs = [
    useRef(null),
    useRef(null),
    useRef(null),
    useRef(null),
    useRef(null),
    useRef(null),
  ];

  // Event details state
  const [eventDetails, setEventDetails] = useState<EventDetailsResponse | null>(
    null
  );

  // Copy to clipboard handler
  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("Copied to clipboard");
  };

  // Check for existing guest token on mount
  useEffect(() => {
    const checkExistingToken = async () => {
      const existingToken = await guestTokenManager.getGuestAccessToken();
      if (existingToken) {
        setAccessToken(existingToken);
        // Pre-fetch reservations if we have a valid token but don't show them by default
        fetchReservations("cash");
        // Keep showReservations as false - user should see all gifts by default
      }
    };

    checkExistingToken();

    // Store current event ID for guest session
    if (eventId) {
      guestTokenManager.setGuestEventId(eventId);
    }
  }, [eventId]);

  // Fetch reservations when tab changes (if we have a token)
  useEffect(() => {
    if (accessToken && showReservations) {
      const reservationType = activeTab === "cash" ? "cash" : "item";
      fetchReservations(reservationType);
    }
  }, [activeTab, accessToken, showReservations]);

  // Fetch event details and gifts data
  useEffect(() => {
    if (!eventId) return;

    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        const [eventDetailsResponse, cashResponse, itemResponse] =
          await Promise.all([
            GuestGiftsAPI.getEventDetails(eventId),
            GuestGiftsAPI.getCashGifts(eventId),
            GuestGiftsAPI.getItemGifts(eventId),
          ]);

        setEventDetails(eventDetailsResponse);
        setCashGifts(cashResponse.gifts);
        setItemGifts(itemResponse.gifts);
      } catch (err) {
        const errorMsg =
          typeof err === "object" && err && "message" in err
            ? (err as { message?: string }).message ?? "Failed to fetch data"
            : "Failed to fetch data";
        setError(errorMsg);
        toast.error(errorMsg);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [eventId]);

  // Transform API data to component format
  const giftItems = itemGifts.map((gift) => ({
    id: gift.id,
    name: gift.name,
    price: parseFloat(gift.price),
    image: gift.image_preview_url || iphone,
    reserved: gift.status === "reserved",
    purchased: gift.status === "purchased",
    mostWanted: false, // This would need to be determined by business logic
    description: gift.description,
    type: "standard",
    link: gift.item_link,
    quantity: gift.quantity,
    crowdGiftingEnabled: false, // Add this for now, would need business logic
    contributedAmount: 0, // Add this for now
    contributorCount: 0, // Add this for now
  }));
  const cashItems = cashGifts.map((gift) => ({
    id: gift.id,
    amount: parseFloat(gift.amount),
    description: gift.description,
    received: 0, // This would need to be calculated from reservations
    status: gift.status,
    is_crowd_gift: gift.is_crowd_gift,
    currency: gift.currency,
  }));

  // Email validation
  const validateEmail = (email: string) => {
    return /\S+@\S+\.\S+/.test(email);
  };

  // const formatAmount = (amount: string) => {
  //   if (!amount) return "₦0";
  //   const numAmount = parseInt(amount);
  //   return `₦${new Intl.NumberFormat("en-NG").format(numAmount)}`;
  // };

  // Handle item gift click - navigate to item gift flow
  const handleItemGiftClick = (gift: ItemGift) => {
    // Prevent navigation if gift is fully reserved
    if (isItemGiftFullyReserved(gift)) {
      toast.info("This gift is fully reserved");
      return;
    }

    const eventId = guestTokenManager.getGuestEventId();
    if (eventId) {
      navigate(`/guest/events/${eventId}/gifts/item/${gift.id}/reserve`);
    }
  };

  // Handle Continue in Returning Guest modal
  const handleReturningGuestContinue = async () => {
    if (!validateEmail(returningGuestEmail)) {
      setEmailError("Please enter a valid email address");
      return;
    }

    setEmailError("");
    setIsInitiatingAccess(true);

    try {
      await GuestGiftsAPI.initiateAccess({ email: returningGuestEmail });
      setShowReturningGuestModal(false);
      setShowOtpModal(true);
      toast.success("OTP sent to your email");
    } catch (error) {
      const errorMsg =
        typeof error === "object" && error && "message" in error
          ? (error as { message?: string }).message ?? "Failed to send OTP"
          : "Failed to send OTP";
      setEmailError(errorMsg);
      toast.error(errorMsg);
    } finally {
      setIsInitiatingAccess(false);
    }
  };

  // Handle OTP input
  const handleOtpChange = (idx: number, value: string) => {
    if (!/^[0-9]?$/.test(value)) return;
    const newOtp = [...otp];
    newOtp[idx] = value;
    setOtp(newOtp);
    if (value && idx < 5) {
      (otpRefs[idx + 1].current as unknown as HTMLInputElement)?.focus();
    }
    if (!value && idx > 0) {
      (otpRefs[idx - 1].current as unknown as HTMLInputElement)?.focus();
    }
  };

  // Handle OTP verification
  const handleOtpVerification = async () => {
    const otpCode = otp.join("");
    if (otpCode.length !== 6) {
      toast.error("Please enter the complete OTP");
      return;
    }

    setIsVerifyingOtp(true);
    try {
      const response = await GuestGiftsAPI.verifyOTP({
        email: returningGuestEmail,
        otp: otpCode,
      });

      // Store enhanced guest token data
      guestTokenManager.setEnhancedGuestToken({
        access_token: response.access_token,
        access_token_expires_at: response.access_token_expires_at,
        refresh_token: response.refresh_token,
        refresh_token_expires_at: response.refresh_token_expires_at,
        guest_data: {
          email: returningGuestEmail,
          first_name: "",
          last_name: "",
          id: "",
          phone_number: "",
          created_at: new Date().toISOString(),
        },
        event_id: eventId,
        expires_at: response.expires_at, // For backward compatibility
      });

      setAccessToken(response.access_token);
      setShowOtpModal(false);
      toast.success("Access verified successfully!");

      // Fetch reservations (fetch all initially, then filter by active tab)
      await fetchReservations("cash");
      setShowReservations(true);
    } catch (error) {
      const errorMsg =
        typeof error === "object" && error && "message" in error
          ? (error as { message?: string }).message ?? "Invalid OTP"
          : "Invalid OTP";
      toast.error(errorMsg);
    } finally {
      setIsVerifyingOtp(false);
    }
  };

  // Fetch reservations with optional type filtering
  const fetchReservations = async (type?: string) => {
    try {
      const params = type ? { type } : {};
      const response = await GuestGiftsAPI.getReservations(params);
      setReservations(response.reservations);
    } catch (error) {
      // Check if it's a token expiration error
      if (
        typeof error === "object" &&
        error &&
        "code" in error &&
        (error as { code?: string }).code === "invalid_or_expired_token"
      ) {
        // Clear expired token and reset state
        guestTokenManager.clearGuestToken();
        setAccessToken(null);
        setShowReservations(false);
        toast.error("Session expired. Please verify your access again.");
        return;
      }

      const errorMsg =
        typeof error === "object" && error && "message" in error
          ? (error as { message?: string }).message ??
            "Failed to fetch reservations"
          : "Failed to fetch reservations";
      toast.error(errorMsg);
    }
  };

  // Cancel reservation
  const handleCancelReservation = async (reservationId: string) => {
    setCancellingReservation(reservationId);
    try {
      const accessToken = await guestTokenManager.getGuestAccessToken();
      if (!accessToken) {
        toast.error("No access token found");
        return;
      }

      await GuestGiftsAPI.cancelReservation(reservationId);
      toast.success("Reservation cancelled successfully!");

      // Refresh reservations list
      await fetchReservations(activeTab === "cash" ? "cash" : "item");
    } catch (error) {
      const errorMsg =
        typeof error === "object" && error && "message" in error
          ? (error as { message?: string }).message ??
            "Failed to cancel reservation"
          : "Failed to cancel reservation";
      toast.error(errorMsg);
    } finally {
      setCancellingReservation(null);
    }
  };

  // Make payment for reservation
  const handleMakePayment = async (reservationId: string) => {
    // Find the reservation to determine its type
    const reservation = reservations.find((r) => r.id === reservationId);
    if (!reservation) {
      toast.error("Reservation not found");
      return;
    }

    // Handle different payment flows based on gift type
    if (reservation.gift_type === "cash") {
      // Cash gift payment flow - use payment gateway
      try {
        const accessToken = await guestTokenManager.getGuestAccessToken();
        if (!accessToken) {
          toast.error("No access token found");
          return;
        }

        // Store reservation ID for payment completion tracking
        guestTokenManager.setGuestReservationId(reservationId);

        // Initiate payment
        const response = await GuestGiftsAPI.initiatePayment(reservationId, {
          channel: "web_checkout",
        });

        // Start countdown
        setPaymentRedirectCountdown(5);
        toast.success("Redirecting to payment gateway in 5 seconds...");

        // Countdown timer
        const countdownInterval = setInterval(() => {
          setPaymentRedirectCountdown((prev) => {
            if (prev === null || prev <= 1) {
              clearInterval(countdownInterval);
              window.location.href = response.payment_url;
              return null;
            }
            return prev - 1;
          });
        }, 1000);
      } catch (error) {
        const errorMsg =
          typeof error === "object" && error && "message" in error
            ? (error as { message?: string }).message ??
              "Failed to initiate payment"
            : "Failed to initiate payment";
        toast.error(errorMsg);
      }
    } else if (reservation.gift_type === "item") {
      // Item gift payment flow - redirect to Jumia purchase flow
      const giftId = reservation.gift_id;
      if (eventId && giftId) {
        // Store reservation ID for tracking
        guestTokenManager.setGuestReservationId(reservationId);

        // Navigate to the item gift reserve flow with existing reservation parameter
        navigate(
          `/guest/events/${eventId}/gifts/item/${giftId}/reserve?existing_reservation=${reservationId}`
        );
      } else {
        toast.error("Missing event or gift information");
      }
    } else {
      toast.error("Unknown gift type");
    }
  };

  // Re-reserve a cancelled gift
  const handleReReserveGift = (reservation: GiftReservation) => {
    if (reservation.gift_type === "cash") {
      // Find the original cash gift
      const originalGift = cashGifts.find(
        (gift) => gift.id === reservation.gift_id
      );
      if (originalGift) {
        handleCashGiftClick(originalGift);
      } else {
        toast.error("Original gift not found");
      }
    } else {
      // For item gifts, find the original item gift and navigate to the item gift flow
      const originalGift = itemGifts.find(
        (gift) => gift.id === reservation.gift_id
      );
      if (originalGift) {
        handleItemGiftClick(originalGift);
      } else {
        toast.error("Original gift not found");
      }
      setShowReservations(false);
    }
  };

  // Get full gift details for a reservation
  const getGiftDetails = (reservation: GiftReservation) => {
    if (reservation.gift_type === "cash") {
      const cashGift = cashGifts.find(
        (gift) => gift.id === reservation.gift_id
      );
      return {
        name: cashGift ? `Cash Gift - ${cashGift.description}` : "Cash Gift",
        description: cashGift?.description || "Cash contribution",
        image: cash,
        link: null,
        price: parseFloat(reservation.amount),
      };
    } else {
      const itemGift = itemGifts.find(
        (gift) => gift.id === reservation.gift_id
      );
      return {
        name: itemGift?.name || "Gift Item",
        description: itemGift?.description || "Gift item",
        image: itemGift?.image_preview_url || "/placeholder-gift.png",
        link: itemGift?.item_link || null,
        price: parseFloat(itemGift?.price || "0"),
      };
    }
  };

  // Handle Confirm OTP
  const handleConfirmOtp = () => {
    handleOtpVerification();
  };

  // Check if item gift is fully reserved
  const isItemGiftFullyReserved = (item: ItemGift) => {
    return item.total_reservations >= item.quantity;
  };

  // Check if cash gift is fully reserved (for non-crowd gifts)
  const isCashGiftFullyReserved = (cashGift: CashGift) => {
    return !cashGift.is_crowd_gift && cashGift.total_reservations > 0;
  };

  // Handle cash gift click
  const handleCashGiftClick = (cashGift: CashGift) => {
    if (!eventId) return;

    // Prevent navigation if gift is fully reserved
    if (isCashGiftFullyReserved(cashGift)) {
      toast.info("This gift has already been reserved");
      return;
    }

    const crowdfundingParam = cashGift.is_crowd_gift ? "/crowdfunding" : "";
    navigate(
      `/guest/events/${eventId}/gifts/cash/${cashGift.id}/reserve${crowdfundingParam}`
    );
  };

  return (
    <div className="flex  min-h-screen flex-col bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] relative">
      <div
        className="absolute inset-0 bg-[url('/src/assets/images/blur-bg.png')] bg-no-repeat bg-center bg-cover z-[0]"
        style={{
          backgroundSize: "100% auto",
        }}
      />

      <header className="w-full h-20 border-b border-[#f5f6fe] relative z-[1">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="flex items-center h-20">
            <div
              onClick={() => {
                window.location.href = `${window.location.origin}/login`;
              }}
              className="flex items-center cursor-pointer gap-1"
            >
              <img className="w-6 h-6" alt="Vector" src="/vector.svg" />
              <div className="font-bold text-xl text-center tracking-[-0.40px] leading-5 whitespace-nowrap">
                <span className="text-[#000073] tracking-[-0.08px]">
                  EventPark Africa
                </span>
                <span className="text-[#ff6630] tracking-[-0.08px]">.</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="mx-auto w-full z-20 px-4 sm:px-0">
        {/* Tabs and View Delivery Details Button */}

        {/* Modal for Delivery Details */}
        {showDeliveryModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
            <div
              className="relative bg-white rounded-[16px] shadow-2xl p-8 w-full max-w-[95%] md:max-w-[480px]"
              style={{ fontFamily: "Rethink Sans" }}
            >
              {/* Close Button */}
              <button
                className="absolute top-4 right-4 bg-[#EDEEFE] rounded-full p-2 hover:bg-[#d6d8f5] transition-colors"
                onClick={() => setShowDeliveryModal(false)}
                aria-label="Close"
              >
                <CloseCircle size={24} color="#4D55F2" variant="Bulk" />
              </button>
              {/* Title & Subtitle */}
              <div className="mb-4">
                <h2 className="font-bold text-[22px] text-[#090909] mb-1">
                  Delivery Details
                </h2>
                <p className="font-normal text-[16px] text-[#8E8E93]">
                  Easily copy the delivery details to your clipboard in one tap
                </p>
              </div>
              {/* Phone Number Field - Only show if contact name is available */}
              {eventDetails?.delivery_phone_number && (
                <div className="mb-4">
                  <label className="block font-bold text-[14px] text-[#414651] mb-1">
                    Delivery Contact
                  </label>
                  <div className="flex items-center bg-white border border-[#D5D7DA] rounded-[63px] px-4 py-2">
                    <span className="text-[16px] font-bold text-black flex-1">
                      {eventDetails.delivery_phone_number}
                    </span>
                    <button
                      className="flex items-center gap-1 bg-[#F5F9FF] text-[#5F66F3] font-semibold text-[14px] rounded-[16px] px-3 py-1 ml-2 hover:bg-[#e0e7ff] transition-colors"
                      onClick={() =>
                        handleCopy(eventDetails.delivery_phone_number || "")
                      }
                    >
                      <Copy size={16} color="#4D55F2" variant="Bulk" /> Copy
                    </button>
                  </div>
                </div>
              )}
              {/* Address Field */}
              <div className="mb-2">
                <label className="block font-bold text-[14px] text-[#414651] mb-1">
                  Full Address
                </label>
                {eventDetails?.delivery_address ? (
                  <div className="flex items-center bg-white border border-[#D5D7DA] rounded-[64px] px-4 py-2">
                    <Location
                      size={20}
                      color="#292D32"
                      variant="Bulk"
                      className="mr-2"
                    />
                    <span className="text-[16px] font-bold text-black flex-1">
                      {eventDetails.delivery_address}
                    </span>
                    <button
                      className="flex items-center gap-1 bg-[#F5F9FF] text-[#5F66F3] font-semibold text-[14px] rounded-[16px] px-3 py-1 ml-2 hover:bg-[#e0e7ff] transition-colors"
                      onClick={() =>
                        handleCopy(eventDetails.delivery_address || "")
                      }
                    >
                      <Copy size={16} color="#4D55F2" variant="Bulk" /> Copy
                    </button>
                  </div>
                ) : (
                  <div className="flex items-center bg-gray-50 border border-[#D5D7DA] rounded-[64px] px-4 py-2">
                    <Location
                      size={20}
                      color="#8E8E93"
                      variant="Bulk"
                      className="mr-2"
                    />
                    <span className="text-[16px] text-[#8E8E93] flex-1">
                      No delivery address available
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Returning Guest Email Modal */}
        {showReturningGuestModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
            <div
              className="relative bg-white rounded-[16px] shadow-2xl p-8 w-full max-w-[95%] md:max-w-[480px]"
              style={{ fontFamily: "Rethink Sans" }}
            >
              {/* Close Button */}
              <button
                className="absolute top-4 right-4  rounded-full p-2  transition-colors"
                onClick={() => setShowReturningGuestModal(false)}
                aria-label="Close"
              >
                <CloseCircle size={24} color="#4D55F2" variant="Bulk" />
              </button>
              <div className="mb-6 mt-2">
                <div className="text-[28px] font-bold text-[#000] mb-1">
                  Enter your Email
                </div>
                <div className="text-[#8E8E93] text-[16px] font-normal mb-2">
                  Please enter your email address to continue
                </div>

                <label className="block font-medium text-[14px] text-[#414651] mb-1 mt-7">
                  Email Address
                </label>
                <input
                  type="email"
                  className="w-full border border-[#D5D7DA] rounded-[64px] px-4 py-3 text-[16px] font-normal text-black focus:outline-none focus:border-[#4D55F2] mb-2"
                  placeholder="<EMAIL>"
                  value={returningGuestEmail}
                  onChange={(e) => setReturningGuestEmail(e.target.value)}
                  style={{ fontFamily: "Rethink Sans" }}
                />
                {emailError && (
                  <div className="text-red-500 text-xs mb-2">{emailError}</div>
                )}
                <button
                  className=" w-full mt-10 flex items-center justify-center gap-2 bg-[#4D55F2]  text-white font-bold text-[16px] rounded-[38px] px-4 py-3 shadow-sm hover:bg-[#3a5a8c] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{ fontFamily: "Rethink Sans" }}
                  onClick={handleReturningGuestContinue}
                  disabled={isInitiatingAccess}
                >
                  {isInitiatingAccess ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      Sending OTP...
                    </>
                  ) : (
                    <>
                      Continue
                      <ArrowRight2 size={20} color="#fff" variant="Bulk" />
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* OTP Modal */}
        {showOtpModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
            <div
              className="relative bg-white rounded-[16px] shadow-2xl p-8 w-full max-w-[95%] md:max-w-[480px]"
              style={{ fontFamily: "Rethink Sans" }}
            >
              {/* Close Button */}
              <button
                className="absolute top-4 right-4 bg-[#EDEEFE] rounded-full p-2 hover:bg-[#d6d8f5] transition-colors"
                onClick={() => setShowOtpModal(false)}
                aria-label="Close"
              >
                <CloseCircle size={24} color="#4D55F2" variant="Bulk" />
              </button>
              <div className="mb-6 mt-2">
                <div className="text-[28px] font-bold text-[#000] mb-2">
                  Enter OTP
                </div>
                <div className="text-[#8E8E93] text-[16px] font-normal mb-14">
                  Check your mail for a 6-Digit OTP sent to{" "}
                  <span className="font-bold text-black">
                    {returningGuestEmail}
                  </span>{" "}
                  and input in the field below
                </div>

                <div className="flex gap-3 justify-center mb-14">
                  {otp.map((digit, idx) => (
                    <input
                      key={idx}
                      ref={otpRefs[idx]}
                      type="text"
                      inputMode="numeric"
                      maxLength={1}
                      className={`w-14 h-14 text-center text-[48px] font-medium rounded-[50%] border ${
                        digit
                          ? "border-[#4D55F2] shadow-[0px_0px_10px_0px_rgba(77,85,242,0.25)]"
                          : "  border-[#DBDDFC]"
                      } focus:border-[#4D55F2] focus:outline-none`}
                      value={digit}
                      onChange={(e) => handleOtpChange(idx, e.target.value)}
                      onFocus={(e) => e.target.select()}
                      style={{
                        fontFamily: "Rethink Sans",
                        color: digit ? "#00000D" : "#4D55F2",
                      }}
                    />
                  ))}
                </div>
                <button
                  className="mt-10 w-full flex items-center justify-center gap-2 bg-[#4D55F2] text-white font-bold text-[16px] rounded-[40px] px-4 py-3 shadow-sm hover:bg-[#3a5a8c] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{ fontFamily: "Rethink Sans" }}
                  onClick={handleConfirmOtp}
                  disabled={isVerifyingOtp}
                >
                  {isVerifyingOtp ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      Verifying...
                    </>
                  ) : (
                    <>
                      Confirm OTP
                      <ArrowRight2 size={20} color="#fff" variant="Bulk" />
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="text-center">
          <div className="flex justify-center mb-4 sm:mb-6">
            <img
              src="/gift-guest.png"
              alt="Gift"
              className="w-12 h-12 sm:w-[88px] sm:h-[88px]"
            />
            <div className="relative">
              <div className="relative flex justify-center xl:left-[-400px]  left-[-150px] sm:left-[-250px]">
                {showBackButton() && (
                  <button
                    onClick={() => navigate(-1)}
                    className="absolute min-w-max top-1 sm:top-8 left-0  flex items-center gap-2 text-primary font-medium sm:text-base text-sm focus:outline-none z-10 bg-white rounded-full sm:px-4 px-2 sm:py-[10px] py-[8px] shadow"
                    aria-label="Back to Guest Portal"
                    style={{
                      boxShadow: "0px 1px 2px 0px rgba(10, 13, 18, 0.05)",
                    }}
                  >
                    <svg
                      className="w-3 h-3 md:w-auto md:h-auto"
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        opacity="0.4"
                        d="M9.9974 18.3346C14.5998 18.3346 18.3307 14.6037 18.3307 10.0013C18.3307 5.39893 14.5998 1.66797 9.9974 1.66797C5.39502 1.66797 1.66406 5.39893 1.66406 10.0013C1.66406 14.6037 5.39502 18.3346 9.9974 18.3346Z"
                        fill="#4D55F2"
                      />
                      <path
                        d="M12.9172 9.37682H8.59219L10.0255 7.94349C10.2672 7.70182 10.2672 7.30182 10.0255 7.06016C9.78385 6.81849 9.38385 6.81849 9.14219 7.06016L6.64219 9.56016C6.40052 9.80182 6.40052 10.2018 6.64219 10.4435L9.14219 12.9435C9.26719 13.0685 9.42552 13.1268 9.58385 13.1268C9.74219 13.1268 9.90052 13.0685 10.0255 12.9435C10.2672 12.7018 10.2672 12.3018 10.0255 12.0602L8.59219 10.6268H12.9172C13.2589 10.6268 13.5422 10.3435 13.5422 10.0018C13.5422 9.66016 13.2589 9.37682 12.9172 9.37682Z"
                        fill="#4D55F2"
                      />
                    </svg>

                    <span>Back</span>
                  </button>
                )}
              </div>
            </div>
          </div>

          <div className="text-sm sm:text-base uppercase text-[#333333] tracking-widest mb-2 sm:mb-3 px-2">
            CURATED BY{" "}
            {eventDetails
              ? `${eventDetails.host_first_name} ${eventDetails.host_last_name}`.toUpperCase()
              : "LOADING..."}
          </div>

          <h1 className="text-2xl sm:text-3xl md:text-[48px] font-bold text-[#000059] mb-2 sm:mb-3 px-2 leading-tight sm:leading-normal">
            {eventDetails ? eventDetails.gift_registry_title : "Loading..."}
          </h1>

          <p className="text-[#666666] leading-[150%] text-base sm:text-lg mb-3 px-4 sm:px-0">
            Celebrate with me! Pick a gift and{" "}
            <br className="hidden sm:block" />
            make my day special. 💙{" "}
          </p>

          {/* <Button
            variant="primary"
            size="sm"
            iconLeft={<InfoCircle size="14" color="#FF6630" variant="Bulk" />}
            className="bg-[#FDEFE9] text-cus-orange mx-auto underline italic font-bold text-sm sm:text-base"
          >
            How to Get Gift
          </Button> */}

          {/* Guest Action Bar */}
          {!guestTokenManager.hasValidGuestToken() ? (
            /* Returning Guest Bar - Show if no valid token */
            <div className="flex items-center mx-auto w-fit mt-10 sm:mt-10 justify-between bg-[#EBF3FE] rounded-[12px] sm:px-4 px-2 py-2  mb-6">
              <span className="font-bold text-[10px] whitespace-nowrap  sm:text-[16px] text-[#322D66] italic font-[Rethink Sans]">
                Are you a Returning Guest? Click here to continue
              </span>
              <button
                className="flex items-center gap-1 ml-1 sm:ml-5 bg-[#5075AF] text-white font-semibold text-[10px] sm:text-xs rounded-full italic px-2 sm:px-4 py-1.5 shadow-sm hover:bg-[#3a5a8c] transition-colors"
                style={{ fontFamily: "Rethink Sans" }}
                onClick={() => setShowReturningGuestModal(true)}
              >
                Continue
                <svg
                  className="w-3 h-3 md:w-auto md:h-auto"
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    opacity="0.4"
                    d="M9.9974 18.3346C14.5998 18.3346 18.3307 14.6037 18.3307 10.0013C18.3307 5.39893 14.5998 1.66797 9.9974 1.66797C5.39502 1.66797 1.66406 5.39893 1.66406 10.0013C1.66406 14.6037 5.39502 18.3346 9.9974 18.3346Z"
                    fill="white"
                  />
                  <path
                    d="M13.3609 9.56016L10.8609 7.06016C10.6193 6.81849 10.2193 6.81849 9.9776 7.06016C9.73594 7.30182 9.73594 7.70182 9.9776 7.94349L11.4109 9.37682H7.08594C6.74427 9.37682 6.46094 9.66016 6.46094 10.0018C6.46094 10.3435 6.74427 10.6268 7.08594 10.6268H11.4109L9.9776 12.0602C9.73594 12.3018 9.73594 12.7018 9.9776 12.9435C10.1026 13.0685 10.2609 13.1268 10.4193 13.1268C10.5776 13.1268 10.7359 13.0685 10.8609 12.9435L13.3609 10.4435C13.6026 10.2018 13.6026 9.80182 13.3609 9.56016Z"
                    fill="white"
                  />
                </svg>
              </button>
            </div>
          ) : (
            /* My Reservations Bar - Show if valid token exists */
            <div className="flex items-center mx-auto w-fit mt-10 sm:mt-10 justify-between bg-[#E8F5E8] rounded-[12px] px-4 py-2  mb-6">
              <span className="font-bold text-[10px] whitespace-nowrap sm:text-[16px] text-[#2D5016] italic font-[Rethink Sans]">
                View your gift reservations and payment history
              </span>
              <button
                className="flex items-center gap-1  ml-1 sm:ml-5 bg-[#4CAF50] text-white font-semibold text-xs rounded-full italic px-1 sm:px-4 py-1.5 shadow-sm hover:bg-[#45a049] transition-colors"
                style={{ fontFamily: "Rethink Sans" }}
                onClick={() => setShowReservations(true)}
              >
                <span className="text-[10px] sm:text-sm whitespace-nowrap">
                  {" "}
                  My Reservations
                </span>

                <svg
                  className="w-3 h-3 md:w-auto md:h-auto"
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    opacity="0.4"
                    d="M9.9974 18.3346C14.5998 18.3346 18.3307 14.6037 18.3307 10.0013C18.3307 5.39893 14.5998 1.66797 9.9974 1.66797C5.39502 1.66797 1.66406 5.39893 1.66406 10.0013C1.66406 14.6037 5.39502 18.3346 9.9974 18.3346Z"
                    fill="white"
                  />
                  <path
                    d="M13.3609 9.56016L10.8609 7.06016C10.6193 6.81849 10.2193 6.81849 9.9776 7.06016C9.73594 7.30182 9.73594 7.70182 9.9776 7.94349L11.4109 9.37682H7.08594C6.74427 9.37682 6.46094 9.66016 6.46094 10.0018C6.46094 10.3435 6.74427 10.6268 7.08594 10.6268H11.4109L9.9776 12.0602C9.73594 12.3018 9.73594 12.7018 9.9776 12.9435C10.1026 13.0685 10.2609 13.1268 10.4193 13.1268C10.5776 13.1268 10.7359 13.0685 10.8609 12.9435L13.3609 10.4435C13.6026 10.2018 13.6026 9.80182 13.3609 9.56016Z"
                    fill="white"
                  />
                </svg>
              </button>
            </div>
          )}

          <div className="w-full md:w-[560px] mx-auto justify-between items-center  flex">
            <div className="flex flex-col gap-4 md:gap-0 items-start md:flex-row  w-full justify-between md:w-[560px]">
              <div className="relative flex justify-center ">
                <div className="flex w-fit">
                  <button
                    className={`px-4 sm:px-5 py-2.5 sm:py-2 rounded-full text-sm font-medium transition-colors min-w-[100px] sm:min-w-0 ${
                      activeTab === "items"
                        ? "bg-[#4D55F2] text-white"
                        : "text-gray-500"
                    }`}
                    onClick={() => setActiveTab("items")}
                  >
                    Gift Items
                  </button>
                  <button
                    className={`px-4 sm:px-5 py-2.5 sm:py-2 rounded-full text-sm font-medium transition-colors min-w-[100px] sm:min-w-0 ${
                      activeTab === "cash"
                        ? "bg-[#4D55F2] text-white"
                        : "text-gray-500"
                    }`}
                    onClick={() => setActiveTab("cash")}
                  >
                    Cashgifts
                  </button>
                </div>

                <div
                  className={`absolute bottom-[-14px] w-1.5 h-1.5 bg-[#FF6630] rounded-full transition-all duration-300 ${
                    activeTab === "items"
                      ? "left-[calc(50%-50px)]"
                      : "left-[calc(50%+50px)]"
                  }`}
                />
              </div>
              <button
                className="flex items-center gap-2 bg-[#F5F6FE] border border-[#F0F0F0] rounded-full px-4 py-2 h-[40px] text-[#343CD8] font-medium text-xs  hover:bg-[#edeefe] transition-colors"
                style={{ fontFamily: "Rethink Sans" }}
                onClick={() => setShowDeliveryModal(true)}
              >
                View Delivery Details
                <ArrowCircleRight size={20} color="#343CD8" variant="Bulk" />
              </button>
            </div>
          </div>

          {showReservations ? (
            /* Reservations View */
            <div className="mt-8 sm:mt-10 mb-16 sm:mb-20 max-w-[800px] w-full mx-auto">
              {/* Back Button */}
              <button
                onClick={() => setShowReservations(false)}
                className="flex items-center gap-2 mb-6 text-[#4D55F2] hover:text-[#3a4bd8] transition-colors"
              >
                <ArrowLeft size={20} />
                <span className="font-medium">Back to All Gifts</span>
              </button>

              {/* Reservations Header */}
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-[#000] mb-2">
                  My Reservations
                </h2>
                <p className="text-gray-600">
                  View and manage your gift reservations
                </p>
              </div>

              {/* Reservations List */}
              <div className="space-y-3 sm:space-y-4">
                {reservations.length > 0 ? (
                  reservations.map((reservation) => {
                    // Get full gift details
                    const giftDetails = getGiftDetails(reservation);

                    // Determine payment status and button text
                    const hasNoPayment = !reservation.collection_transaction_id;
                    const isPending =
                      reservation.collection_transaction_id &&
                      reservation.status === "pending";
                    const isCompleted =
                      reservation.status === "confirmed" ||
                      reservation.status === "completed";
                    const isCancelled = reservation.status === "cancelled";

                    return (
                      <div
                        key={reservation.id}
                        className="bg-white mx-0 sm:mx-4 md:mx-0 rounded-xl min-h-[280px] sm:h-[280px] flex flex-col sm:flex-row items-stretch border border-[#F0F0F0] shadow-[0px_12px_120px_0px_rgba(95,95,95,0.06)] relative"
                      >
                        {/* Status indicator */}
                        <div
                          className={`absolute top-2 right-2 text-xs font-bold px-2 py-1 rounded-full flex items-center z-10 ${
                            isCancelled ? "bg-red-100 text-red-700" : ""
                          }`}
                        >
                          {isCancelled ? "❌ CANCELLED" : ""}
                        </div>

                        {/* Gift Image */}
                        <div className="w-full h-[120px] sm:w-[80px] md:w-[175px] sm:h-[180px] md:h-auto md:min-h-full bg-gray-200 rounded-t-xl sm:rounded-t-none sm:rounded-l-lg overflow-hidden flex-shrink-0">
                          <img
                            src={giftDetails.image}
                            alt={giftDetails.name}
                            className="w-full h-full object-cover"
                          />
                        </div>

                        {/* Gift Details */}
                        <div className="flex-1 text-center sm:text-left p-3 sm:p-4 md:p-5">
                          {/* Gift Name and Description */}
                          <div className="mb-3">
                            <h3 className="text-xl font-semibold text-gray-900 mb-1">
                              {giftDetails.name}
                            </h3>
                            <p className="text-xs text-gray-600 line-clamp-2">
                              {giftDetails.description}
                            </p>
                            {giftDetails.link && (
                              <a
                                href={giftDetails.link}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-xs text-blue-600 hover:text-blue-800 underline mt-1 inline-block"
                              >
                                View Item Link
                              </a>
                            )}
                          </div>
                          <div className="flex flex-col sm:flex-row gap-2 items-center mb-3 sm:mb-4">
                            <div className="flex items-center gap-2 bg-light-blue-150 text-orange-700 px-3 py-2 sm:px-2.5 sm:py-1.5 rounded-full">
                              <Tag2 size={12} variant="Bulk" color="#5925DC" />
                              <span className="text-primary text-sm font-semibold">
                                ₦
                                {parseFloat(
                                  String(giftDetails.price)
                                ).toLocaleString()}
                              </span>
                            </div>
                            <span className="text-[#B54708] bg-[#FFFAEB] text-sm font-medium px-2 py-1 rounded-full">
                              Reserved by you
                            </span>
                            {reservation.order_number && (
                              <div className="text-xs text-gray-500">
                                Order: {reservation.order_number}
                              </div>
                            )}
                          </div>

                          <div className="text-xs text-gray-500 mb-4">
                            Reserved:{" "}
                            {new Date(
                              reservation.created_at
                            ).toLocaleDateString()}
                            {reservation.expires_at && (
                              <span className="block">
                                Expires:{" "}
                                {new Date(
                                  reservation.expires_at
                                ).toLocaleDateString()}
                              </span>
                            )}
                          </div>

                          {/* Action Buttons */}
                          <div className="flex flex-col gap-2 sm:justify-end sm:flex-row sm:gap-3">
                            {isCancelled ? (
                              <button
                                onClick={() => handleReReserveGift(reservation)}
                                className="bg-[#4D55F2] text-white px-4 py-2.5 rounded-full text-sm font-medium hover:bg-[#3a4bd8] transition-colors w-full sm:w-auto"
                              >
                                Reserve Again
                              </button>
                            ) : isCompleted ? (
                              <div className="flex items-center gap-2">
                                <TickCircle
                                  size={16}
                                  color="#10B981"
                                  variant="Bulk"
                                />
                                <span className="text-green-600 font-medium text-sm">
                                  Payment Completed
                                </span>
                              </div>
                            ) : hasNoPayment ? (
                              <>
                                <button
                                  onClick={() =>
                                    handleCancelReservation(reservation.id)
                                  }
                                  disabled={
                                    cancellingReservation === reservation.id
                                  }
                                  className="bg-[#FFF5F5] text-[#FF9999] shadow-xs  px-4 py-2.5 rounded-full text-sm font-medium italic hover:bg-red-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed w-full sm:w-auto"
                                >
                                  {cancellingReservation === reservation.id
                                    ? "Cancelling..."
                                    : "Cancel Reservation"}
                                </button>
                                <button
                                  onClick={() =>
                                    handleMakePayment(reservation.id)
                                  }
                                  disabled={paymentRedirectCountdown !== null}
                                  className="bg-[#4D55F2] text-white px-4 py-2.5 rounded-full text-sm font-medium italic hover:bg-[#3a4bd8] transition-colors disabled:opacity-50 disabled:cursor-not-allowed shadow-sm w-full sm:w-auto"
                                >
                                  {paymentRedirectCountdown !== null
                                    ? `Redirecting in ${paymentRedirectCountdown}s`
                                    : "Make Payment"}
                                </button>
                              </>
                            ) : isPending ? (
                              <>
                                <button
                                  onClick={() =>
                                    handleCancelReservation(reservation.id)
                                  }
                                  disabled={
                                    cancellingReservation === reservation.id
                                  }
                                  className="bg-[#FFF5F5] text-red-600 border border-red-200 px-4 py-2.5 rounded-full text-sm font-medium italic hover:bg-red-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed w-full sm:w-auto"
                                >
                                  {cancellingReservation === reservation.id
                                    ? "Cancelling..."
                                    : "Cancel Reservation"}
                                </button>
                                <button
                                  onClick={() =>
                                    handleMakePayment(reservation.id)
                                  }
                                  disabled={paymentRedirectCountdown !== null}
                                  className="bg-[#4D55F2] text-white px-4 py-2.5 rounded-full text-sm font-medium hover:bg-[#3a4bd8] transition-colors disabled:opacity-50 disabled:cursor-not-allowed w-full sm:w-auto"
                                >
                                  {paymentRedirectCountdown !== null
                                    ? `Redirecting in ${paymentRedirectCountdown}s`
                                    : "Complete Payment"}
                                </button>
                              </>
                            ) : (
                              <div className="flex items-center gap-2">
                                <span
                                  className={`px-3 py-1 rounded-full text-xs font-medium ${
                                    reservation.status === "confirmed"
                                      ? "bg-green-100 text-green-800"
                                      : reservation.status === "pending"
                                      ? "bg-yellow-100 text-yellow-800"
                                      : "bg-gray-100 text-gray-800"
                                  }`}
                                >
                                  {reservation.status}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className="text-center py-12">
                    <p className="text-gray-500 mb-4">No reservations found</p>
                    <button
                      onClick={() => setShowReservations(false)}
                      className="text-[#4D55F2] hover:text-[#3a4bd8] font-medium"
                    >
                      Browse Gifts
                    </button>
                  </div>
                )}
              </div>
            </div>
          ) : (
            /* Main Gift List View */
            <div className="mt-8 sm:mt-10 mb-16 sm:mb-20 max-w-[560px] w-full mx-auto">
              {loading ? (
                <div className="flex flex-col items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#4D55F2]"></div>
                  <p className="mt-4 text-gray-600">Loading gifts...</p>
                </div>
              ) : error ? (
                <div className="flex flex-col items-center justify-center py-12">
                  <div className="text-red-500 text-center">
                    <p className="text-lg font-medium mb-2">
                      Error loading gifts
                    </p>
                    <p className="text-sm">{error}</p>
                  </div>
                </div>
              ) : activeTab === "items" ? (
                <div>
                  <div className="space-y-3 sm:space-y-4">
                    {giftItems.length === 0 ? (
                      <div className="text-center py-12">
                        <p className="text-gray-600">No gift items available</p>
                      </div>
                    ) : (
                      itemGifts
                        .sort((a, b) => {
                          // Hidden items (disabled) come last
                          if (
                            a.status === "disabled" &&
                            b.status !== "disabled"
                          )
                            return 1;
                          if (
                            a.status !== "disabled" &&
                            b.status === "disabled"
                          )
                            return -1;
                          return 0; // Keep original order for items with same status
                        })
                        .map((item) => {
                          const isFullyReserved = isItemGiftFullyReserved(item);
                          return (
                            <div
                              key={item.id}
                              onClick={() => {
                                if (item.status === "disabled") {
                                  return; // Prevent clicking on hidden items
                                }
                                handleItemGiftClick(item);
                              }}
                              className={`bg-white min-h-[200px] sm:h-[168px] mx-0 sm:mx-4 md:mx-0 rounded-xl flex flex-col md:flex-row items-stretch border border-[#F0F0F0] shadow-[0px_12px_120px_0px_rgba(95,95,95,0.06)] transition-shadow relative ${
                                isFullyReserved || item.status === "disabled"
                                  ? " cursor-not-allowed"
                                  : "cursor-pointer hover:shadow-lg"
                              }`}
                            >
                              {/* Hidden Item Overlay */}
                              {item.status === "disabled" && (
                                <div className="absolute inset-0 bg-black/30 rounded-xl flex items-center justify-center z-30">
                                  <div className="bg-white px-4 py-2 rounded-full shadow-md text-sm font-medium text-gray-700">
                                    Unavailable
                                  </div>
                                </div>
                              )}
                              {/* Most Wanted Badge - Top Right */}
                              {eventDetails?.most_wanted_gift_id ===
                                item.id && (
                                <div className="absolute top-2 right-2 z-10">
                                  <div className="bg-[#F5F6FE]   text-[#5F66F3] px-2 py-1 rounded-full ">
                                    <span className="text-xs font-medium italic">
                                      📍 MOST WANTED
                                    </span>
                                  </div>
                                </div>
                              )}
                              <div className="w-full md:w-[155px] h-[140px] sm:h-[180px] md:h-auto md:min-h-full bg-gray-200 md:rounded-l-lg rounded-t-xl md:rounded-t-none overflow-hidden flex-shrink-0">
                                <img
                                  src={item.image_preview_url || defaultGiftImg}
                                  alt={item.name}
                                  className="w-full h-full object-cover"
                                  onError={(e) => {
                                    // Fallback to default image if the image fails to load
                                    const target = e.target as HTMLImageElement;
                                    target.src = defaultGiftImg;
                                  }}
                                />
                              </div>
                              <div className="flex-1 text-center md:text-left p-3 sm:p-4 md:p-5">
                                <h3 className="text-base sm:text-lg md:text-xl lg:text-[22px] font-medium text-grey-750 mb-2">
                                  {item.name.length >
                                  (window.innerWidth < 640 ? 15 : 10)
                                    ? item.name.substring(
                                        0,
                                        window.innerWidth < 640 ? 15 : 10
                                      ) + "..."
                                    : item.name}
                                </h3>
                                <p className="text-xs sm:text-sm md:text-base text-grey-100 mb-2">
                                  {item?.description?.length >
                                  (window.innerWidth < 640 ? 80 : 100)
                                    ? item.description.substring(
                                        0,
                                        window.innerWidth < 640 ? 80 : 200
                                      ) + "..."
                                    : item.description}
                                </p>

                                <div className="flex flex-wrap gap-1.5 sm:gap-2 items-center mb-3 sm:mb-4">
                                  {/* Price Badge */}
                                  <div className="inline-flex items-center gap-1 px-2 py-1 sm:px-2.5 sm:py-1.5 bg-[#F4F3FF] rounded-2xl">
                                    <Tag2
                                      size={10}
                                      variant="Bulk"
                                      color="#5856D6"
                                    />
                                    <span className="text-xs sm:text-sm font-bold text-[#5925DC] leading-[1.429] tracking-[-0.01em]">
                                      ₦{Number(item.price).toLocaleString()}
                                    </span>
                                  </div>

                                  {/* Quantity Badge */}
                                  {/* {item.quantity > 1 && (
                                  <>
                                    <span className="text-gray-400">•</span>
                                    <span className="text-gray-600 text-sm">
                                      {item.quantity} Pcs
                                    </span>
                                  </>
                                )} */}

                                  {/* Reservations Badge */}
                                  {item.total_reservations > 0 && (
                                    <>
                                      <span className="text-gray-400 text-xs sm:text-sm">
                                        •
                                      </span>
                                      <span className="text-gray-600 text-xs sm:text-sm">
                                        {item.total_reservations} Pcs Reserved
                                      </span>
                                    </>
                                  )}

                                  {/* Fully Reserved Badge */}
                                  {isFullyReserved && (
                                    <>
                                      <span className="text-gray-400 text-xs sm:text-sm">
                                        •
                                      </span>
                                      <span className="text-red-600 text-xs sm:text-sm font-medium">
                                        Fully Reserved
                                      </span>
                                    </>
                                  )}

                                  {/* Quantity Display */}
                                  <div className="flex items-center gap-1 bg-gray-100 text-gray-700 px-2 py-1.5 sm:px-3 sm:py-2 md:px-2.5 md:py-1.5 rounded-full">
                                    <svg
                                      width="6"
                                      height="6"
                                      viewBox="0 0 6 6"
                                      fill="none"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <circle
                                        cx="3"
                                        cy="3"
                                        r="3"
                                        fill="#717680"
                                      />
                                    </svg>

                                    <span className="text-gray-700 text-xs sm:text-sm font-medium">
                                      {item.quantity} Pcs
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          );
                        })
                    )}
                  </div>
                </div>
              ) : (
                <div>
                  <div className="space-y-3 sm:space-y-4">
                    {cashItems.length === 0 ? (
                      <div className="text-center py-12">
                        <p className="text-gray-600">No cash gifts available</p>
                      </div>
                    ) : (
                      cashItems
                        .sort((a, b) => {
                          // Hidden items (disabled) come last
                          if (
                            a.status === "disabled" &&
                            b.status !== "disabled"
                          )
                            return 1;
                          if (
                            a.status !== "disabled" &&
                            b.status === "disabled"
                          )
                            return -1;
                          return 0; // Keep original order for items with same status
                        })
                        .map((item) => {
                          const cashGift = cashGifts.find(
                            (gift) => gift.id === item.id
                          )!;
                          const isFullyReserved =
                            isCashGiftFullyReserved(cashGift);
                          return (
                            <div
                              key={item.id}
                              className={`bg-white mx-0 sm:mx-4 md:mx-0 rounded-xl border border-[#F0F0F0] shadow-[0px_12px_120px_0px_rgba(95,95,95,0.06)] transition-shadow relative ${
                                isFullyReserved
                                  ? "opacity-60 cursor-not-allowed"
                                  : "cursor-pointer hover:shadow-lg"
                              }`}
                              onClick={() => {
                                if (item.status === "disabled") {
                                  return; // Prevent clicking on hidden items
                                }
                                handleCashGiftClick(cashGift);
                              }}
                            >
                              {/* Hidden Item Overlay */}
                              {item.status === "disabled" && (
                                <div className="absolute inset-0 bg-black/30 rounded-xl flex items-center justify-center z-30">
                                  <div className="bg-white px-4 py-2 rounded-full shadow-md text-sm font-medium text-gray-700">
                                    Unavailable
                                  </div>
                                </div>
                              )}
                              {/* Most Wanted Badge - Top Right */}
                              {eventDetails?.most_wanted_gift_id ===
                                item.id && (
                                <div className="absolute top-2 right-2 z-10">
                                  <div className="bg-white border border-[#5F66F3] text-[#5F66F3] px-2 py-1 rounded-full shadow-sm">
                                    <span className="text-xs font-medium italic">
                                      📍 MOST WANTED
                                    </span>
                                  </div>
                                </div>
                              )}
                              <div className="flex flex-col-reverse md:flex-row items-stretch gap-3 sm:gap-4 min-h-[140px]">
                                <div className="w-full md:w-[155px] h-[140px] sm:h-[180px] md:h-full bg-gray-200 md:rounded-l-lg rounded-t-xl md:rounded-t-none overflow-hidden flex-shrink-0">
                                  <img
                                    src={cash}
                                    className="w-full h-full object-cover"
                                  />
                                </div>
                                <div className="flex flex-1 flex-col justify-between p-3 sm:p-4 md:p-5 lg:py-4 lg:pr-4">
                                  <div className="text-center md:text-end mb-2 sm:mb-3 md:mb-0">
                                    {item.status === "available" && (
                                      <span className="text-xs font-bold italic text-primary-750 px-2 py-1.5 sm:px-3 sm:py-2 md:px-2 md:py-1 rounded-full bg-primary-150 uppercase tracking-wide">
                                        Available
                                      </span>
                                    )}
                                    {item.status === "reserved" && (
                                      <span className="text-orange-600 text-xs sm:text-sm font-medium">
                                        Reserved by you
                                      </span>
                                    )}
                                  </div>
                                  <div className="flex-1 text-center md:text-start mb-3 sm:mb-4 md:mb-0">
                                    <h3 className="text-lg sm:text-xl md:text-2xl lg:text-[28px] font-extrabold text-grey-750 mb-2">
                                      ₦{item.amount.toLocaleString()}
                                    </h3>
                                    <p className="text-xs sm:text-sm md:text-base text-grey-100 max-w-full md:max-w-[201px]">
                                      {item.description}
                                    </p>
                                    {cashGift.is_crowd_gift && (
                                      <div className="mt-2 sm:mt-3 space-y-1.5 sm:space-y-2">
                                        <div className="flex items-center gap-1.5 sm:gap-2">
                                          <span className="text-green-600 text-xs sm:text-sm">
                                            ✓
                                          </span>
                                          <span className="text-gray-600 text-xs sm:text-sm">
                                            Crowd gifting enabled
                                          </span>
                                        </div>
                                        <div className="w-full bg-gray-200 rounded-full h-1.5 sm:h-2">
                                          <div
                                            className="bg-blue-600 h-1.5 sm:h-2 rounded-full"
                                            style={{
                                              width: `${Math.min(
                                                (Number(
                                                  cashGift.total_contributions
                                                ) /
                                                  Number(item.amount)) *
                                                  100,
                                                100
                                              )}%`,
                                            }}
                                          ></div>
                                        </div>
                                        <p className="text-gray-600 text-xs sm:text-sm">
                                          ₦
                                          {Number(
                                            cashGift.total_contributions
                                          ).toLocaleString()}{" "}
                                          contributed by{" "}
                                          {cashGift.total_contributors} people
                                        </p>
                                      </div>
                                    )}
                                    {isFullyReserved &&
                                      !cashGift.is_crowd_gift && (
                                        <div className="flex items-center gap-1 mt-2">
                                          <div className="bg-red-100 px-2 py-1 rounded-full">
                                            <span className="text-xs text-red-600 font-semibold">
                                              Already Reserved
                                            </span>
                                          </div>
                                        </div>
                                      )}
                                  </div>
                                  <div className="flex w-full justify-center md:justify-end">
                                    <button
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleCashGiftClick(cashGift);
                                      }}
                                      disabled={isFullyReserved}
                                      className={`border whitespace-nowrap italic font-semibold text-xs sm:text-sm px-3 py-2 sm:px-4 sm:py-2.5 md:px-2.5 md:py-1.5 rounded-full min-w-[120px] sm:min-w-[140px] md:min-w-0 transition-colors ${
                                        isFullyReserved
                                          ? "border-gray-300 text-gray-400 cursor-not-allowed"
                                          : "border-primary-110 hover:bg-primary-110 hover:text-white"
                                      }`}
                                    >
                                      {isFullyReserved
                                        ? "Reserved"
                                        : "Send Cashgift"}
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          );
                        })
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      <Footer />
    </div>
  );
};
