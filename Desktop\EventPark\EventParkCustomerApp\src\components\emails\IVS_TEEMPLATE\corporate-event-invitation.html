<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Corporate Event Invitation</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Mr+<PERSON><PERSON><PERSON>&family=Cardo:wght@700&display=swap"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Cardo", serif;
        background-color: #f5f5f5;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
      }

      .invitation-container {
        position: relative;
        width: 1240px;
        height: 1748px;
        background-image: url("corporate-event-background.png");
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        max-width: 100%;
        max-height: 100vh;
        object-fit: contain;
      }

      .text-element {
        position: absolute;
        color: #ffffff;
      }

      .celebration-text {
        font-family: "Mr <PERSON>foe", cursive;
        font-weight: 400;
        font-size: 260px;
        line-height: 1em;
        text-align: center;
        color: #d4c174;
        left: 148px;
        top: 375.7815633267164px;
        width: 944.762569129467px;
        height: 633.7257222682238px;
        transform-origin: center center;
      }

      .invitation-text {
        font-family: "Cardo", serif;
        font-weight: 700;
        font-size: 36px;
        line-height: 1em;
        letter-spacing: 6.000000000000001%;
        text-align: center;
        left: 418px;
        top: 989px;
        width: 404px;
        height: 36px;
        transform-origin: center center;
      }

      .event-type {
        font-family: "Cardo", serif;
        font-weight: 700;
        font-size: 72px;
        line-height: 1.0499999788072374em;
        letter-spacing: 2%;
        text-transform: uppercase;
        text-align: center;
        left: 347px;
        top: 1061px;
        width: 546px;
        height: 152px;
        transform-origin: center center;
      }

      .event-month {
        font-family: "Cardo", serif;
        font-weight: 700;
        font-size: 42px;
        line-height: 1.0499999636695498em;
        letter-spacing: 2%;
        text-transform: uppercase;
        text-align: center;
        left: 396px;
        top: 1328px;
        width: 98px;
        height: 44px;
      }

      .event-day {
        font-family: "Cardo", serif;
        font-weight: 700;
        font-size: 100px;
        line-height: 1.0499999237060547em;
        letter-spacing: 2%;
        text-transform: uppercase;
        text-align: center;
        left: 568px;
        top: 1297px;
        width: 105px;
        height: 105px;
      }

      .event-time {
        font-family: "Cardo", serif;
        font-weight: 700;
        font-size: 42px;
        line-height: 1.0499999636695498em;
        letter-spacing: 2%;
        text-transform: uppercase;
        text-align: center;
        left: 747px;
        top: 1328px;
        width: 103px;
        height: 44px;
      }

      .venue-address {
        font-family: "Cardo", serif;
        font-weight: 700;
        font-size: 36px;
        line-height: 1em;
        letter-spacing: 6.000000000000001%;
        text-align: center;
        left: 242px;
        top: 1509px;
        width: 756px;
        height: 36px;
      }

      .rsvp-info {
        font-family: "Cardo", serif;
        font-weight: 700;
        font-size: 30px;
        line-height: 1em;
        letter-spacing: 4%;
        text-align: center;
        left: 392px;
        top: 1589px;
        width: 456px;
        height: 30px;
      }

      /* Dynamic scaling for celebration text */
      .celebration-text[data-length="8"] {
        transform: scale(0.95);
      }
      .celebration-text[data-length="9"] {
        transform: scale(0.9);
      }
      .celebration-text[data-length="10"] {
        transform: scale(0.85);
      }
      .celebration-text[data-length="11"] {
        transform: scale(0.8);
      }
      .celebration-text[data-length="12"] {
        transform: scale(0.75);
      }
      .celebration-text[data-length="13"] {
        transform: scale(0.7);
      }
      .celebration-text[data-length="14"] {
        transform: scale(0.65);
      }
      .celebration-text[data-length="15"] {
        transform: scale(0.6);
      }
      .celebration-text[data-length="16"] {
        transform: scale(0.55);
      }
      .celebration-text[data-length="17"] {
        transform: scale(0.5);
      }
      .celebration-text[data-length="18"],
      .celebration-text[data-length="19"],
      .celebration-text[data-length="20"] {
        transform: scale(0.45);
      }

      /* Dynamic scaling for event type */
      .event-type[data-length="8"] {
        transform: scale(0.95);
      }
      .event-type[data-length="9"] {
        transform: scale(0.9);
      }
      .event-type[data-length="10"] {
        transform: scale(0.85);
      }
      .event-type[data-length="11"] {
        transform: scale(0.8);
      }
      .event-type[data-length="12"] {
        transform: scale(0.75);
      }
      .event-type[data-length="13"] {
        transform: scale(0.7);
      }
      .event-type[data-length="14"] {
        transform: scale(0.65);
      }
      .event-type[data-length="15"] {
        transform: scale(0.6);
      }
      .event-type[data-length="16"] {
        transform: scale(0.55);
      }
      .event-type[data-length="17"] {
        transform: scale(0.5);
      }
      .event-type[data-length="18"],
      .event-type[data-length="19"],
      .event-type[data-length="20"] {
        transform: scale(0.45);
      }

      @media screen and (max-width: 1280px) {
        .invitation-container {
          transform: scale(0.8);
          transform-origin: center center;
        }
      }

      @media screen and (max-width: 1024px) {
        .invitation-container {
          transform: scale(0.6);
          transform-origin: center center;
        }
      }

      @media screen and (max-width: 768px) {
        .invitation-container {
          transform: scale(0.4);
          transform-origin: center center;
        }
      }
    </style>
  </head>
  <body>
    <div class="invitation-container">
      <div class="text-element celebration-text">Let's<br />Celebrate</div>

      <div class="text-element invitation-text">You are invited to our</div>

      <div class="text-element event-type">Corporate<br />event party</div>

      <div class="text-element event-month">{{{event_month}}}</div>

      <div class="text-element event-day">{{{event_day}}}</div>

      <div class="text-element event-time">{{{event_time}}}</div>

      <div class="text-element venue-address">{{{venue_address}}}</div>

      <div class="text-element rsvp-info">{{{rsvp_info}}}</div>
    </div>
  </body>
</html>
