/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useRef, useState } from 'react';
import { ArrowCircleRight2 } from 'iconsax-react';
import { Button } from '../../components/button/button';
import { useMutation } from '@tanstack/react-query';
import { AuthServices } from '../../lib/services/auth';
import { toast } from 'react-toastify';
import { formatTime } from '../../lib/helpers';

interface VerifyPasswordChangeProps {
  email: string;
  onVerify: () => void;
  onClose: () => void;
}

export const VerifyPasswordChange = ({
  email,
  onVerify,
  onClose,
}: VerifyPasswordChangeProps) => {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const inputRefs = useRef<HTMLInputElement[]>([]);
  const modalRef = useRef<HTMLDivElement>(null);
  const [timeLeft, setTimeLeft] = useState<number | null>(null);
  const [canResend, setCanResend] = useState(false);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);
  const startCountdown = (expiration: string) => {
    const expirationTime = new Date(expiration).getTime();
    const now = new Date().getTime();
    const remainingTime = Math.max(
      0,
      Math.floor((expirationTime - now) / 1000)
    );

    setTimeLeft(remainingTime);
    setCanResend(remainingTime <= 0);

    if (remainingTime > 0) {
      const timer = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev === null || prev <= 1) {
            clearInterval(timer);
            setCanResend(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  };
  useEffect(() => {
    const expiresAt = localStorage.getItem('reset_expiry');
    if (!expiresAt) return;
    return startCountdown(expiresAt);
  }, []);
  const verifyMutation = useMutation({
    mutationFn: () =>
      AuthServices.passwordResetOTP({
        otp: otp.join(''),
        email: email,
      }),
    onSuccess: (data) => {
     
      const resetToken = data?.data?.access_token;
      if (resetToken) {
        localStorage.setItem('password_reset_token', resetToken);
      }
      onVerify();
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || error?.message);
    },
  });
  const resendMutation = useMutation({
    mutationFn: () =>
      AuthServices.passwordResetInitiate({
        email: email,
      }),
    onSuccess: (data) => {
      const newExpiresAt = data?.data?.expires_at;
      if (newExpiresAt) {
        startCountdown(newExpiresAt);
        setCanResend(false);
      }
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || error?.message);
    },
  });

  const handleResendOTP = () => {
    resendMutation.mutate();
  };
  const handleChange = (index: number, value: string) => {
    if (/^\d*$/.test(value)) {
      const newOtp = [...otp];
      newOtp[index] = value;
      setOtp(newOtp);

      if (value !== '' && index < 5) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };
  const isOTPComplete = otp.every((digit) => digit !== '');

  const handleVerify = () => {
    if (isOTPComplete) {
      verifyMutation.mutate();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').trim();
    if (/^\d{6}$/.test(pastedData)) {
      const digits = pastedData.split('');
      setOtp(digits);
      inputRefs.current[5]?.focus();
    }
  };

  const handleKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (e.key === 'Backspace' && index > 0 && otp[index] === '') {
      inputRefs.current[index - 1]?.focus();
    }
  };

  return (
    <div
      ref={modalRef}
      className="relative mt-10 bg-white rounded-2xl font-rethink shadow-[0px_12px_120px_0px_#5F5F5F0F] w-full p-4 z-50">
      <div className="mb-6">
        <h3 className="text-sm font-medium mb-4.5 tracking-[0.12em] text-primary">
          PASSWORD & SECURITY
        </h3>
        <h2 className="text-2xl font-medium mb-1.5">Change Password</h2>
        <p className="text-grey-950 text-base">
          Update your password to stay secure.
        </p>
      </div>
      <div className="flex gap-1 mb-6 w-[81px] bg-cus-pink-700 rounded-full">
        <div className="h-[8px] w-10 bg-primary-750 rounded-full"></div>
        <div className="h-[8px] w-10 bg-cus-pink-700 rounded-full"></div>
      </div>
      <div className="border border-grey-150 px-4 pt-5 pb-4 rounded-xl">
        <p className="text-lg font-normal">
          Kindly enter 6-digit OTP sent to verify password change
        </p>

        <div className="flex gap-2 my-5">
          {otp.map((digit, index) => (
            <input
              key={index}
              ref={(el) => {
                if (el) inputRefs.current[index] = el;
              }}
              type="text"
              inputMode="numeric"
              placeholder="0"
              maxLength={1}
              value={digit}
              onChange={(e) => handleChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              onPaste={handlePaste}
              className={`md:w-[56px] md:h-[56px] h-10.5 w-10.5 placeholder:text-grey-200 text-center border border-primary-950 rounded-full text-[32px] font-medium leading-[60px] tracking-[-2%] shadow-xs shadow-[hsla(220,29%,5%,0.05)] ${
                digit
                  ? 'text-[#00000D] border-[#DBDDFC]'
                  : 'border-stroke-gray-300'
              } focus:outline-none focus:border-[#A6AAF9] focus:text-primary focus:shadow-[0px_0px_0px_4px_#DBDDFC] `}
            />
          ))}
        </div>
        <div className="flex items-center gap-3 border border-grey-800 py-1 pr-1 pl-3.5 text-nowrap rounded-full mb-14 max-w-[218px] text-sm font-medium">
          <span className="text-grey-100 leading-none">No OTP Yet?</span>
          <Button
            variant="neutral"
            onClick={handleResendOTP}
            disabled={!canResend || resendMutation.isPending}
            className={`bg-cus-pink-300 h-7 px-3 text-nowrap  items-center text-primary-650 rounded-full ${
              !canResend && 'text-xs !cursor-default font-bold '
            }`}>
            {canResend ? 'Resend OTP' : `Resend in ${formatTime(timeLeft)}`}
          </Button>
        </div>
      </div>

      <button
        onClick={handleVerify}
        disabled={!isOTPComplete || verifyMutation.isPending}
        className={`w-fit flex items-center gap-2 ${
          !isOTPComplete || verifyMutation.isPending
            ? 'opacity-40 cursor-not-allowed '
            : 'cursor-pointer'
        } bg-primary text-white py-2 px-3.5 rounded-full text-sm font-semibold cursor-pointer mt-4`}>
        {verifyMutation.isPending ? 'Loading...' : 'Continue'}
        <ArrowCircleRight2 size={20} color="#fff" variant="Bulk" />
      </button>
    </div>
  );
};
