"use client";

import {
  forwardRef,
  ReactNode,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { twMerge } from "tailwind-merge";

export interface SliderHandle {
  moveLeft: () => void;
  moveRight: () => void;
  move: () => void;
}

interface Props {
  elements: ReactNode[];
  currentElement: number;
  setCurrentElement: React.Dispatch<React.SetStateAction<number>>;
  onSlide?: (slider: HTMLDivElement) => void;
  elementsContainerClassName?: string;
}

export const Slider = forwardRef<SliderHandle, Props>(function (
  {
    elements,
    currentElement,
    setCurrentElement,
    onSlide,
    elementsContainerClassName,
  },
  ref
) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const [sizeChanged, setSizeChanged] = useState(1);

  const scrollListener = (e: Event) => {
    const element = e.target as HTMLDivElement;
    const scrollContainerWidth = element.offsetWidth;
    const scrollRatio = element.scrollLeft / scrollContainerWidth;
    const decimal = scrollRatio - Math.floor(scrollRatio);

    const skipCount =
      decimal > 0.6 && decimal < 0.99
        ? Math.floor(scrollRatio)
        : decimal > 0.1
        ? Math.floor(scrollRatio) + 1
        : Math.floor(scrollRatio);

    setCurrentElement(skipCount);
  };

  const resizeListener = () => {
    setSizeChanged((x) => x + 1);
  };

  useEffect(() => {
    if (!scrollContainerRef.current) return;

    const scrollContainer = scrollContainerRef.current;
    scrollContainer.addEventListener("scrollend", scrollListener);

    return () =>
      scrollContainer.removeEventListener("scrollend", scrollListener);
  }, []);

  useEffect(() => {
    window.addEventListener("resize", resizeListener);

    return () => window.removeEventListener("resize", resizeListener);
  }, []);

  useEffect(() => {
    if (!scrollContainerRef.current) return;

    const scrollContainer = scrollContainerRef.current;
    scrollContainer.addEventListener("touchend", touchendListener);

    return () =>
      scrollContainer.removeEventListener("touchend", touchendListener);
  }, []);

  const touchendListener = (e: TouchEvent) => {
    const ctarget = e.currentTarget as HTMLDivElement;
    const scrollContainerWidth = ctarget.offsetWidth;
    const scrollRatio = ctarget.scrollLeft / scrollContainerWidth;
    const decimal = scrollRatio - Math.floor(scrollRatio);

    const skipCount =
      decimal > 0.6 && decimal < 0.99
        ? Math.floor(scrollRatio)
        : decimal > 0.1
        ? Math.floor(scrollRatio) + 1
        : Math.floor(scrollRatio);

    setCurrentElement(skipCount);
  };

  useEffect(() => {
    if (!scrollContainerRef.current) return;
    const scrollContainer = scrollContainerRef.current;
    const scrollContainerWidth = scrollContainer.offsetWidth;

    scrollContainer.scrollTo({
      left: currentElement * scrollContainerWidth,
      behavior: "smooth",
    });
  }, [currentElement, scrollContainerRef, sizeChanged]);
  useEffect(() => {
    const element = scrollContainerRef.current;
    if (element) onSlide?.(element);
  }, []);

  const moveLeft = useCallback(() => {
    setCurrentElement((x) => (x < elements.length - 1 ? x + 1 : x));
  }, [setCurrentElement, elements]);

  const moveRight = useCallback(() => {
    setCurrentElement((x) => (x > 0 ? x - 1 : x));
  }, [setCurrentElement]);

  const move = useCallback(() => {
    if (currentElement === elements.length - 1) {
      moveRight();
    } else {
      moveLeft();
    }
  }, [moveLeft, moveRight, currentElement, elements]);

  useImperativeHandle(
    ref,
    () => {
      return {
        moveLeft,
        moveRight,
        move,
      };
    },
    [moveLeft, moveRight, move]
  );

  return (
    <div className="relative z-0 w-full h-full">
      <div
        className={twMerge(
          "flex overflow-hidden scrollbar-none h-full",
          elementsContainerClassName
        )}
        ref={scrollContainerRef}
      >
        {elements.map((Element, index) => (
          <div className="min-w-full" key={index}>
            {Element}
          </div>
        ))}
      </div>
    </div>
  );
});
