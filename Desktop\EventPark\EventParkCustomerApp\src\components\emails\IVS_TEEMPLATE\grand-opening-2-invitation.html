<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Grand Opening 2 Invitation</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Red+Hat+Display:wght@400&family=Rampart+One&display=swap"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Red Hat Display", sans-serif;
        background-color: #f5f5f5;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
      }

      .invitation-container {
        position: relative;
        width: 1414px;
        height: 2000px;
        background-image: url("https://customer-preprod.eventpark.africa/assets/email-templates//grand-opening-2-background.png");
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        max-width: 100%;
        max-height: 100vh;
        object-fit: contain;
      }

      .text-element {
        position: absolute;
        color: #241717;
      }

      .company-name {
        font-family: "Red Hat Display", sans-serif;
        font-weight: 400;
        font-size: 54px;
        line-height: 1em;
        letter-spacing: 4%;
        text-transform: uppercase;
        text-align: center;
        left: 532px;
        top: 472px;
        width: 351px;
        height: 54px;
        transform-origin: center center;
      }

      .invitation-text {
        font-family: "Rampart One", cursive;
        font-weight: 400;
        font-size: 220px;
        line-height: 1em;
        letter-spacing: 4%;
        text-transform: uppercase;
        text-align: center;
        left: 213px;
        top: 606px;
        width: 989px;
        height: 440px;
        transform-origin: center center;
      }

      .grand-opening-text {
        font-family: "Red Hat Display", sans-serif;
        font-weight: 400;
        font-size: 54px;
        line-height: 1em;
        letter-spacing: 4%;
        text-transform: uppercase;
        text-align: center;
        left: 355px;
        top: 1158px;
        width: 705px;
        height: 54px;
        transform-origin: center center;
      }

      .event-details {
        font-family: "Red Hat Display", sans-serif;
        font-weight: 400;
        font-size: 40px;
        line-height: 1.4em;
        letter-spacing: 4%;
        text-align: center;
        left: 453px;
        top: 1439px;
        width: 508px;
        height: 168px;
      }

      .rsvp-info {
        font-family: "Red Hat Display", sans-serif;
        font-weight: 400;
        font-size: 40px;
        line-height: 1.4em;
        letter-spacing: 4%;
        text-align: center;
        left: 391px;
        top: 1791px;
        width: 632px;
        height: 56px;
      }

      /* Dynamic scaling for company name */
      .company-name[data-length="8"] {
        transform: scale(0.95);
      }
      .company-name[data-length="9"] {
        transform: scale(0.9);
      }
      .company-name[data-length="10"] {
        transform: scale(0.85);
      }
      .company-name[data-length="11"] {
        transform: scale(0.8);
      }
      .company-name[data-length="12"] {
        transform: scale(0.75);
      }
      .company-name[data-length="13"] {
        transform: scale(0.7);
      }
      .company-name[data-length="14"] {
        transform: scale(0.65);
      }
      .company-name[data-length="15"] {
        transform: scale(0.6);
      }
      .company-name[data-length="16"] {
        transform: scale(0.55);
      }
      .company-name[data-length="17"] {
        transform: scale(0.5);
      }
      .company-name[data-length="18"],
      .company-name[data-length="19"],
      .company-name[data-length="20"] {
        transform: scale(0.45);
      }

      /* Dynamic scaling for invitation text */
      .invitation-text[data-length="8"] {
        transform: scale(0.95);
      }
      .invitation-text[data-length="9"] {
        transform: scale(0.9);
      }
      .invitation-text[data-length="10"] {
        transform: scale(0.85);
      }
      .invitation-text[data-length="11"] {
        transform: scale(0.8);
      }
      .invitation-text[data-length="12"] {
        transform: scale(0.75);
      }
      .invitation-text[data-length="13"] {
        transform: scale(0.7);
      }
      .invitation-text[data-length="14"] {
        transform: scale(0.65);
      }
      .invitation-text[data-length="15"] {
        transform: scale(0.6);
      }
      .invitation-text[data-length="16"] {
        transform: scale(0.55);
      }
      .invitation-text[data-length="17"] {
        transform: scale(0.5);
      }
      .invitation-text[data-length="18"],
      .invitation-text[data-length="19"],
      .invitation-text[data-length="20"] {
        transform: scale(0.45);
      }

      /* Dynamic scaling for grand opening text */
      .grand-opening-text[data-length="8"] {
        transform: scale(0.95);
      }
      .grand-opening-text[data-length="9"] {
        transform: scale(0.9);
      }
      .grand-opening-text[data-length="10"] {
        transform: scale(0.85);
      }
      .grand-opening-text[data-length="11"] {
        transform: scale(0.8);
      }
      .grand-opening-text[data-length="12"] {
        transform: scale(0.75);
      }
      .grand-opening-text[data-length="13"] {
        transform: scale(0.7);
      }
      .grand-opening-text[data-length="14"] {
        transform: scale(0.65);
      }
      .grand-opening-text[data-length="15"] {
        transform: scale(0.6);
      }
      .grand-opening-text[data-length="16"] {
        transform: scale(0.55);
      }
      .grand-opening-text[data-length="17"] {
        transform: scale(0.5);
      }
      .grand-opening-text[data-length="18"],
      .grand-opening-text[data-length="19"],
      .grand-opening-text[data-length="20"] {
        transform: scale(0.45);
      }

      @media screen and (max-width: 1280px) {
        .invitation-container {
          transform: scale(0.8);
          transform-origin: center center;
        }
      }

      @media screen and (max-width: 1024px) {
        .invitation-container {
          transform: scale(0.6);
          transform-origin: center center;
        }
      }

      @media screen and (max-width: 768px) {
        .invitation-container {
          transform: scale(0.4);
          transform-origin: center center;
        }
      }
    </style>
  </head>
  <body>
    <div class="invitation-container">
      <div class="text-element company-name">{{{company_name}}}</div>

      <div class="text-element invitation-text">you're<br />invited</div>

      <div class="text-element grand-opening-text">to our grand opening</div>

      <div class="text-element event-details">
        {{{event_date}}}
        <br />
        {{{event_time}}}<br />
        {{{venue_address}}}
      </div>

      <div class="text-element rsvp-info">{{{rsvp_info}}}</div>
    </div>

    <script>
      function calculateTextLengths() {
        const companyName = document.querySelector(".company-name");
        const invitationText = document.querySelector(".invitation-text");
        const grandOpeningText = document.querySelector(".grand-opening-text");

        if (companyName) {
          const textContent = companyName.textContent || companyName.innerText;
          const cleanText = textContent.replace(/\s+/g, " ").trim();
          const totalLength = cleanText.length;
          companyName.setAttribute("data-length", totalLength.toString());
          console.log(`Company name length: ${totalLength} characters`);
        }

        if (invitationText) {
          const textContent =
            invitationText.textContent || invitationText.innerText;
          const cleanText = textContent.replace(/\s+/g, " ").trim();
          const totalLength = cleanText.length;
          invitationText.setAttribute("data-length", totalLength.toString());
          console.log(`Invitation text length: ${totalLength} characters`);
        }

        if (grandOpeningText) {
          const textContent =
            grandOpeningText.textContent || grandOpeningText.innerText;
          const cleanText = textContent.replace(/\s+/g, " ").trim();
          const totalLength = cleanText.length;
          grandOpeningText.setAttribute("data-length", totalLength.toString());
          console.log(`Grand opening text length: ${totalLength} characters`);
        }
      }

      // Run the calculation when the page loads
      document.addEventListener("DOMContentLoaded", calculateTextLengths);

      // Also run it after a short delay to ensure all content is loaded
      setTimeout(calculateTextLengths, 100);
    </script>
  </body>
</html>
