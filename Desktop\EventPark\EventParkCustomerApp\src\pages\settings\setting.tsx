import { ArrowLeft } from "iconsax-react";
import { SettingsCard } from "../../components/reuseables/settingsCard";
import { useUserAuthStore } from "../../lib/store/auth";
import { Footer } from "../prelaunch/footer";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import { EditProfile } from "./editProfile";
import { ChangePassword } from "./changePassword";
import { NewPassword } from "./NewPassword";
import { TransactionPinModal } from "./TransactionPinModal";
import { TransactionPinSuccessModal } from "./TransactionPinSuccessModal";
import { InitiatePasswordReset } from "./InitiatePasswordReset";
import { PasswordResetOTPVerify } from "./PasswordResetOTPVerify";

export const Settings = () => {
  const navigate = useNavigate();
  const user = useUserAuthStore((state) => state.userData);
  const [isOpen, setIsOpen] = useState(false);
  // const [isEmailOpen, setIsEmailOpen] = useState(false);
  const [isPasswordOpen, setIsPasswordOpen] = useState(false);
  const [isNewPasswordOpen, setIsNewPasswordOpen] = useState(false);
  const [isTransactionPinOpen, setIsTransactionPinOpen] = useState(false);
  const [isTransactionPinSuccessOpen, setIsTransactionPinSuccessOpen] =
    useState(false);
  const [currentPassword, setCurrentPassword] = useState("");
  const [isPasswordResetInitiated, setIsPasswordResetInitiated] =
    useState(false);
  const [isVerifyingPasswordOTP, setIsVerifyingPasswordOTP] = useState(false);
  const [isSettingNewPassword, setIsSettingNewPassword] = useState(false);
  const [userEmail, setUserEmail] = useState("");
  const openModal = () => setIsOpen(true);
  const closeModal = () => setIsOpen(false);
  // const openEmailModal = () => setIsEmailOpen(true);
  const openPasswordModal = () => {
    if (user?.password_set) {
      setIsPasswordOpen(true);
    } else {
      setUserEmail(user?.email || "");
      setIsPasswordResetInitiated(true);
    }
  };
  const closePasswordModal = () => setIsPasswordOpen(false);
  const openTransactionModal = () => setIsTransactionPinOpen(true);
  const closeTransactionModal = () => setIsTransactionPinOpen(false);
  const openTransactionSuccessModal = () => {
    setIsTransactionPinOpen(false);
    setIsTransactionPinSuccessOpen(true);
  };
  const closeTransactionSuccessModal = () =>
    setIsTransactionPinSuccessOpen(false);
  const handleSave = () => {
    closeModal();
  };

  const handlePasswordNext = (oldPassword: string) => {
    setCurrentPassword(oldPassword);
    setIsPasswordOpen(false);
    setIsNewPasswordOpen(true);
  };

  // Handlers for first-time password setting flow
  const handlePasswordResetInitiated = () => {
    setIsPasswordResetInitiated(false);
    setIsVerifyingPasswordOTP(true);
  };

  const handlePasswordOTPVerified = () => {
    setIsVerifyingPasswordOTP(false);
    setIsSettingNewPassword(true);
  };

  const handleNewPasswordSet = () => {
    setIsSettingNewPassword(false);
    // Reset all states
    setUserEmail("");
  };

  const handleClosePasswordFlow = () => {
    setIsPasswordResetInitiated(false);
    setIsVerifyingPasswordOTP(false);
    setIsSettingNewPassword(false);
    setUserEmail("");
  };

  // const handleNewPasswordSave = () => {
  //   setIsNewPasswordOpen(false);
  // };

  return (
    <div className="p-4 md:p-0 bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] font-rethink">
      <button
        type="button"
        onClick={() => navigate(-1)}
        className={`fixed left-4 md:left-1/2 md:ml-[-282px] top-8 p-2.5 w-fit bg-white rounded-full cursor-pointer ${
          isOpen || isPasswordOpen || isTransactionPinOpen
            ? "blur-sm pointer-events-none"
            : "z-[999]"
        }`}
      >
        <div className="bg-[#F5F6FE] w-fit p-0.5">
          <div className="bg-primary p-0.5 rounded-full">
            <ArrowLeft size="16" color="#fff" />
          </div>{" "}
        </div>
      </button>
      <div className="max-w-[564px] mx-auto pt-8 pb-[116px] ">
        <div className="mb-7 pt-24 relative z-50">
          <h1 className="font-semibold text-2xl">Settings</h1>
          <p className="text-base text-grey-950">Manage your Account details</p>
        </div>

        <div className="space-y-4">
          <div
            className={`relative ${
              isPasswordOpen ||
              isNewPasswordOpen ||
              isTransactionPinOpen ||
              isPasswordResetInitiated ||
              isVerifyingPasswordOTP ||
              isSettingNewPassword
                ? "blur-xs pointer-events-none "
                : ""
            }`}
          >
            {isOpen ? (
              <EditProfile
                initialFirstName={user?.first_name || ""}
                initialLastName={user?.last_name || ""}
                onSave={handleSave}
                onClose={closeModal}
              />
            ) : (
              <SettingsCard
                title={user?.first_name + " " + user?.last_name}
                description={user?.email}
                buttonText="Edit Profile Details"
                onClick={openModal}
                initials={
                  (user?.first_name?.charAt(0)?.toUpperCase() || "") +
                  (user?.last_name?.charAt(0)?.toUpperCase() || "")
                }
                profilePicture={user?.profile_picture}
              />
            )}
          </div>

          {/* <div
            className={`relative ${
              isOpen ||
              isPasswordOpen ||
              isVerifyPasswordOpen ||
              isNewPasswordOpen
                ? ' blur-xs pointer-events-none'
                : ''
            }`}>
            <SettingsCard
              title="Change Email"
              description="Keep your account updated with a new email."
              title1="ACCOUNT EMAIL"
              buttonText="Change Email"
              // onClick={openEmailModal}
            />
          </div> */}

          <div
            className={`relative ${
              isOpen || isTransactionPinOpen
                ? "blur-xs pointer-events-none "
                : ""
            }`}
          >
            {isPasswordOpen ? (
              <ChangePassword
                onSave={handlePasswordNext}
                onClose={closePasswordModal}
              />
            ) : isNewPasswordOpen ? (
              <NewPassword
                isfirstTimePasswordSetting={true}
                currentPassword={currentPassword}
                onSave={() => {
                  setIsNewPasswordOpen(false);
                }}
                onClose={() => setIsNewPasswordOpen(false)}
              />
            ) : isPasswordResetInitiated ? (
              <InitiatePasswordReset
                email={userEmail}
                onInitiated={handlePasswordResetInitiated}
                onClose={handleClosePasswordFlow}
              />
            ) : isVerifyingPasswordOTP ? (
              <PasswordResetOTPVerify
                email={userEmail}
                onVerified={handlePasswordOTPVerified}
                onResend={() => {
                  // Reset to initiate flow to resend
                  setIsVerifyingPasswordOTP(false);
                  setIsPasswordResetInitiated(true);
                }}
                onBack={() => {
                  setIsVerifyingPasswordOTP(false);
                  setIsPasswordResetInitiated(true);
                }}
                onClose={handleClosePasswordFlow}
              />
            ) : isSettingNewPassword ? (
              <NewPassword
                currentPassword="" // No current password for first-time setting
                onSave={handleNewPasswordSet}
                onClose={handleClosePasswordFlow}
                isfirstTimePasswordSetting={false}
              />
            ) : (
              <SettingsCard
                title={user?.password_set ? "Change Password" : "Set Password"}
                description={
                  user?.password_set
                    ? "Update your password to stay secure."
                    : "Set your password to secure your account."
                }
                title1="PASSWORD & SECURITY"
                buttonText={
                  user?.password_set ? "Change Password" : "Set Password"
                }
                onClick={openPasswordModal}
              />
            )}
          </div>
          <div
            className={`relative ${
              isOpen ||
              isPasswordOpen ||
              isNewPasswordOpen ||
              isPasswordResetInitiated ||
              isVerifyingPasswordOTP ||
              isSettingNewPassword
                ? "blur-xs pointer-events-none "
                : ""
            }`}
          >
            {isTransactionPinOpen ? (
              <TransactionPinModal
                onSave={openTransactionSuccessModal}
                onClose={closeTransactionModal}
              />
            ) : (
              <SettingsCard
                title="Set Transaction Pin"
                description="For safe and secure withdrawals"
                title1="TRANSACTION PIN"
                buttonText="Set Transaction Pin"
                onClick={openTransactionModal}
              />
            )}
          </div>
        </div>
      </div>
      <Footer />

      {/* Transaction PIN Success Modal */}
      <TransactionPinSuccessModal
        isOpen={isTransactionPinSuccessOpen}
        onClose={closeTransactionSuccessModal}
      />
    </div>
  );
};
