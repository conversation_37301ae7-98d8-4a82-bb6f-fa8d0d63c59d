<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Wedding Celebration Invitation</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Alegreya+SC:wght@400&family=Pinyon+Script:wght@400&display=swap"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Alegreya SC", serif;
        background-color: #f5f5f5;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
      }

      .invitation-container {
        position: relative;
        width: 1240px;
        height: 1748px;
        background-image: url("/wedding-celebration-background.png");
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        max-width: 100%;
        max-height: 100vh;
        object-fit: contain;
      }

      .text-element {
        position: absolute;
      }

      .first-celebrant {
        font-family: "Alegreya SC", serif;
        font-weight: 400;
        font-size: 110px;
        line-height: 1.360999922318892em;
        letter-spacing: 5%;
        color: #3a3a3a;
        left: 447px;
        top: 323px;
        width: 353px;
        height: 150px;
        transform-origin: left center;
      }

      .connector-and {
        font-family: "Pinyon Script", cursive;
        font-weight: 400;
        font-size: 90px;
        line-height: 1.24755859375em;
        letter-spacing: 5%;
        color: #3a3a3a;
        left: 560px;
        top: 477px;
        width: 133px;
        height: 112px;
      }

      .second-celebrant {
        font-family: "Alegreya SC", serif;
        font-weight: 400;
        font-size: 110px;
        line-height: 1.360999922318892em;
        letter-spacing: 5%;
        color: #3a3a3a;
        left: 442px;
        top: 573px;
        width: 368px;
        height: 150px;
        transform-origin: left center;
      }

      .celebration-text {
        font-family: "Alegreya SC", serif;
        font-weight: 400;
        font-size: 54px;
        line-height: 1.1111111111111112em;
        letter-spacing: 5%;
        text-align: center;
        color: #737373;
        left: 109px;
        top: 1020px;
        width: 1022px;
        height: 120px;
      }

      .date-container {
        position: absolute;
        left: 309.5px;
        top: 1240px;
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 32px;
      }

      .date-day {
        font-family: "Alegreya SC", serif;
        font-weight: 400;
        font-size: 54px;
        line-height: 1.1111111111111112em;
        letter-spacing: 5%;
        color: #737373;
      }

      .date-number {
        font-family: "Alegreya SC", serif;
        font-weight: 400;
        font-size: 118.80000305175781px;
        line-height: 1.010100984153337em;
        letter-spacing: 5%;
        text-align: center;
        color: #3a3a3a;
      }

      .date-time {
        font-family: "Alegreya SC", serif;
        font-weight: 400;
        font-size: 54px;
        line-height: 1.1111111111111112em;
        letter-spacing: 5%;
        color: #737373;
      }

      .decorative-line {
        width: 0px;
        height: 98px;
        border-left: 3px solid #3a3a3a;
      }

      .event-month {
        font-family: "Alegreya SC", serif;
        font-weight: 400;
        font-size: 54px;
        line-height: 1.1111111111111112em;
        letter-spacing: 5%;
        text-align: center;
        color: #737373;
        left: 532px;
        top: 1172px;
        width: 176px;
        height: 60px;
      }

      .event-year {
        font-family: "Alegreya SC", serif;
        font-weight: 400;
        font-size: 54px;
        line-height: 1.1111111111111112em;
        letter-spacing: 5%;
        text-align: center;
        color: #737373;
        left: 563px;
        top: 1360px;
        width: 114px;
        height: 60px;
      }

      .venue-address {
        font-family: "Alegreya SC", serif;
        font-weight: 400;
        font-size: 54px;
        line-height: 1.1111111111111112em;
        letter-spacing: 5%;
        text-align: center;
        color: #737373;
        left: 244px;
        top: 1450px;
        width: 752px;
        height: 120px;
      }

      .reception-text {
        font-family: "Pinyon Script", cursive;
        font-weight: 400;
        font-size: 45px;
        line-height: 1.24755859375em;
        letter-spacing: 5%;
        color: #3a3a3a;
        left: 454px;
        top: 1600px;
        width: 344px;
        height: 56px;
      }

      /* Dynamic scaling for celebrant names */
      .first-celebrant[data-length="8"] {
        transform: scale(0.95);
      }
      .first-celebrant[data-length="9"] {
        transform: scale(0.9);
      }
      .first-celebrant[data-length="10"] {
        transform: scale(0.85);
      }
      .first-celebrant[data-length="11"] {
        transform: scale(0.8);
      }
      .first-celebrant[data-length="12"] {
        transform: scale(0.75);
      }
      .first-celebrant[data-length="13"] {
        transform: scale(0.7);
      }
      .first-celebrant[data-length="14"] {
        transform: scale(0.65);
      }
      .first-celebrant[data-length="15"] {
        transform: scale(0.6);
      }
      .first-celebrant[data-length="16"] {
        transform: scale(0.55);
      }
      .first-celebrant[data-length="17"] {
        transform: scale(0.5);
      }
      .first-celebrant[data-length="18"],
      .first-celebrant[data-length="19"],
      .first-celebrant[data-length="20"] {
        transform: scale(0.45);
      }

      .second-celebrant[data-length="8"] {
        transform: scale(0.95);
      }
      .second-celebrant[data-length="9"] {
        transform: scale(0.9);
      }
      .second-celebrant[data-length="10"] {
        transform: scale(0.85);
      }
      .second-celebrant[data-length="11"] {
        transform: scale(0.8);
      }
      .second-celebrant[data-length="12"] {
        transform: scale(0.75);
      }
      .second-celebrant[data-length="13"] {
        transform: scale(0.7);
      }
      .second-celebrant[data-length="14"] {
        transform: scale(0.65);
      }
      .second-celebrant[data-length="15"] {
        transform: scale(0.6);
      }
      .second-celebrant[data-length="16"] {
        transform: scale(0.55);
      }
      .second-celebrant[data-length="17"] {
        transform: scale(0.5);
      }
      .second-celebrant[data-length="18"],
      .second-celebrant[data-length="19"],
      .second-celebrant[data-length="20"] {
        transform: scale(0.45);
      }

      @media screen and (max-width: 1280px) {
        .invitation-container {
          transform: scale(0.8);
          transform-origin: center center;
        }
      }

      @media screen and (max-width: 1024px) {
        .invitation-container {
          transform: scale(0.6);
          transform-origin: center center;
        }
      }

      @media screen and (max-width: 768px) {
        .invitation-container {
          transform: scale(0.4);
          transform-origin: center center;
        }
      }
    </style>
  </head>
  <body>
    <div class="invitation-container">
      <div class="text-element first-celebrant">{{{first_celebrant}}}</div>

      <div class="text-element connector-and">and</div>

      <div class="text-element second-celebrant">{{{second_celebrant}}}</div>

      <div class="text-element celebration-text">{{{celebration_text}}}</div>

      <div class="date-container">
        <div class="date-day">{{{event_day}}}</div>
        <div class="decorative-line"></div>
        <div class="date-number">{{{event_date_number}}}</div>
        <div class="decorative-line"></div>
        <div class="date-time">{{{event_time}}}</div>
      </div>

      <div class="text-element event-month">{{{event_month}}}</div>

      <div class="text-element event-year">{{{event_year}}}</div>

      <div class="text-element venue-address">{{{venue_address}}}</div>

      <div class="text-element reception-text">Reception to follow</div>
    </div>

    <script>
      function calculateCelebrantNamesLength() {
        const firstCelebrant = document.querySelector(".first-celebrant");
        const secondCelebrant = document.querySelector(".second-celebrant");

        if (firstCelebrant) {
          const textContent =
            firstCelebrant.textContent || firstCelebrant.innerText;
          const cleanText = textContent.replace(/\s+/g, " ").trim();
          const totalLength = cleanText.length;
          firstCelebrant.setAttribute("data-length", totalLength.toString());

          console.log(`First celebrant length: ${totalLength} characters`);
        }

        if (secondCelebrant) {
          const textContent =
            secondCelebrant.textContent || secondCelebrant.innerText;
          const cleanText = textContent.replace(/\s+/g, " ").trim();
          const totalLength = cleanText.length;
          secondCelebrant.setAttribute("data-length", totalLength.toString());

          console.log(`Second celebrant length: ${totalLength} characters`);
        }
      }

      // Run the calculation when the page loads
      document.addEventListener(
        "DOMContentLoaded",
        calculateCelebrantNamesLength
      );

      // Also run it after a short delay to ensure all content is loaded
      setTimeout(calculateCelebrantNamesLength, 100);
    </script>
  </body>
</html>
