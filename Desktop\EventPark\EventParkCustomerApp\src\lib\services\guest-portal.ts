import axios from "axios";

// Create a separate axios instance for guest portal (no auth required)
const guestAPI = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 30000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Types based on the API documentation
export interface EventImage {
  created_at: string;
  event_id: string;
  id: string;
  preview_key: string;
  preview_url: string;
  updated_at: string;
}

export interface Guest {
  created_at: string;
  email: string;
  first_name: string;
  id: string;
  invite_status: string;
  last_name: string;
  phone_number: string;
}

export interface ValidateTokenResponse {
  event_banner_preview_url: string;
  event_date_from: string;
  event_date_to: string;
  event_description: string;
  event_gift_count: number;
  event_gift_registry_status: string;
  event_id: string;
  event_images: EventImage[];
  event_iv_preview_url: string;
  event_location_address: string;
  event_qr_code?: string;
  event_location_lat: number;
  event_location_long: number;
  event_location_name: string;
  event_title: string;
  event_visibility: string;
  guest: Guest;
  host_first_name: string;
  host_last_name: string;
}

export interface ValidateTokenRequest {
  token: string;
}

export interface RSVPRequest {
  email: string;
  first_name: string;
  last_name: string;
  phone_number: string;
  status: "accepted" | "declined";
}

export interface RSVPResponse {
  created_at: string;
  email: string;
  event_id: string;
  first_name: string;
  id: string;
  invite_sent: boolean;
  invite_status: string;
  invite_type: string;
  last_name: string;
  phone_number: string;
  updated_at: string;
}

export interface ApiError {
  code: string;
  message: string;
}

// Helper function to handle API errors
function handleApiError(error: unknown): never {
  if (axios.isAxiosError(error) && error.response?.data) {
    throw error.response.data as ApiError;
  }
  throw {
    code: "unknown_error",
    message: "An unexpected error occurred",
  } as ApiError;
}

export const GuestPortalAPI = {
  /**
   * Validate guest event invite token
   */
  async validateToken(token: string): Promise<ValidateTokenResponse> {
    try {
      const response = await guestAPI.post<ValidateTokenResponse>(
        "/v1/guest/tokens/invite/validate",
        { token }
      );
      return response.data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * RSVP to an event (accept or decline)
   */
  async rsvp(eventId: string, rsvpData: RSVPRequest): Promise<RSVPResponse> {
    try {
      const response = await guestAPI.post<RSVPResponse>(
        `/v1/guest/events/${eventId}/rsvp`,
        rsvpData
      );
      return response.data;
    } catch (error) {
      handleApiError(error);
    }
  },
};
