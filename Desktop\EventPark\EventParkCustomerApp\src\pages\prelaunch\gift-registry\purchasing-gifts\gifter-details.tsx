import { useState } from "react";
import { useParams } from "react-router-dom";
import { FormInput } from "../../../../components/inputs/form-input/form-input";
import { ArrowCircleRight2, ArrowDown2 } from "iconsax-react";
import { <PERSON><PERSON> } from "../../../../components/button/onboardingButton";
import {
  GuestGiftsAPI,
  GuestRegistrationRequest,
} from "../../../../lib/apis/guestGiftsApi";
import { guestTokenManager } from "../../../../lib/utils/guestTokenManager";
import { toast } from "react-toastify";

interface GuestData {
  email: string;
  first_name: string;
  last_name: string;
  phone_number: string;
}

interface GifterDetailsProps {
  onContinue?: (guestData?: GuestData, accessToken?: string) => void;
}

export const GifterDetails = ({ onContinue }: GifterDetailsProps) => {
  const { eventId } = useParams<{ eventId: string }>();
  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState("");
  const [mobileNumber, setMobileNumber] = useState("");
  const [isRegistering, setIsRegistering] = useState(false);

  const handleContinue = async () => {
    // Validate inputs
    if (!fullName.trim() || !email.trim()) {
      toast.error("Please fill in all fields");
      return;
    }

    // Split full name into first and last name
    const nameParts = fullName.trim().split(" ");
    const firstName = nameParts[0] || "";
    const lastName = nameParts.slice(1).join(" ") || "";

    if (!firstName || !lastName) {
      toast.error("Please enter both first and last name");
      return;
    }

    // Prepare guest registration data
    const guestData: GuestRegistrationRequest = {
      email: email.trim(),
      first_name: firstName,
      last_name: lastName,
      phone_number:
        mobileNumber.trim().length > 0 ? `+234${mobileNumber.trim()}` : "",
    };

    setIsRegistering(true);
    try {
      // Register guest
      const response = await GuestGiftsAPI.registerGuest(guestData);

      // Store enhanced guest token data
      guestTokenManager.setEnhancedGuestToken({
        access_token: response.access_token,
        access_token_expires_at: response.access_token_expires_at,
        refresh_token: response.refresh_token,
        refresh_token_expires_at: response.refresh_token_expires_at,
        guest_data: {
          email: guestData.email,
          first_name: guestData.first_name,
          last_name: guestData.last_name,
          id: "", // Will be populated from API response if available
          phone_number: guestData.phone_number,
          created_at: new Date().toISOString(),
        },
        event_id: eventId,
        expires_at: response.expires_at, // For backward compatibility
      });

      // Pass guest data and access token to parent and continue
      if (onContinue) {
        onContinue(guestData, response.access_token);
      }
    } catch (error) {
      const errorMsg =
        typeof error === "object" && error && "message" in error
          ? (error as { message?: string }).message ?? "Registration failed"
          : "Registration failed";
      toast.error(errorMsg);
    } finally {
      setIsRegistering(false);
    }
  };
  return (
    <div className="md:pl-5 md:pr-7">
      <p className="text-base my-4.5">
        Let's the recipient know who's gifting 🥳
      </p>
      <div className="pb-6">
        <FormInput
          label="Full Name"
          placeholder="Enter your full name"
          type="name"
          value={fullName}
          onChange={(e) => setFullName(e.target.value)}
          text="This would be recorded and sent to the recipient"
        />
        <FormInput
          label="Email Address"
          placeholder="Enter your email address"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
        />
        <FormInput
          label="Mobile Number"
          placeholder="Enter mobile number"
          type="number"
          className="no-spinner"
          pattern="[0-9]*"
          onInput={(e: React.FormEvent<HTMLInputElement>) => {
            const input = e.target as HTMLInputElement;
            if (input.value.length > 10) {
              input.value = input.value.slice(0, 10);
            }
          }}
          maxLength={10}
          value={mobileNumber}
          onChange={(e) => setMobileNumber(e.target.value)}
          leftAddon={
            <div className="flex items-center">
              <span className="text-grey-500 font-semibold mr-1 italic text-base">
                +234
              </span>
              <ArrowDown2 size={16} color="#717680" />
            </div>
          }
        />
        <Button
          variant="primary"
          size="md"
          className={`text-white bg-primary-650 md:mb-19 ${
            isRegistering ? "opacity-50 cursor-not-allowed" : ""
          }`}
          iconRight={
            isRegistering ? (
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
            ) : (
              <ArrowCircleRight2 size="20" color="#fff" variant="Bulk" />
            )
          }
          onClick={handleContinue}
          disabled={isRegistering}
        >
          {isRegistering ? "Processing..." : "Continue"}
        </Button>
      </div>
    </div>
  );
};
