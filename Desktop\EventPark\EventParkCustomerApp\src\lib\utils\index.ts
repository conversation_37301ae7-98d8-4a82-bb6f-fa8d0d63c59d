import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Get the user's IANA timezone identifier
 * @returns IANA timezone string (e.g., "America/New_York", "Europe/London")
 */
export function getUserTimezone(): string {
  try {
    // Intl.DateTimeFormat().resolvedOptions().timeZone returns IANA timezone format
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch (error) {
    console.error("Failed to get user timezone:", error);
    // Fallback to UTC if timezone detection fails
    return "UTC";
  }
}
