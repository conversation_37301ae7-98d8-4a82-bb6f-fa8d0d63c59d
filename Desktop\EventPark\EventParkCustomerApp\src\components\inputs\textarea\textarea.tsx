import { TextareaHTMLAttributes } from "react";
import { Label } from "../label/label";
import "./styles.css";

interface Props extends TextareaHTMLAttributes<HTMLTextAreaElement> {
  label: string;
  labelClassname: string;
  id: string;
  value: string;
}

export function TextArea({ label, labelClassname, ...props }: Props) {
  return (
    <>
      <Label
        id={`label-${props.id}`}
        htmlFor={props.id}
        className={labelClassname}
      >
        {label}
      </Label>
      <textarea
        {...props}
        className={`cutsom-textarea ${props.className}`}
      ></textarea>
    </>
  );
}
