export function base64ToBuffer(data: string) {
  const binaryString = atob(data);
  const buf = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    buf[i] = binaryString.charCodeAt(i);
  }
  return buf.buffer;
}

export function bufferToBase64(buffer: ArrayBuffer) {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
}

export function encodeECDSASignatureDER(rawSignature: ArrayBuffer): Uint8Array {
  const signature = new Uint8Array(rawSignature);
  const r = signature.slice(0, 32);
  const s = signature.slice(32, 64);

  const removeLeadingZeros = (arr: Uint8Array): Uint8Array => {
    let start = 0;
    while (start < arr.length && arr[start] === 0) start++;
    return arr.slice(start);
  };

  const rStripped = removeLeadingZeros(r);
  const sStripped = removeLeadingZeros(s);
  const rEncoded =
    rStripped[0] & 0x80 ? new Uint8Array([0, ...rStripped]) : rStripped;
  const sEncoded =
    sStripped[0] & 0x80 ? new Uint8Array([0, ...sStripped]) : sStripped;

  const totalLength = rEncoded.length + sEncoded.length + 4;
  const derSignature = new Uint8Array(totalLength + 2);
  derSignature[0] = 0x30;
  derSignature[1] = totalLength;

  derSignature[2] = 0x02;
  derSignature[3] = rEncoded.length;
  derSignature.set(rEncoded, 4);

  derSignature[4 + rEncoded.length] = 0x02;
  derSignature[5 + rEncoded.length] = sEncoded.length;
  derSignature.set(sEncoded, 6 + rEncoded.length);

  return derSignature;
}

export async function getOrCreateKeyPair() {
  // Check if keys exist in localStorage
  const existingPublicKey = localStorage.getItem('device_public_key');
  const existingPrivateKey = localStorage.getItem('device_private_key');

  // If both keys exist, return them
  if (existingPublicKey && existingPrivateKey) {
    return {
      publicKey: existingPublicKey,
      privateKey: existingPrivateKey,
    };
  }

  // Generate new key pair
  const keyPair = await window.crypto.subtle.generateKey(
    { name: 'ECDSA', namedCurve: 'P-256' },
    true,
    ['sign', 'verify']
  );

  // Export the public key
  const publicKeyBuffer = await window.crypto.subtle.exportKey(
    'spki',
    keyPair.publicKey
  );
  const publicKeyBase64 = bufferToBase64(publicKeyBuffer);

  // Export the private key
  const privateKeyBuffer = await window.crypto.subtle.exportKey(
    'pkcs8',
    keyPair.privateKey
  );
  const privateKeyBase64 = bufferToBase64(privateKeyBuffer);

  // Store the keys
  localStorage.setItem('device_public_key', publicKeyBase64);
  localStorage.setItem('device_private_key', privateKeyBase64);

  return {
    publicKey: publicKeyBase64,
    privateKey: privateKeyBase64,
  };
}
