import React, { useState } from "react";
import { But<PERSON> } from "./ui/button";
import { Card, CardContent } from "./ui/card";
import { Input } from "./ui/input";
import { Label } from "./ui/label";

interface RSVPFormProps {
  onSubmit: (data: { fullname: string; email: string; mobile: string }) => void;
  onCancel: () => void;
}

export const RSVPForm: React.FC<RSVPFormProps> = ({ onSubmit, onCancel }) => {
  const [formData, setFormData] = useState({
    fullname: "",
    email: "",
    mobile: "",
  });

  const [errors, setErrors] = useState({
    fullname: "",
    email: "",
    mobile: "",
  });

  const validateForm = () => {
    const newErrors = {
      fullname: "",
      email: "",
      mobile: "",
    };

    if (!formData.fullname.trim()) {
      newErrors.fullname = "Full name is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email";
    }

    if (!formData.mobile.trim()) {
      newErrors.mobile = "Mobile number is required";
    } else if (!/^\+?[\d\s-()]+$/.test(formData.mobile)) {
      newErrors.mobile = "Please enter a valid mobile number";
    }

    setErrors(newErrors);
    return !Object.values(newErrors).some((error) => error !== "");
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  const handleInputChange =
    (field: keyof typeof formData) =>
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setFormData((prev) => ({
        ...prev,
        [field]: e.target.value,
      }));

      // Clear error when user starts typing
      if (errors[field]) {
        setErrors((prev) => ({
          ...prev,
          [field]: "",
        }));
      }
    };

  return (
    <Card className="absolute w-[424px] h-[476px] top-[468px] left-[268px] bg-white rounded-2xl overflow-hidden shadow-EVENTPARK-SHADOWS-SM">
      <CardContent className="p-5">
        <h2 className="font-semibold text-black text-2xl tracking-[-0.72px] leading-6 mb-6">
          Fill in your Details
        </h2>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label
                htmlFor="fullname"
                className="text-sm font-medium text-[#808080]"
              >
                Full Name
              </Label>
              <Input
                id="fullname"
                type="text"
                value={formData.fullname}
                onChange={handleInputChange("fullname")}
                className={`w-full px-3.5 py-3 rounded-xl border-[0.8px] ${
                  errors.fullname
                    ? "border-red-500 focus:border-red-500"
                    : "border-[#e6e6e6] focus:border-[#4d55f2]"
                } bg-[linear-gradient(180deg,rgba(250,250,250,1)_0%,rgba(255,255,255,1)_100%)] text-black placeholder:text-[#999999]`}
                placeholder="Enter your full name"
              />
              {errors.fullname && (
                <p className="text-red-500 text-xs mt-1">{errors.fullname}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="email"
                className="text-sm font-medium text-[#808080]"
              >
                Email Address
              </Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange("email")}
                className={`w-full px-3.5 py-3 rounded-xl border-[0.8px] ${
                  errors.email
                    ? "border-red-500 focus:border-red-500"
                    : "border-[#e6e6e6] focus:border-[#4d55f2]"
                } bg-[linear-gradient(180deg,rgba(250,250,250,1)_0%,rgba(255,255,255,1)_100%)] text-black placeholder:text-[#999999]`}
                placeholder="Enter your email address"
              />
              {errors.email && (
                <p className="text-red-500 text-xs mt-1">{errors.email}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="mobile"
                className="text-sm font-medium text-[#808080]"
              >
                Mobile Number
              </Label>
              <Input
                id="mobile"
                inputMode="numeric"
                pattern="[0-9]*"
                type="number"
                value={formData.mobile}
                onChange={handleInputChange("mobile")}
                className={`w-full px-3.5 py-3 rounded-xl border-[0.8px] no-spinner ${
                  errors.mobile
                    ? "border-red-500 focus:border-red-500"
                    : "border-[#e6e6e6] focus:border-[#4d55f2]"
                } bg-[linear-gradient(180deg,rgba(250,250,250,1)_0%,rgba(255,255,255,1)_100%)] text-black placeholder:text-[#999999]`}
                placeholder="Enter your mobile number"
              />
              {errors.mobile && (
                <p className="text-red-500 text-xs mt-1">{errors.mobile}</p>
              )}
            </div>
          </div>

          <div className="flex flex-col w-full items-start gap-4 mt-8">
            <Button
              type="submit"
              className="relative self-stretch w-full px-7 py-3.5 h-[56px] bg-[#4d55f2] rounded-[64px] text-white text-lg leading-7 font-semibold hover:bg-[#3d45d2] transition-colors"
            >
              Continue
            </Button>

            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              className="relative self-stretch w-full px-7 py-3.5 h-[56px] bg-[#fff5f5] rounded-[64px] text-[#990000] text-lg leading-7 font-semibold border border-solid shadow-shadow-xs hover:bg-[#ffe5e5] transition-colors"
            >
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
