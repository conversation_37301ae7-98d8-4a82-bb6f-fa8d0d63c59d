import { useMutation } from '@tanstack/react-query';
import { AuthServices } from '../services/auth';
import { useUserAuthStore } from '../store/auth';
import { toolStatusEmitter } from './useToolStatus';

/**
 * Hook for refreshing tool status after actions that might change it
 * Use this after creating gift registries, guest lists, etc.
 */
export const useToolStatusRefresh = () => {
  const { setToolStatus } = useUserAuthStore();

  const refreshMutation = useMutation({
    mutationFn: async () => {
      const response = await AuthServices.getToolStatus();
      return response.data;
    },
    onSuccess: (data) => {
      if (data) {
        setToolStatus(data);
        // Broadcast to all components that tool status has been updated
        toolStatusEmitter.emit();
      }
    },
    onError: (error) => {
      console.error('Failed to refresh tool status:', error);
    },
  });

  return {
    refreshToolStatus: refreshMutation.mutate,
    isRefreshing: refreshMutation.isPending,
    refreshError: refreshMutation.error,
  };
};
