import { useEffect, useRef, useState } from 'react';
import { ArrowCircleRight2, Eye, EyeSlash } from 'iconsax-react';
import { AuthServices } from '../../lib/services/auth';
import { toast } from 'react-toastify';

interface ChangePasswordProps {
  onSave: (currentPassword: string) => void;
  onClose: () => void;
}

export const ChangePassword = ({ onClose, onSave }: ChangePasswordProps) => {
  const [currentPassword, setCurrentPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  const handleSave = async () => {
    if (!currentPassword) {
      setError('Current password is required');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Validate the current password
      await AuthServices.validateUserPassword({
        password: currentPassword,
      });

      // If validation is successful, proceed to the next step
      onSave(currentPassword);
    } catch (err: unknown) {
      const errorMessage =
        (err as { response?: { data?: { message?: string } } })?.response?.data
          ?.message || 'Invalid password. Please try again.';
      setError(errorMessage);
      toast.error(errorMessage);

      // Clear the password input for retry
      setCurrentPassword('');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      ref={modalRef}
      className="relative mt-10 bg-white rounded-2xl font-rethink shadow-[0px_12px_120px_0px_#5F5F5F0F] w-full p-4 z-50">
      <div className="mb-6">
        <h3 className="text-sm font-medium mb-4.5 tracking-[0.12em] text-primary">
          PASSWORD & SECURITY
        </h3>
        <h2 className="text-2xl font-medium mb-1.5">Change Password</h2>
        <p className="text-grey-950 text-base">
          Update your password to stay secure.
        </p>
      </div>
      <div className="flex gap-1 mb-6 w-[81px] bg-cus-pink-700 rounded-full">
        <div className="h-[8px] w-10 bg-primary-750 rounded-full"></div>
        <div className="h-[8px] w-10 bg-cus-pink-700 rounded-full"></div>
      </div>
      <div className="mb-5 relative">
        <label className="text-grey-500 text-sm font-medium mb-1.5 block">
          Current Password
        </label>
        <div className="relative">
          <input
            type={showCurrentPassword ? 'text' : 'password'}
            value={currentPassword}
            onChange={(e) => setCurrentPassword(e.target.value)}
            placeholder="Enter current password"
            className="w-full px-3 py-2 border border-stroke-gray-300 focus-within:outline-none focus-within:border-primary-main rounded-[64px] h-[44px] focus:outline-none focus:ring-transparent"
          />
          <button
            type="button"
            onClick={() => setShowCurrentPassword(!showCurrentPassword)}
            className="absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer">
            {showCurrentPassword ? (
              <Eye size="20" color="#B3B3B3" variant="Bulk" />
            ) : (
              <EyeSlash size="20" color="#B3B3B3" variant="Bulk" />
            )}
          </button>
        </div>
      </div>
      {error && <p className="text-red-500 text-sm mb-4">{error}</p>}

      <button
        onClick={handleSave}
        disabled={!currentPassword || isLoading}
        className={`w-fit flex items-center gap-2 ${
          !currentPassword || isLoading
            ? 'opacity-40 cursor-not-allowed '
            : 'cursor-pointer'
        } bg-primary text-white py-2 px-3.5 rounded-full text-sm font-semibold`}>
        {isLoading ? (
          <>
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            Validating...
          </>
        ) : (
          <>
            Continue <ArrowCircleRight2 size={20} color="#fff" variant="Bulk" />
          </>
        )}
      </button>
    </div>
  );
};
