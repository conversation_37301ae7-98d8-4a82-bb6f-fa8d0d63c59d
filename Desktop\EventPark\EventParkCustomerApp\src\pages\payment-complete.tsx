import React, { useState, useEffect } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { toast } from "react-toastify";
import { WalletAPI, Transaction } from "../lib/apis/walletapi";
import { ClipLoader } from "react-spinners";

const PaymentComplete: React.FC = () => {
  const navigate = useNavigate();
  const { transactionId } = useParams<{ transactionId: string }>();
  const [searchParams] = useSearchParams();

  console.log(transactionId);
  // const [isConfirming, setIsConfirming] = useState(false);
  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check for error parameter in URL - if present, payment failed regardless of API response
  const urlError = searchParams.get("error");
  const hasPaymentError = !!urlError;

  // Fetch transaction details on component mount
  useEffect(() => {
    const fetchTransactionDetails = async () => {
      // Check for URL error parameter first - if present, payment failed immediately
      if (hasPaymentError) {
        console.log("🚨 Payment failed due to URL error parameter:", urlError);
        setError(
          `Payment failed: ${decodeURIComponent(
            urlError || "An error occurred"
          )}`
        );
        setIsLoading(false);
        toast.error(
          `Payment failed: ${decodeURIComponent(
            urlError || "An error occurred"
          )}`
        );
        return;
      }

      if (!transactionId) {
        setError("Transaction ID not found");
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const transactionData = await WalletAPI.getTransactionById(
          transactionId
        );
        setTransaction(transactionData);
        setError(null);
      } catch (err: unknown) {
        console.error("Failed to fetch transaction details:", err);
        setError("Failed to load transaction details");
        toast.error("Failed to load transaction details");
      } finally {
        setIsLoading(false);
      }
    };

    fetchTransactionDetails();
  }, [transactionId, hasPaymentError, urlError]);

  // const handlePaymentConfirmed = async () => {
  //   setIsConfirming(true);
  //   try {
  //     // Refresh wallet data to get updated balance
  //     await WalletAPI.getUserWallets();

  //     // Show success message
  //     toast.success("Payment confirmed successfully!");

  //     // Navigate back to wallet after a short delay
  //     setTimeout(() => {
  //       navigate("/wallet");
  //     }, 1000);
  //   } catch (error: unknown) {
  //     console.log(error);
  //     toast.error("Failed to confirm payment");
  //   } finally {
  //     setIsConfirming(false);
  //   }
  // };

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen mt-[100px] bg-gradient-to-b from-[#FEFAF8] via-[#F5F6FE] to-[#F5F9FF] flex items-center justify-center p-4">
        <div className="bg-white rounded-3xl shadow-xl max-w-md w-full p-8 text-center">
          <div className="flex justify-center mb-6">
            <ClipLoader size={60} color="#4D55F2" />
          </div>
          <h1 className="text-2xl font-semibold text-gray-900 mb-2">
            Loading Transaction Details
          </h1>
          <p className="text-gray-600">
            Please wait while we fetch your transaction information...
          </p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error || !transaction) {
    return (
      <div className="min-h-screen mt-[100px] bg-gradient-to-b from-[#FEFAF8] via-[#F5F6FE] to-[#F5F9FF] flex items-center justify-center p-4">
        <div className="bg-white rounded-3xl shadow-xl max-w-md w-full p-8 text-center">
          <div className="flex justify-center mb-6">
            <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center">
              <svg
                width="40"
                height="40"
                viewBox="0 0 40 40"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M20 36.6667C29.2048 36.6667 36.6667 29.2048 36.6667 20C36.6667 10.7952 29.2048 3.33334 20 3.33334C10.7952 3.33334 3.33334 10.7952 3.33334 20C3.33334 29.2048 10.7952 36.6667 20 36.6667Z"
                  fill="#EF4444"
                />
                <path
                  d="M25 15L15 25M15 15L25 25"
                  stroke="white"
                  strokeWidth="2.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
          </div>
          <h1 className="text-2xl font-semibold text-gray-900 mb-2">
            {hasPaymentError ? "Payment Failed" : "Error Loading Transaction"}
          </h1>
          <p className="text-gray-600 mb-8">
            {error || "Transaction not found"}
          </p>
          <button
            onClick={() => navigate("/wallet")}
            className="w-full py-3 px-6 rounded-full font-semibold text-white bg-[#4D55F2] hover:bg-[#343CD8] transition-all"
          >
            Back to Wallet
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen mt-[100px] bg-gradient-to-b from-[#FEFAF8] via-[#F5F6FE] to-[#F5F9FF] flex items-center justify-center p-4">
      <div className="bg-white rounded-3xl shadow-xl max-w-md w-full p-8 text-center">
        {/* Success Icon */}
        <div className="flex justify-center mb-6">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
            <svg
              width="40"
              height="40"
              viewBox="0 0 40 40"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M33.3333 20C33.3333 27.3638 27.3638 33.3333 20 33.3333C12.6362 33.3333 6.66667 27.3638 6.66667 20C6.66667 12.6362 12.6362 6.66667 20 6.66667C27.3638 6.66667 33.3333 12.6362 33.3333 20Z"
                fill="#22C55E"
              />
              <path
                d="M26.6667 15L17.5 24.1667L13.3333 20"
                stroke="white"
                strokeWidth="2.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </div>

        {/* Title */}
        <h1 className="text-2xl font-semibold text-gray-900 mb-2">
          Payment Complete
        </h1>

        {/* Description */}
        <p className="text-gray-600 mb-6">
          Your payment has been processed successfully.
        </p>

        {/* Transaction Details */}
        <div className="bg-gray-50 rounded-2xl p-6 mb-8 text-left">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Transaction Details
          </h3>

          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Amount:</span>
              <span className="font-semibold text-gray-900">
                ₦{parseFloat(transaction.amount).toLocaleString()}
              </span>
            </div>

            <div className="flex justify-between">
              <span className="text-gray-600">Fee:</span>
              <span className="font-semibold text-gray-900">
                ₦{parseFloat(transaction.fee).toLocaleString()}
              </span>
            </div>

            <div className="flex justify-between">
              <span className="text-gray-600">Channel:</span>
              <span className="font-semibold text-gray-900 capitalize">
                {transaction.channel}
              </span>
            </div>

            <div className="flex justify-between">
              <span className="text-gray-600">Status:</span>
              <span
                className={`font-semibold capitalize ${
                  transaction.status === "completed"
                    ? "text-green-600"
                    : transaction.status === "pending"
                    ? "text-yellow-600"
                    : "text-gray-900"
                }`}
              >
                {transaction.status}
              </span>
            </div>

            <div className="flex justify-between">
              <span className="text-gray-600">
                Transaction <br /> Reference:
              </span>
              <span className="font-mono text-sm text-gray-900">
                {transaction.reference}
              </span>
            </div>

            {transaction.description && (
              <div className="flex justify-between">
                <span className="text-gray-600">Description:</span>
                <span className="font-semibold text-gray-900">
                  {transaction.description}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Confirm Button */}
        {/* <button
          onClick={handlePaymentConfirmed}
          disabled={isConfirming}
          className={`w-full py-3 px-6 rounded-full font-semibold text-white transition-all ${
            isConfirming
              ? "bg-gray-400 cursor-not-allowed"
              : "bg-[#4D55F2] hover:bg-[#343CD8]"
          }`}
        >
          {isConfirming ? "Confirming..." : "Payment Confirmed"}
        </button> */}

        {/* Back to Wallet Link */}
        <button
          onClick={() => navigate("/wallet")}
          className="mt-4 text-[#4D55F2] hover:text-[#343CD8] font-medium transition-colors"
        >
          Back to Wallet
        </button>
      </div>
    </div>
  );
};

export default PaymentComplete;
