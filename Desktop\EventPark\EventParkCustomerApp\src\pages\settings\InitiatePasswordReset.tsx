/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useRef } from 'react';
import { useMutation } from '@tanstack/react-query';
import { AuthServices } from '../../lib/services/auth';
import { toast } from 'react-toastify';

interface InitiatePasswordResetProps {
  email: string;
  onInitiated: () => void;
  onClose: () => void;
}

export const InitiatePasswordReset = ({
  email,
  onInitiated,
  onClose,
}: InitiatePasswordResetProps) => {
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  const initiateMutation = useMutation({
    mutationFn: () =>
      AuthServices.passwordResetInitiate({
        email: email,
      }),
    onSuccess: (data) => {
      const expiresAt = data?.data?.expires_at;
      if (expiresAt) {
        localStorage.setItem('reset_expiry', expiresAt);
      }
      toast.success('Verification code sent to your email');
      onInitiated();
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || error?.message);
    },
  });

  const handleSendCode = () => {
    initiateMutation.mutate();
  };

  return (
    <div
      ref={modalRef}
      className="relative mt-10 bg-white rounded-2xl font-rethink shadow-[0px_12px_120px_0px_#5F5F5F0F] w-full p-4 z-50">
      <div className="mb-6">
        <h3 className="text-sm font-medium mb-4.5 tracking-[0.12em] text-primary">
          PASSWORD & SECURITY
        </h3>
        <h2 className="text-2xl font-medium mb-1.5">Set Password</h2>
        <p className="text-grey-950 text-base">
          We'll send a verification code to your email to set up your password.
        </p>
      </div>
      <div className="flex gap-0 w-[81px] bg-gray-200 rounded-full">
        <div className="h-[8px] bg-primary-750 rounded-full w-1/3"></div>
      </div>

      <div className="mt-6 p-4 bg-grey-850 rounded-xl">
        <p className="text-grey-500 text-sm mb-2">
          We'll send verification code to:
        </p>
        <p className="text-grey-50 font-medium">{email}</p>
      </div>

      {initiateMutation.isPending && (
        <div className="mt-4 flex items-center gap-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          <span className="text-sm text-grey-500">
            Sending verification code...
          </span>
        </div>
      )}

      {initiateMutation.isError && (
        <div className="mt-4 mb-4">
          <p className="text-red-500 text-sm mb-2">
            Failed to send verification code. Please try again.
          </p>
        </div>
      )}

      {!initiateMutation.isSuccess && (
        <button
          onClick={handleSendCode}
          disabled={initiateMutation.isPending}
          className={`w-fit mt-4 text-white py-2 px-3.5 rounded-full text-sm font-semibold ${
            initiateMutation.isPending
              ? 'bg-primary/60 cursor-not-allowed'
              : 'bg-primary cursor-pointer'
          }`}>
          {initiateMutation.isPending ? 'Sending...' : 'Send Verification Code'}
        </button>
      )}
    </div>
  );
};
